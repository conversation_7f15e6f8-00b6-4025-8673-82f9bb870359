<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('reservations', function (Blueprint $table) {
            $table->string('offer_slug')->nullable()->after('status');
            $table->string('offer_title')->nullable()->after('offer_slug');
            $table->string('contact_phone')->nullable()->after('offer_title');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('reservations', function (Blueprint $table) {
            $table->dropColumn(['offer_slug', 'offer_title', 'contact_phone']);
        });
    }
};
