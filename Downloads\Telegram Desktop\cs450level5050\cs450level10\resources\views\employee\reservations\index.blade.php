@extends('employee.layouts.app')

@section('title', 'إدارة الحجوزات - نظام إدارة المطعم')

@section('page-title', 'إدارة الحجوزات')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- هيدر الصفحة المبدع -->
    <div class="mb-8 p-6 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl border border-purple-200 dark:border-purple-800">
        <div class="flex flex-wrap items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gradient-luxury mb-2">إدارة الحجوزات</h1>
                <p class="text-purple-600 dark:text-purple-400 flex items-center">
                    <i class="fas fa-calendar-check mr-2"></i>
                    عرض وإدارة حجوزات الطاولات والمواعيد
                </p>
                <div class="w-20 h-1 bg-gradient-luxury rounded-full mt-2"></div>
            </div>
            <div class="mt-4 sm:mt-0 flex items-center space-x-4 space-x-reverse">
                <div class="p-4 rounded-xl bg-gradient-luxury text-white shadow-luxury">
                    <i class="fas fa-calendar-check text-3xl"></i>
                </div>
                <a href="{{ route('employee.reservations.create') }}" class="btn-magical inline-flex items-center px-6 py-3 bg-gradient-luxury text-white font-bold rounded-xl shadow-luxury hover:shadow-xl transition-all duration-300 group">
                    <i class="fas fa-plus-circle mr-2 group-hover:rotate-90 transition-transform duration-300"></i>
                    إنشاء حجز جديد
                </a>
            </div>
        </div>
    </div>

    <!-- رسائل النجاح والخطأ المبدعة -->
    @if(session('success'))
    <div class="mb-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-2 border-green-200 dark:border-green-800 rounded-xl shadow-lg" role="alert">
        <div class="flex items-center">
            <div class="p-2 rounded-lg bg-gradient-forest text-white mr-3">
                <i class="fas fa-check-circle"></i>
            </div>
            <div>
                <h4 class="font-bold text-green-800 dark:text-green-400">نجح!</h4>
                <span class="text-green-700 dark:text-green-300">{{ session('success') }}</span>
            </div>
        </div>
    </div>
    @endif

    @if(session('error'))
    <div class="mb-6 p-4 bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 border-2 border-red-200 dark:border-red-800 rounded-xl shadow-lg" role="alert">
        <div class="flex items-center">
            <div class="p-2 rounded-lg bg-gradient-to-r from-red-500 to-pink-500 text-white mr-3">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div>
                <h4 class="font-bold text-red-800 dark:text-red-400">خطأ!</h4>
                <span class="text-red-700 dark:text-red-300">{{ session('error') }}</span>
            </div>
        </div>
    </div>
    @endif

    <!-- فلاتر البحث المبدعة -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl overflow-hidden mb-8 border border-gray-200 dark:border-gray-700">
        <div class="p-6 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border-b border-purple-200 dark:border-purple-800">
            <div class="flex items-center">
                <div class="p-2 rounded-lg bg-gradient-luxury text-white mr-3">
                    <i class="fas fa-filter"></i>
                </div>
                <div>
                    <h2 class="text-xl font-bold text-gradient-luxury">فلترة الحجوزات</h2>
                    <p class="text-sm text-purple-600 dark:text-purple-400">ابحث وفلتر الحجوزات حسب المعايير</p>
                </div>
            </div>
        </div>
        <div class="p-6">
            <form action="{{ route('employee.reservations') }}" method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-6">
                <!-- فلتر الحالة -->
                <div>
                    <label for="status" class="block text-sm font-bold text-purple-800 dark:text-purple-300 mb-2">
                        <i class="fas fa-info-circle mr-1"></i>
                        الحالة
                    </label>
                    <select id="status" name="status" class="w-full px-4 py-3 border-2 border-purple-200 dark:border-purple-600 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white transition-all duration-300">
                        <option value="">جميع الحالات</option>
                        <option value="confirmed" {{ request('status') == 'confirmed' ? 'selected' : '' }}>مؤكد</option>
                        <option value="canceled" {{ request('status') == 'canceled' ? 'selected' : '' }}>ملغي</option>
                        <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>مكتمل</option>
                    </select>
                </div>

                <!-- فلتر من تاريخ -->
                <div>
                    <label for="date_from" class="block text-sm font-bold text-purple-800 dark:text-purple-300 mb-2">
                        <i class="fas fa-calendar-alt mr-1"></i>
                        من تاريخ
                    </label>
                    <input type="date" id="date_from" name="date_from" value="{{ request('date_from') }}" class="w-full px-4 py-3 border-2 border-purple-200 dark:border-purple-600 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white transition-all duration-300">
                </div>

                <!-- فلتر إلى تاريخ -->
                <div>
                    <label for="date_to" class="block text-sm font-bold text-purple-800 dark:text-purple-300 mb-2">
                        <i class="fas fa-calendar-alt mr-1"></i>
                        إلى تاريخ
                    </label>
                    <input type="date" id="date_to" name="date_to" value="{{ request('date_to') }}" class="w-full px-4 py-3 border-2 border-purple-200 dark:border-purple-600 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white transition-all duration-300">
                </div>

                <!-- زر التطبيق -->
                <div class="flex items-end">
                    <button type="submit" class="btn-magical w-full px-6 py-3 bg-gradient-luxury text-white font-bold rounded-xl shadow-luxury hover:shadow-xl transition-all duration-300 group">
                        <i class="fas fa-filter mr-2 group-hover:rotate-12 transition-transform duration-300"></i>
                        تطبيق الفلتر
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول الحجوزات -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-bold text-gray-800 dark:text-white">قائمة الحجوزات</h2>
        </div>

        @if(count($reservations) > 0)
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            رقم الحجز
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            العميل
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الطاولة
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            وقت الحجز
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            المدة
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الحالة
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($reservations as $reservation)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {{ $reservation->reservation_id }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            @if(isset($reservation->user))
                            {{ $reservation->user->first_name }} {{ $reservation->user->last_name }}
                            <div class="text-xs text-gray-500 dark:text-gray-400">{{ $reservation->user->phone }}</div>
                            @else
                            <span class="text-gray-500 dark:text-gray-400">غير متوفر</span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            @if(isset($reservation->table))
                            طاولة رقم {{ $reservation->table->table_number }}
                            <div class="text-xs text-gray-500 dark:text-gray-400">{{ $reservation->table->capacity }} أشخاص</div>
                            @else
                            <span class="text-gray-500 dark:text-gray-400">غير متوفر</span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {{ \Carbon\Carbon::parse($reservation->reservation_time)->format('Y-m-d H:i') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {{ $reservation->duration }} دقيقة
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                {{ $reservation->status == 'confirmed' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' :
                                   ($reservation->status == 'canceled' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' :
                                   'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300') }}">
                                {{ $reservation->status == 'confirmed' ? 'مؤكد' :
                                   ($reservation->status == 'canceled' ? 'ملغي' :
                                   ($reservation->status == 'completed' ? 'مكتمل' : $reservation->status)) }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                            <div class="flex space-x-2 space-x-reverse">
                                <a href="{{ route('employee.reservations.edit', $reservation->reservation_id) }}" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>

                                @if($reservation->status == 'confirmed')
                                <form action="{{ route('employee.reservations.update-status', $reservation->reservation_id) }}" method="POST" class="inline-block">
                                    @csrf
                                    @method('PUT')
                                    <input type="hidden" name="status" value="completed">
                                    <button type="submit" class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300" title="إكمال الحجز">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </form>

                                <form action="{{ route('employee.reservations.update-status', $reservation->reservation_id) }}" method="POST" class="inline-block">
                                    @csrf
                                    @method('PUT')
                                    <input type="hidden" name="status" value="canceled">
                                    <button type="submit" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300" title="إلغاء الحجز">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </form>
                                @endif

                                <form action="{{ route('employee.reservations.delete', $reservation->reservation_id) }}" method="POST" class="inline-block" onsubmit="return confirm('هل أنت متأكد من حذف هذا الحجز؟');">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300" title="حذف">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            {{ $reservations->links() }}
        </div>
        @else
        <div class="text-center py-8">
            <div class="text-5xl text-gray-300 dark:text-gray-600 mb-4">
                <i class="fas fa-calendar-times"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-700 dark:text-gray-300 mb-2">لا توجد حجوزات</h3>
            <p class="text-gray-500 dark:text-gray-400 mb-4">لم يتم العثور على أي حجوزات تطابق معايير البحث</p>
            <a href="{{ route('employee.reservations.create') }}" class="inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50">
                <i class="fas fa-plus-circle ml-2"></i>
                إنشاء حجز جديد
            </a>
        </div>
        @endif
    </div>

    <!-- ملخص الحجوزات -->
    <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
            <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-bold text-gray-800 dark:text-white">الحجوزات اليوم</h2>
            </div>
            <div class="p-6 text-center">
                <div class="text-3xl font-bold text-primary mb-2">
                    {{ $reservations->where('reservation_time', '>=', \Carbon\Carbon::today())->where('reservation_time', '<', \Carbon\Carbon::tomorrow())->count() }}
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400">حجز</p>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
            <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-bold text-gray-800 dark:text-white">الحجوزات القادمة</h2>
            </div>
            <div class="p-6 text-center">
                <div class="text-3xl font-bold text-blue-500 mb-2">
                    {{ $reservations->where('reservation_time', '>=', \Carbon\Carbon::now())->where('status', 'confirmed')->count() }}
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400">حجز</p>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
            <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-bold text-gray-800 dark:text-white">الحجوزات المكتملة</h2>
            </div>
            <div class="p-6 text-center">
                <div class="text-3xl font-bold text-green-500 mb-2">
                    {{ $reservations->where('status', 'completed')->count() }}
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400">حجز</p>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
            <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-bold text-gray-800 dark:text-white">الحجوزات الملغاة</h2>
            </div>
            <div class="p-6 text-center">
                <div class="text-3xl font-bold text-red-500 mb-2">
                    {{ $reservations->where('status', 'canceled')->count() }}
                </div>
                <p class="text-sm text-gray-600 dark:text-gray-400">حجز</p>
            </div>
        </div>
    </div>
</div>
@endsection
