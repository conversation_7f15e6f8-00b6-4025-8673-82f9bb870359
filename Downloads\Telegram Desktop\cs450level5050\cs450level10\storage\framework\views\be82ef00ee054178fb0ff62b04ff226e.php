<!-- سكريبت الصفحة -->
<script src="https://cdn.jsdelivr.net/npm/apexcharts@3.27.3/dist/apexcharts.min.js"></script>
<script src="<?php echo e(asset('js/theme-manager.js')); ?>"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded');

    // تأخير بسيط للتأكد من تحميل جميع العناصر
    setTimeout(function() {
        console.log('Initializing scripts...');

        // إضافة تأثيرات بصرية للأزرار
        const buttons = document.querySelectorAll('.btn-magical');
        buttons.forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
            });

            button.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // إدارة القائمة الجانبية للموبايل
    const sidebarToggle = document.getElementById('sidebarToggle');
    const mobileMenu = document.getElementById('mobileMenu');
    const closeMobileMenu = document.getElementById('closeMobileMenu');

    if (sidebarToggle && mobileMenu) {
        sidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            mobileMenu.classList.remove('-translate-x-full');

            // إضافة تأثير بصري للأيقونة
            const icon = sidebarToggle.querySelector('i');
            if (icon) {
                icon.classList.add('animate-spin');
                setTimeout(() => {
                    icon.classList.remove('animate-spin');
                }, 300);
            }
        });
    }

    if (closeMobileMenu && mobileMenu) {
        closeMobileMenu.addEventListener('click', function(e) {
            e.preventDefault();
            mobileMenu.classList.add('-translate-x-full');
        });
    }

    // إغلاق القائمة الجانبية عند النقر خارجها
    if (mobileMenu) {
        document.addEventListener('click', function(event) {
            if (!mobileMenu.contains(event.target) && !sidebarToggle.contains(event.target)) {
                mobileMenu.classList.add('-translate-x-full');
            }
        });
    }

    // إدارة قائمة المستخدم
    const userMenuBtn = document.getElementById('userMenuBtn');
    const userMenu = document.getElementById('userMenu');

    console.log('userMenuBtn:', userMenuBtn);
    console.log('userMenu:', userMenu);

    if (userMenuBtn && userMenu) {
        userMenuBtn.addEventListener('click', function(e) {
            console.log('User menu button clicked');
            e.preventDefault();
            e.stopPropagation();
            userMenu.classList.toggle('hidden');

            // إضافة تأثير بصري للسهم
            const arrow = userMenuBtn.querySelector('.fa-chevron-down');
            if (arrow) {
                arrow.classList.toggle('rotate-180');
            }
        });
    } else {
        console.error('User menu elements not found');
    }

    // إغلاق قائمة المستخدم عند النقر خارجها
    document.addEventListener('click', function(event) {
        if (userMenuBtn && userMenu && !userMenuBtn.contains(event.target) && !userMenu.contains(event.target)) {
            userMenu.classList.add('hidden');
            // إعادة السهم لوضعه الطبيعي
            const arrow = userMenuBtn.querySelector('.fa-chevron-down');
            if (arrow) {
                arrow.classList.remove('rotate-180');
            }
        }
    });

    // إدارة سلة الطلبات
    const cartButton = document.getElementById('cartButton');
    const cartMenu = document.getElementById('cartMenu');

    console.log('cartButton:', cartButton);
    console.log('cartMenu:', cartMenu);

    if (cartButton && cartMenu) {
        // فتح/إغلاق قائمة سلة الطلبات عند النقر على الزر
        cartButton.addEventListener('click', function(e) {
            console.log('Cart button clicked');
            e.preventDefault();
            e.stopPropagation();
            cartMenu.classList.toggle('hidden');

            // إضافة تأثير بصري للسلة
            const cart = cartButton.querySelector('.fa-shopping-cart');
            if (cart) {
                cart.classList.add('animate-bounce');
                setTimeout(() => {
                    cart.classList.remove('animate-bounce');
                }, 500);
            }

            // تحديث محتوى السلة عند فتحها
            if (!cartMenu.classList.contains('hidden')) {
                loadCartItems();
            }
        });

        // إغلاق قائمة سلة الطلبات عند النقر خارجها
        document.addEventListener('click', function(event) {
            if (!cartButton.contains(event.target) && !cartMenu.contains(event.target)) {
                cartMenu.classList.add('hidden');
            }
        });
    }

    // دالة تحميل عناصر السلة
    function loadCartItems() {
        const cartItemsList = document.getElementById('cartItemsList');

        // استرجاع عناصر السلة من التخزين المحلي
        const cartItems = JSON.parse(localStorage.getItem('cartItems')) || [];

        if (cartItems.length === 0) {
            cartItemsList.innerHTML = `
                <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                    <p>لا توجد عناصر في السلة</p>
                </div>
            `;
            return;
        }

        // عرض عناصر السلة
        let cartHtml = '';
        let totalAmount = 0;

        cartItems.forEach(item => {
            const itemTotal = item.price * item.quantity;
            totalAmount += itemTotal;

            cartHtml += `
                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex justify-between items-start">
                        <div>
                            <h4 class="font-medium text-gray-800 dark:text-white">${item.name}</h4>
                            <div class="flex items-center mt-1">
                                <span class="text-sm text-gray-500 dark:text-gray-400">الكمية: ${item.quantity}</span>
                                <span class="mx-2 text-gray-300 dark:text-gray-600">|</span>
                                <span class="text-sm text-gray-500 dark:text-gray-400">${item.price.toFixed(2)} د.ل</span>
                            </div>
                        </div>
                        <button class="text-red-500 hover:text-red-700" onclick="removeCartItem('${item.id}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;
        });

        // إضافة المجموع وزر إنشاء طلب
        cartHtml += `
            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                <div class="flex justify-between items-center">
                    <span class="font-bold text-gray-800 dark:text-white">المجموع:</span>
                    <span class="font-bold text-primary">${totalAmount.toFixed(2)} د.ل</span>
                </div>
            </div>
            <div class="px-4 py-3">
                <a href="<?php echo e(route('employee.orders.create')); ?>" class="block w-full bg-primary hover:bg-primary/90 text-white text-center py-2 px-4 rounded-md">
                    إنشاء طلب
                </a>
            </div>
        `;

        cartItemsList.innerHTML = cartHtml;
    }

    // دالة إزالة عنصر من السلة
    window.removeCartItem = function(itemId) {
        let cartItems = JSON.parse(localStorage.getItem('cartItems')) || [];
        cartItems = cartItems.filter(item => item.id !== itemId);
        localStorage.setItem('cartItems', JSON.stringify(cartItems));

        // تحديث عدد العناصر في السلة
        updateCartCount();

        // تحديث محتوى السلة
        loadCartItems();
    }

    // دالة تحديث عدد العناصر في السلة
    function updateCartCount() {
        const cartItems = JSON.parse(localStorage.getItem('cartItems')) || [];
        const cartCountElements = document.querySelectorAll('.cart-count');
        const previousCount = parseInt(cartCountElements[0]?.textContent || '0');

        cartCountElements.forEach(element => {
            element.textContent = cartItems.length;

            // إضافة تأثير بصري عند تغيير العدد
            if (cartItems.length !== previousCount) {
                element.classList.add('animate-pulse', 'bg-green-500');
                setTimeout(() => {
                    element.classList.remove('animate-pulse');
                }, 1000);
            }

            // إظهار/إخفاء العداد
            if (cartItems.length > 0) {
                element.classList.remove('hidden');
            } else {
                element.classList.add('hidden');
            }
        });
    }

    // تحديث عدد العناصر في السلة عند تحميل الصفحة
    updateCartCount();

    // دالة إضافة عنصر إلى السلة
    window.addToCart = function(itemId, itemName, itemPrice) {
        let cartItems = JSON.parse(localStorage.getItem('cartItems')) || [];

        // البحث عن العنصر في السلة
        const existingItem = cartItems.find(item => item.id === itemId);

        if (existingItem) {
            // زيادة الكمية إذا كان العنصر موجود
            existingItem.quantity += 1;
        } else {
            // إضافة عنصر جديد
            cartItems.push({
                id: itemId,
                name: itemName,
                price: parseFloat(itemPrice),
                quantity: 1
            });
        }

        // حفظ السلة المحدثة
        localStorage.setItem('cartItems', JSON.stringify(cartItems));

        // تحديث عدد العناصر
        updateCartCount();

        // إظهار رسالة نجاح
        showNotification('تم إضافة العنصر إلى السلة', 'success');

        // تأثير بصري على زر السلة
        const cartButton = document.getElementById('cartButton');
        if (cartButton) {
            const cart = cartButton.querySelector('.fa-shopping-cart');
            if (cart) {
                cart.classList.add('animate-bounce', 'text-green-500');
                setTimeout(() => {
                    cart.classList.remove('animate-bounce', 'text-green-500');
                }, 500);
            }
        }
    };

    // دالة إظهار الإشعارات
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-md shadow-lg transition-all duration-300 transform translate-x-full`;

        const bgColor = type === 'success' ? 'bg-green-500' :
                       type === 'error' ? 'bg-red-500' :
                       type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500';

        notification.classList.add(bgColor, 'text-white');
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas ${type === 'success' ? 'fa-check' : type === 'error' ? 'fa-times' : type === 'warning' ? 'fa-exclamation' : 'fa-info'} ml-2"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // إظهار الإشعار
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // إخفاء الإشعار بعد 3 ثوان
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // دالة إضافة عنصر إلى السلة
    window.addToCart = function(itemId, itemName, itemPrice) {
        let cartItems = JSON.parse(localStorage.getItem('cartItems')) || [];

        // البحث عن العنصر في السلة
        const existingItem = cartItems.find(item => item.id === itemId);

        if (existingItem) {
            // زيادة الكمية إذا كان العنصر موجود
            existingItem.quantity += 1;
        } else {
            // إضافة عنصر جديد
            cartItems.push({
                id: itemId,
                name: itemName,
                price: parseFloat(itemPrice),
                quantity: 1
            });
        }

        // حفظ السلة
        localStorage.setItem('cartItems', JSON.stringify(cartItems));

        // تحديث عدد العناصر
        updateCartCount();

        // إظهار رسالة نجاح
        showCartNotification('تم إضافة العنصر إلى السلة');
    };

    // دالة إظهار إشعار السلة
    function showCartNotification(message) {
        // إنشاء عنصر الإشعار
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg z-50 transform translate-x-full transition-transform duration-300';
        notification.textContent = message;

        document.body.appendChild(notification);

        // إظهار الإشعار
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);

        // إخفاء الإشعار بعد 3 ثوان
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // إدارة الإشعارات
    const notificationBtn = document.getElementById('notificationBtn');
    const notificationsMenu = document.getElementById('notificationsMenu');
    const notificationsList = document.getElementById('notificationsList');
    const notificationCount = document.getElementById('notificationCount');

    if (notificationBtn && notificationsMenu) {
        console.log('Notification elements found');
        // فتح/إغلاق قائمة الإشعارات
        notificationBtn.addEventListener('click', function(e) {
            console.log('Notification button clicked');
            e.preventDefault();
            e.stopPropagation();
            notificationsMenu.classList.toggle('hidden');

            // إضافة تأثير بصري للجرس
            const bell = notificationBtn.querySelector('.fa-bell');
            if (bell) {
                bell.classList.add('animate-pulse');
                setTimeout(() => {
                    bell.classList.remove('animate-pulse');
                }, 300);
            }

            // تحميل الإشعارات عند فتح القائمة
            if (!notificationsMenu.classList.contains('hidden')) {
                console.log('Loading notifications...');
                loadNotifications();
            }
        });

        // إغلاق قائمة الإشعارات عند النقر خارجها
        document.addEventListener('click', function(event) {
            if (!notificationBtn.contains(event.target) && !notificationsMenu.contains(event.target)) {
                notificationsMenu.classList.add('hidden');
            }
        });
    }

    // دالة تحميل الإشعارات
    function loadNotifications() {
        console.log('loadNotifications called');
        if (!notificationsList) {
            console.log('notificationsList not found');
            return;
        }

        notificationsList.innerHTML = '<div class="text-center py-4 text-gray-500 dark:text-gray-400"><p>جاري تحميل الإشعارات...</p></div>';

        console.log('Fetching notifications from /employee/api/notifications/latest');
        fetch('/employee/api/notifications/latest')
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Notifications data:', data);
                displayNotifications(data.notifications || []);
            })
            .catch(error => {
                console.error('Error loading notifications:', error);
                notificationsList.innerHTML = '<div class="text-center py-4 text-red-500"><p>حدث خطأ في تحميل الإشعارات</p></div>';
            });
    }

    // دالة عرض الإشعارات
    function displayNotifications(notifications) {
        if (!notificationsList) return;

        if (notifications.length === 0) {
            notificationsList.innerHTML = '<div class="text-center py-4 text-gray-500 dark:text-gray-400"><p>لا توجد إشعارات جديدة</p></div>';
            return;
        }

        let html = '';
        notifications.forEach(notification => {
            const icon = getNotificationIcon(notification.type);
            const timeAgo = getTimeAgo(notification.created_at);

            html += `
                <div class="px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700/30 border-b border-gray-100 dark:border-gray-700 last:border-b-0 cursor-pointer notification-item ${notification.is_read ? 'read' : 'unread'}"
                     onclick="handleNotificationClick(${notification.notification_id}, '${notification.action_url || ''}')">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 ml-3">
                            <div class="h-8 w-8 rounded-full ${icon.bgColor} flex items-center justify-center ${icon.textColor}">
                                <i class="${icon.class}"></i>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                    ${notification.title || 'إشعار جديد'}
                                </p>
                                ${!notification.is_read ? '<div class="w-2 h-2 bg-primary rounded-full"></div>' : ''}
                            </div>
                            <p class="text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
                                ${notification.message}
                            </p>
                            <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                                ${timeAgo}
                            </p>
                        </div>
                    </div>
                </div>
            `;
        });

        notificationsList.innerHTML = html;
    }

    // دالة الحصول على أيقونة الإشعار
    function getNotificationIcon(type) {
        const icons = {
            'order': {
                class: 'fas fa-shopping-cart',
                bgColor: 'bg-blue-100 dark:bg-blue-900/30',
                textColor: 'text-blue-500 dark:text-blue-300'
            },
            'reservation': {
                class: 'fas fa-calendar-check',
                bgColor: 'bg-green-100 dark:bg-green-900/30',
                textColor: 'text-green-500 dark:text-green-300'
            },
            'inventory': {
                class: 'fas fa-box',
                bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',
                textColor: 'text-yellow-500 dark:text-yellow-300'
            },
            'system': {
                class: 'fas fa-cog',
                bgColor: 'bg-purple-100 dark:bg-purple-900/30',
                textColor: 'text-purple-500 dark:text-purple-300'
            },
            'default': {
                class: 'fas fa-bell',
                bgColor: 'bg-gray-100 dark:bg-gray-700',
                textColor: 'text-gray-500 dark:text-gray-300'
            }
        };

        return icons[type] || icons['default'];
    }

    // دالة حساب الوقت المنقضي
    function getTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) {
            return 'الآن';
        } else if (diffInSeconds < 3600) {
            const minutes = Math.floor(diffInSeconds / 60);
            return `منذ ${minutes} دقيقة`;
        } else if (diffInSeconds < 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return `منذ ${hours} ساعة`;
        } else {
            const days = Math.floor(diffInSeconds / 86400);
            return `منذ ${days} يوم`;
        }
    }

    // دالة تحديث عدد الإشعارات
    function updateNotificationCount() {
        fetch('/employee/api/notifications/count')
            .then(response => response.json())
            .then(data => {
                const count = data.count || 0;
                const previousCount = parseInt(notificationCount?.textContent || '0');

                if (notificationCount) {
                    notificationCount.textContent = count;
                    if (count > 0) {
                        notificationCount.classList.remove('hidden');

                        // إضافة تأثير بصري عند وجود إشعارات جديدة
                        if (count > previousCount) {
                            notificationCount.classList.add('animate-pulse', 'bg-red-500');
                            const bell = notificationBtn?.querySelector('.fa-bell');
                            if (bell) {
                                bell.classList.add('animate-bounce', 'text-red-500');
                                setTimeout(() => {
                                    bell.classList.remove('animate-bounce', 'text-red-500');
                                }, 1000);
                            }

                            setTimeout(() => {
                                notificationCount.classList.remove('animate-pulse');
                            }, 2000);
                        }
                    } else {
                        notificationCount.classList.add('hidden');
                    }
                }
            })
            .catch(error => {
                console.error('Error updating notification count:', error);
            });
    }

    // تحديث عدد الإشعارات عند تحميل الصفحة
    updateNotificationCount();

    // تحديث عدد الإشعارات كل 30 ثانية
    setInterval(updateNotificationCount, 30000);

    // تحسين البحث
    const searchInput = document.querySelector('input[name="query"]');
    const mobileSearchBtn = document.getElementById('mobileSearchBtn');

    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.closest('form').submit();
            }
        });

        // إضافة تأثير بصري عند التركيز
        searchInput.addEventListener('focus', function() {
            this.parentElement.classList.add('ring-2', 'ring-blue-500');
        });

        searchInput.addEventListener('blur', function() {
            this.parentElement.classList.remove('ring-2', 'ring-blue-500');
        });
    }

    if (mobileSearchBtn) {
        mobileSearchBtn.addEventListener('click', function() {
            // يمكن إضافة منطق لإظهار شريط البحث في الموبايل
            showNotification('البحث متاح في الإصدار المكتبي', 'info');
        });
    }

    // دالة تعليم الإشعار كمقروء
    window.markNotificationAsRead = function(notificationId) {
        fetch(`/employee/notifications/${notificationId}/mark-read`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث عدد الإشعارات
                updateNotificationCount();
            }
        })
        .catch(error => {
            console.error('Error marking notification as read:', error);
        });
    };

    // دالة تعليم جميع الإشعارات كمقروءة
    window.markAllNotificationsAsRead = function() {
        fetch('/employee/notifications/mark-all-read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث عدد الإشعارات
                updateNotificationCount();
                // إعادة تحميل الإشعارات
                if (!notificationsMenu.classList.contains('hidden')) {
                    loadNotifications();
                }
            }
        })
        .catch(error => {
            console.error('Error marking all notifications as read:', error);
        });
    };

    // دالة التعامل مع النقر على الإشعار
    window.handleNotificationClick = function(notificationId, actionUrl) {
        // تعليم الإشعار كمقروء
        markNotificationAsRead(notificationId);

        // إغلاق قائمة الإشعارات
        if (notificationsMenu) {
            notificationsMenu.classList.add('hidden');
        }

        // الانتقال إلى الرابط إذا كان موجود
        if (actionUrl && actionUrl !== '' && actionUrl !== 'null') {
            window.location.href = actionUrl;
        } else {
            // الانتقال إلى صفحة الإشعارات
            window.location.href = '/employee/notifications';
        }
    };

    // التنقل بين الصفحات
    const pageTitle = document.getElementById('pageTitle');
    const navLinks = document.querySelectorAll('.nav-link');
    const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');

    // تحديث حالة الروابط النشطة
    function updateActiveLinks() {
        const currentPath = window.location.pathname;
        const pageId = currentPath.split('/').pop() || 'dashboard';

        // تحديث العنوان
        pageTitle.textContent = pageId === 'dashboard' ? 'لوحة التحكم' :
                               pageId === 'orders' ? 'إدارة الطلبات' :
                               pageId === 'reservations' ? 'إدارة الحجوزات' :
                               pageId === 'tables' ? 'حالة الطاولات' :
                               pageId === 'payments' ? 'المدفوعات' :
                               pageId === 'menu' ? 'قائمة الطعام' :
                               pageId === 'notifications' ? 'الإشعارات' : 'لوحة التحكم';

        // تحديث حالة الروابط
        navLinks.forEach(link => {
            if (link.dataset.page === pageId) {
                link.classList.add('bg-primary/10', 'text-primary');
            } else {
                link.classList.remove('bg-primary/10', 'text-primary');
            }
        });

        mobileNavLinks.forEach(link => {
            if (link.dataset.page === pageId) {
                link.classList.add('bg-primary/10', 'text-primary');
            } else {
                link.classList.remove('bg-primary/10', 'text-primary');
            }
        });
    }

    // تنفيذ عند تحميل الصفحة
    updateActiveLinks();

    // ضمان ثبات الثيم - حل نهائي
    function forceThemeConsistency() {
        const savedTheme = localStorage.getItem('theme_preference') || 'light';
        let themeToApply = savedTheme;

        if (savedTheme === 'auto') {
            themeToApply = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        }

        const html = document.documentElement;

        // تطبيق الثيم بقوة
        if (!html.classList.contains(themeToApply)) {
            html.className = ''; // مسح جميع الكلاسات
            html.classList.add(themeToApply);
            html.setAttribute('data-theme', themeToApply);

            // حفظ الثيم
            localStorage.setItem('theme_preference', savedTheme);
            localStorage.setItem('effective_theme', themeToApply);
        }
    }

    // تطبيق فوري
    forceThemeConsistency();

    // مراقبة مستمرة كل ثانية
    setInterval(forceThemeConsistency, 1000);

    // مراقبة عند تغيير الصفحة
    window.addEventListener('beforeunload', forceThemeConsistency);
    window.addEventListener('pageshow', forceThemeConsistency);

    // تحسين زر تبديل الثيم
    const themeToggle = document.querySelector('[data-theme-toggle]');
    if (themeToggle) {
        themeToggle.addEventListener('click', function() {
            // إضافة تأثير بصري
            const icon = this.querySelector('.theme-icon');
            if (icon) {
                icon.classList.add('animate-spin');
                setTimeout(() => {
                    icon.classList.remove('animate-spin');
                }, 500);
            }

            // إظهار رسالة تأكيد
            const currentTheme = document.documentElement.classList.contains('dark') ? 'الوضع المظلم' : 'الوضع الفاتح';
            showNotification(`تم التبديل إلى ${currentTheme}`, 'success');
        });
    }

    // ربط أحداث النقر بالروابط مع ضمان ثبات الثيم
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            const pageId = this.dataset.page;
            // حفظ الثيم قبل التنقل
            const currentTheme = localStorage.getItem('theme_preference');
            const currentEffectiveTheme = localStorage.getItem('effective_theme');
            if (currentTheme) {
                localStorage.setItem('theme_preference', currentTheme);
                localStorage.setItem('effective_theme', currentEffectiveTheme);
            }
            window.location.href = `/employee/${pageId === 'dashboard' ? '' : pageId}`;
        });
    });

    mobileNavLinks.forEach(link => {
        link.addEventListener('click', function() {
            const pageId = this.dataset.page;
            // حفظ الثيم قبل التنقل
            const currentTheme = localStorage.getItem('theme_preference');
            const currentEffectiveTheme = localStorage.getItem('effective_theme');
            if (currentTheme) {
                localStorage.setItem('theme_preference', currentTheme);
                localStorage.setItem('effective_theme', currentEffectiveTheme);
            }
            window.location.href = `/employee/${pageId === 'dashboard' ? '' : pageId}`;
            // إغلاق القائمة الجانبية للموبايل
            mobileMenu.classList.add('-translate-x-full');
        });
    });

    // نموذج إنشاء طلب جديد
    const openNewOrderBtns = document.querySelectorAll('button.bg-primary:has(.fas.fa-plus)');
    const newOrderModal = document.getElementById('new-order-modal');
    const closeNewOrderBtns = document.querySelectorAll('#close-new-order, #cancel-new-order');

    if (openNewOrderBtns.length > 0 && newOrderModal) {
        openNewOrderBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                if (newOrderModal) {
                    newOrderModal.classList.remove('hidden');
                }
            });
        });

        if (closeNewOrderBtns.length > 0) {
            closeNewOrderBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    newOrderModal.classList.add('hidden');
                });
            });
        }

        // إغلاق النموذج عند النقر خارجه
        newOrderModal.addEventListener('click', function(e) {
            if (e.target === newOrderModal) {
                newOrderModal.classList.add('hidden');
            }
        });
    }

    // مخطط المبيعات
    if (document.getElementById('salesChart')) {
        const salesChartOptions = {
            series: [{
                name: 'المبيعات',
                data: [1800, 1600, 2200, 1800, 2400, 1900, 2100]
            }],
            chart: {
                height: 288,
                type: 'area',
                fontFamily: 'Cairo, sans-serif',
                toolbar: {
                    show: false
                },
                zoom: {
                    enabled: false
                }
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                curve: 'smooth',
                width: 3
            },
            colors: ['#FF6B35'],
            fill: {
                type: 'gradient',
                gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.7,
                    opacityTo: 0.3,
                    stops: [0, 90, 100]
                }
            },
            grid: {
                borderColor: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb',
            },
            xaxis: {
                categories: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
                labels: {
                    style: {
                        colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280',
                        fontFamily: 'Cairo, sans-serif'
                    }
                }
            },
            yaxis: {
                labels: {
                    formatter: function(value) {
                        return value + ' ر.س';
                    },
                    style: {
                        colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280',
                        fontFamily: 'Cairo, sans-serif'
                    }
                }
            },
            tooltip: {
                y: {
                    formatter: function(value) {
                        return value + ' ر.س';
                    }
                }
            }
        };

        const salesChart = new ApexCharts(document.getElementById('salesChart'), salesChartOptions);
        salesChart.render();
    }

    // تم إزالة تعيين الصفحة الافتراضية لأننا نستخدم الراوت الآن

    // إضافة مستمع للأخطاء العامة
    window.addEventListener('error', function(e) {
        console.error('JavaScript Error:', e.error);
        showNotification('حدث خطأ في النظام', 'error');
    });

    // إضافة مستمع للأخطاء غير المعالجة
    window.addEventListener('unhandledrejection', function(e) {
        console.error('Unhandled Promise Rejection:', e.reason);
        showNotification('حدث خطأ في الاتصال', 'error');
    });

    // دالة تحديث الحالة العامة للنظام
    function updateSystemStatus() {
        // تحديث عدد الإشعارات
        updateNotificationCount();

        // تحديث عدد عناصر السلة
        updateCartCount();

        // التأكد من ثبات الثيم
        forceThemeConsistency();
    }

    // تحديث الحالة كل دقيقة
    setInterval(updateSystemStatus, 60000);

    console.log('All scripts initialized successfully!');

    }, 100); // نهاية setTimeout
}); // نهاية DOMContentLoaded

// دوال مساعدة عامة
window.showSuccess = function(message) {
    showNotification(message, 'success');
};

window.showError = function(message) {
    showNotification(message, 'error');
};

window.showWarning = function(message) {
    showNotification(message, 'warning');
};

window.showInfo = function(message) {
    showNotification(message, 'info');
};
</script><?php /**PATH C:\Users\<USER>\Downloads\Telegram Desktop\cs450level5050\cs450level10\resources\views/employee/layouts/scripts.blade.php ENDPATH**/ ?>