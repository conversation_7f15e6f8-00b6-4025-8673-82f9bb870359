@extends('employee.layouts.app')

@section('title', 'حالة الطاولات')

@section('content')
<div id="tables-page" class="page fade-in">
    <!-- هيدر الصفحة المبدع -->
    <div class="mb-8 p-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl border border-green-200 dark:border-green-800">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h2 class="text-3xl font-bold text-gradient-ocean mb-2">حالة الطاولات</h2>
                <div class="flex items-center text-green-600 dark:text-green-400 mb-2">
                    <i class="fas fa-chair mr-2"></i>
                    <span>إدارة ومتابعة حالة جميع الطاولات</span>
                </div>
                <div class="flex items-center text-sm text-green-500 dark:text-green-400">
                    <a href="{{ route('employee.dashboard') }}" class="hover:text-green-700 dark:hover:text-green-300 transition-colors duration-300">لوحة التحكم</a>
                    <i class="fas fa-chevron-left mx-2 text-xs"></i>
                    <span>حالة الطاولات</span>
                </div>
                <div class="w-20 h-1 bg-gradient-forest rounded-full mt-2"></div>
            </div>
            <div class="mt-4 md:mt-0 flex items-center space-x-4 space-x-reverse">
                <div class="p-4 rounded-xl bg-gradient-forest text-white shadow-secondary">
                    <i class="fas fa-chair text-3xl"></i>
                </div>
                <a href="{{ route('employee.tables.create') }}" class="btn-magical inline-flex items-center px-6 py-3 bg-gradient-forest text-white font-bold rounded-xl shadow-secondary hover:shadow-xl transition-all duration-300 group">
                    <i class="fas fa-plus mr-2 group-hover:rotate-90 transition-transform duration-300"></i>
                    <span>إضافة طاولة جديدة</span>
                </a>
            </div>
        </div>
    </div>

    <!-- بطاقات حالة الطاولات المبدعة -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- الطاولات المتاحة -->
        <div class="relative bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700 card-float stat-card overflow-hidden group">
            <div class="absolute inset-0 gradient-forest opacity-5 group-hover:opacity-10 transition-opacity duration-300"></div>
            <div class="absolute top-0 left-0 right-0 h-1 gradient-forest"></div>
            <div class="relative z-10 flex items-center justify-between">
                <div>
                    <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">الطاولات المتاحة</h3>
                    <p class="text-3xl font-bold text-gradient-ocean">{{ $availableTables }}</p>
                    <div class="w-12 h-1 gradient-forest rounded-full mt-2"></div>
                </div>
                <div class="p-4 rounded-2xl bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 shadow-secondary group-hover:shadow-xl transition-all duration-300">
                    <i class="fas fa-check-circle text-green-600 dark:text-green-400 text-2xl icon-bounce"></i>
                </div>
            </div>
        </div>

        <!-- الطاولات المشغولة -->
        <div class="relative bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700 card-float stat-card overflow-hidden group">
            <div class="absolute inset-0 bg-gradient-to-r from-red-500 to-pink-500 opacity-5 group-hover:opacity-10 transition-opacity duration-300"></div>
            <div class="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-red-500 to-pink-500"></div>
            <div class="relative z-10 flex items-center justify-between">
                <div>
                    <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">الطاولات المشغولة</h3>
                    <p class="text-3xl font-bold text-gradient-luxury">{{ $occupiedTables }}</p>
                    <div class="w-12 h-1 bg-gradient-to-r from-red-500 to-pink-500 rounded-full mt-2"></div>
                </div>
                <div class="p-4 rounded-2xl bg-gradient-to-br from-red-100 to-pink-100 dark:from-red-900/30 dark:to-pink-900/30 shadow-lg group-hover:shadow-xl transition-all duration-300">
                    <i class="fas fa-users text-red-600 dark:text-red-400 text-2xl icon-pulse"></i>
                </div>
            </div>
        </div>

        <!-- الطاولات المحجوزة -->
        <div class="relative bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700 card-float stat-card overflow-hidden group">
            <div class="absolute inset-0 gradient-sunset opacity-5 group-hover:opacity-10 transition-opacity duration-300"></div>
            <div class="absolute top-0 left-0 right-0 h-1 gradient-sunset"></div>
            <div class="relative z-10 flex items-center justify-between">
                <div>
                    <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">الطاولات المحجوزة</h3>
                    <p class="text-3xl font-bold text-gradient-primary">{{ $reservedTables }}</p>
                    <div class="w-12 h-1 gradient-sunset rounded-full mt-2"></div>
                </div>
                <div class="p-4 rounded-2xl bg-gradient-to-br from-yellow-100 to-orange-100 dark:from-yellow-900/30 dark:to-orange-900/30 shadow-lg group-hover:shadow-xl transition-all duration-300">
                    <i class="fas fa-bookmark text-yellow-600 dark:text-yellow-400 text-2xl icon-rotate"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول الطاولات المبدع -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl overflow-hidden border border-gray-200 dark:border-gray-700">
        <div class="p-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-b border-green-200 dark:border-green-800">
            <div class="flex items-center">
                <div class="p-2 rounded-lg bg-gradient-forest text-white mr-3">
                    <i class="fas fa-table"></i>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-gradient-ocean">جدول الطاولات</h3>
                    <p class="text-sm text-green-600 dark:text-green-400">عرض تفصيلي لجميع الطاولات وحالاتها</p>
                </div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">رقم الطاولة</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">السعة</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الحالة</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الموقع</th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($tables as $table)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                            <span class="inline-flex items-center justify-center h-8 w-8 rounded-full bg-primary/10 text-primary">
                                {{ $table->table_number }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            <span class="inline-flex items-center">
                                <i class="fas fa-user-friends text-gray-400 ml-1.5"></i>
                                {{ $table->capacity }} أشخاص
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            @if($table->status == 'available')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                    <i class="fas fa-check-circle ml-1"></i>متاح
                                </span>
                            @elseif($table->status == 'occupied')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                                    <i class="fas fa-times-circle ml-1"></i>مشغول
                                </span>
                            @elseif($table->status == 'reserved')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                                    <i class="fas fa-clock ml-1"></i>محجوز
                                </span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            {{ $table->location }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            <div class="flex items-center space-x-3 space-x-reverse">
                                <a href="{{ route('employee.tables.show', $table->table_id) }}" class="text-blue-500 hover:text-blue-700 transition-colors">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ route('employee.tables.edit', $table->table_id) }}" class="text-yellow-500 hover:text-yellow-700 transition-colors">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="text-red-500 hover:text-red-700 transition-colors" 
                                        onclick="confirmDelete('{{ $table->table_id }}', '{{ $table->table_number }}')">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                                <button type="button" class="change-status-btn text-primary hover:text-primary/80 transition-colors"
                                        data-table-id="{{ $table->table_id }}" 
                                        data-table-number="{{ $table->table_number }}" 
                                        data-status="{{ $table->status }}">
                                    <i class="fas fa-exchange-alt"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                            <div class="flex flex-col items-center justify-center py-8">
                                <i class="fas fa-coffee text-4xl mb-4 text-gray-300 dark:text-gray-600"></i>
                                <p class="text-lg font-medium">لا توجد طاولات متاحة حالياً</p>
                                <p class="text-sm mt-2">قم بإضافة طاولات جديدة للبدء</p>
                                <a href="{{ route('employee.tables.create') }}" class="mt-4 inline-flex items-center px-4 py-2 bg-primary hover:bg-primary/90 text-white rounded-md transition-colors">
                                    <i class="fas fa-plus ml-2"></i>
                                    <span>إضافة طاولة جديدة</span>
                                </a>
                            </div>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <div class="mt-4">
            {{ $tables->links() }}
        </div>
    </div>
</div>

<!-- Modal: Change Status -->
<div id="changeStatusModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:text-right sm:w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                            تغيير حالة الطاولة <span id="tableNumberSpan"></span>
                        </h3>
                        <div class="mt-6">
                            <form id="changeStatusForm" action="" method="POST">
                                @csrf
                                @method('PUT')
                                <div class="mb-4">
                                    <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الحالة الجديدة</label>
                                    <select id="status" name="status" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                                        <option value="available">متاح</option>
                                        <option value="occupied">مشغول</option>
                                        <option value="reserved">محجوز</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="confirmChangeStatus" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary/90 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm">
                    تأكيد
                </button>
                <button type="button" id="cancelChangeStatus" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal: Delete Confirmation -->
<div id="deleteConfirmationModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-gray-500 dark:bg-gray-900 opacity-75"></div>
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-right overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 dark:bg-red-900/30 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:mr-4 sm:text-right">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                            حذف الطاولة
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                هل أنت متأكد من رغبتك في حذف الطاولة رقم <span id="deleteTableNumber"></span>؟ لا يمكن التراجع عن هذا الإجراء.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <form id="deleteForm" action="" method="POST">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none sm:ml-3 sm:w-auto sm:text-sm">
                        حذف
                    </button>
                </form>
                <button type="button" id="cancelDelete" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    إلغاء
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Change Status Modal
    const changeStatusModal = document.getElementById('changeStatusModal');
    const changeStatusBtns = document.querySelectorAll('.change-status-btn');
    const tableNumberSpan = document.getElementById('tableNumberSpan');
    const statusSelect = document.getElementById('status');
    const changeStatusForm = document.getElementById('changeStatusForm');
    const confirmChangeStatus = document.getElementById('confirmChangeStatus');
    const cancelChangeStatus = document.getElementById('cancelChangeStatus');

    changeStatusBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            const tableId = btn.dataset.tableId;
            const tableNumber = btn.dataset.tableNumber;
            const currentStatus = btn.dataset.status;
            
            tableNumberSpan.textContent = tableNumber;
            statusSelect.value = currentStatus;
            changeStatusForm.action = `/employee/tables/${tableId}/status`;
            
            changeStatusModal.classList.remove('hidden');
        });
    });

    confirmChangeStatus.addEventListener('click', () => {
        changeStatusForm.submit();
    });

    cancelChangeStatus.addEventListener('click', () => {
        changeStatusModal.classList.add('hidden');
    });

    // Delete Confirmation Modal
    function confirmDelete(tableId, tableNumber) {
        const deleteModal = document.getElementById('deleteConfirmationModal');
        const deleteForm = document.getElementById('deleteForm');
        const deleteTableNumber = document.getElementById('deleteTableNumber');
        
        deleteTableNumber.textContent = tableNumber;
        deleteForm.action = `/employee/tables/${tableId}`;
        deleteModal.classList.remove('hidden');
    }

    document.getElementById('cancelDelete').addEventListener('click', () => {
        document.getElementById('deleteConfirmationModal').classList.add('hidden');
    });

    // Close modals when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target === changeStatusModal) {
            changeStatusModal.classList.add('hidden');
        }
        if (e.target === document.getElementById('deleteConfirmationModal')) {
            document.getElementById('deleteConfirmationModal').classList.add('hidden');
        }
    });
</script>
@endsection
