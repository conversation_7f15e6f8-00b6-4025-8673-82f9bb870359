@extends('employee.layouts.app')

@section('title', 'إنشاء حجز جديد - نظام إدارة المطعم')

@section('page-title', 'إنشاء حجز جديد')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">إنشاء حجز جديد</h1>
        <p class="text-gray-600 dark:text-gray-400">قم بإنشاء حجز جديد للطاولات</p>
    </div>

    @if(session('error'))
    <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 mb-6 rounded-md dark:bg-red-900/30 dark:text-red-500 dark:border-red-500">
        <p>{{ session('error') }}</p>
    </div>
    @endif

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-bold text-gray-800 dark:text-white">معلومات الحجز</h2>
        </div>

        <form action="{{ route('employee.reservations.store') }}" method="POST" class="p-6 space-y-6">
            @csrf

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- اختيار العميل -->
                <div>
                    <label for="user_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">العميل</label>
                    <select id="user_id" name="user_id" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white @error('user_id') border-red-500 dark:border-red-500 @enderror">
                        <option value="">اختر العميل</option>
                        @foreach($customers as $customer)
                        <option value="{{ $customer->user_id }}" {{ old('user_id') == $customer->user_id ? 'selected' : '' }}>
                            {{ $customer->first_name }} {{ $customer->last_name }} ({{ $customer->phone }})
                        </option>
                        @endforeach
                    </select>
                    @error('user_id')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- اختيار الطاولة -->
                <div>
                    <label for="table_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الطاولة</label>
                    <select id="table_id" name="table_id" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white @error('table_id') border-red-500 dark:border-red-500 @enderror">
                        <option value="">اختر الطاولة</option>
                        @foreach($tables as $table)
                        <option value="{{ $table->table_id }}" {{ old('table_id') == $table->table_id ? 'selected' : '' }}>
                            طاولة رقم {{ $table->table_number }} ({{ $table->capacity }} أشخاص)
                        </option>
                        @endforeach
                    </select>
                    @error('table_id')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- وقت الحجز -->
                <div>
                    <label for="reservation_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">وقت الحجز</label>
                    <input type="datetime-local" id="reservation_time" name="reservation_time" value="{{ old('reservation_time') }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white @error('reservation_time') border-red-500 dark:border-red-500 @enderror">
                    @error('reservation_time')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- مدة الحجز -->
                <div>
                    <label for="duration" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">مدة الحجز (بالدقائق)</label>
                    <select id="duration" name="duration" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white @error('duration') border-red-500 dark:border-red-500 @enderror">
                        <option value="30" {{ old('duration') == 30 ? 'selected' : '' }}>30 دقيقة</option>
                        <option value="60" {{ old('duration', 60) == 60 ? 'selected' : '' }}>ساعة واحدة</option>
                        <option value="90" {{ old('duration') == 90 ? 'selected' : '' }}>ساعة ونصف</option>
                        <option value="120" {{ old('duration') == 120 ? 'selected' : '' }}>ساعتان</option>
                        <option value="180" {{ old('duration') == 180 ? 'selected' : '' }}>3 ساعات</option>
                        <option value="240" {{ old('duration') == 240 ? 'selected' : '' }}>4 ساعات</option>
                    </select>
                    @error('duration')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- ملاحظات -->
                <div class="md:col-span-2">
                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">ملاحظات</label>
                    <textarea id="notes" name="notes" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white @error('notes') border-red-500 dark:border-red-500 @enderror">{{ old('notes') }}</textarea>
                    @error('notes')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="flex justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
                <a href="{{ route('employee.reservations') }}" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                    إلغاء
                </a>
                <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50">
                    إنشاء الحجز
                </button>
            </div>
        </form>
    </div>

    <div class="mt-8">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
            <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-bold text-gray-800 dark:text-white">توافر الطاولات</h2>
                <p class="text-sm text-gray-600 dark:text-gray-400">عرض توافر الطاولات للمساعدة في اختيار الطاولة المناسبة</p>
            </div>

            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    @foreach($tables as $table)
                    <div class="border border-gray-200 dark:border-gray-700 rounded-md overflow-hidden">
                        <div class="p-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
                            <h3 class="font-bold text-gray-800 dark:text-white">طاولة رقم {{ $table->table_number }}</h3>
                        </div>
                        <div class="p-4">
                            <div class="flex items-center mb-2">
                                <span class="w-3 h-3 rounded-full {{ $table->status == 'available' ? 'bg-green-500' : ($table->status == 'occupied' ? 'bg-red-500' : ($table->status == 'reserved' ? 'bg-yellow-500' : 'bg-gray-500')) }} mr-2"></span>
                                <span class="text-sm text-gray-700 dark:text-gray-300">
                                    {{ $table->status == 'available' ? 'متاحة' : ($table->status == 'occupied' ? 'مشغولة' : ($table->status == 'reserved' ? 'محجوزة' : 'غير متاحة')) }}
                                </span>
                            </div>
                            <div class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                <i class="fas fa-users ml-1"></i> {{ $table->capacity }} أشخاص
                            </div>
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                <i class="fas fa-map-marker-alt ml-1"></i> {{ $table->location ?? 'الصالة الرئيسية' }}
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحديث حالة الطاولات عند تغيير وقت الحجز
        const reservationTimeInput = document.getElementById('reservation_time');
        const durationSelect = document.getElementById('duration');

        if (reservationTimeInput && durationSelect) {
            const updateTableAvailability = function() {
                const reservationTime = reservationTimeInput.value;
                const duration = durationSelect.value;

                if (reservationTime && duration) {
                    // يمكن إضافة طلب AJAX هنا للتحقق من توافر الطاولات في الوقت المحدد
                    console.log('Checking availability for time:', reservationTime, 'duration:', duration);
                }
            };

            reservationTimeInput.addEventListener('change', updateTableAvailability);
            durationSelect.addEventListener('change', updateTableAvailability);
        }
    });
</script>
@endsection
