// حل بسيط وسريع لمشكلة الوضع المظلم في المخططات
(function() {
    'use strict';
    
    console.log('🌙 تحميل إصلاح الوضع المظلم للمخططات...');
    
    // متغيرات عامة
    let currentTheme = document.documentElement.classList.contains('dark') ? 'dark' : 'light';
    let allCharts = new Map();
    
    // دالة للحصول على ألوان الوضع الحالي
    function getThemeColors() {
        const isDark = document.documentElement.classList.contains('dark');
        return {
            primary: isDark ? '#3b82f6' : '#f97316',
            success: isDark ? '#34d399' : '#10b981',
            danger: isDark ? '#f87171' : '#ef4444',
            warning: isDark ? '#fbbf24' : '#f59e0b',
            info: isDark ? '#60a5fa' : '#3b82f6',
            purple: isDark ? '#a78bfa' : '#8b5cf6',
            text: isDark ? '#e5e7eb' : '#4b5563',
            textSecondary: isDark ? '#9ca3af' : '#6b7280',
            border: isDark ? '#374151' : '#e5e7eb',
            background: isDark ? '#1f2937' : '#ffffff'
        };
    }
    
    // دالة لتسجيل مخطط
    function registerChart(id, chart) {
        allCharts.set(id, chart);
        console.log('📊 تم تسجيل المخطط:', id);
    }
    
    // دالة لتحديث جميع المخططات
    function updateAllCharts() {
        console.log('🔄 تحديث جميع المخططات للوضع الجديد...');
        
        allCharts.forEach((chart, id) => {
            try {
                if (chart && typeof chart.updateOptions === 'function') {
                    const colors = getThemeColors();
                    const isDark = document.documentElement.classList.contains('dark');
                    
                    chart.updateOptions({
                        theme: {
                            mode: isDark ? 'dark' : 'light'
                        },
                        grid: {
                            borderColor: colors.border
                        },
                        xaxis: {
                            labels: {
                                style: {
                                    colors: colors.textSecondary
                                }
                            },
                            axisBorder: {
                                color: colors.border
                            },
                            axisTicks: {
                                color: colors.border
                            }
                        },
                        yaxis: {
                            labels: {
                                style: {
                                    colors: colors.textSecondary
                                }
                            }
                        },
                        tooltip: {
                            theme: isDark ? 'dark' : 'light'
                        },
                        legend: {
                            labels: {
                                colors: colors.text
                            }
                        }
                    });
                    
                    console.log('✅ تم تحديث المخطط:', id);
                }
            } catch (error) {
                console.error('❌ خطأ في تحديث المخطط:', id, error);
            }
        });
    }
    
    // مراقبة تغيير الوضع المظلم
    function observeThemeChanges() {
        // مراقب DOM
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const newTheme = document.documentElement.classList.contains('dark') ? 'dark' : 'light';
                    if (newTheme !== currentTheme) {
                        console.log('🌙 تم تغيير الوضع من', currentTheme, 'إلى', newTheme);
                        currentTheme = newTheme;
                        setTimeout(updateAllCharts, 100);
                    }
                }
            });
        });
        
        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['class']
        });
        
        // مراقبة إضافية بالتحقق الدوري
        setInterval(function() {
            const newTheme = document.documentElement.classList.contains('dark') ? 'dark' : 'light';
            if (newTheme !== currentTheme) {
                console.log('🔄 تم اكتشاف تغيير الوضع:', newTheme);
                currentTheme = newTheme;
                updateAllCharts();
            }
        }, 1000);
    }
    
    // تصدير الدوال للاستخدام العام
    window.simpleDarkModeFix = {
        registerChart: registerChart,
        updateAllCharts: updateAllCharts,
        getThemeColors: getThemeColors,
        getCurrentTheme: function() {
            return document.documentElement.classList.contains('dark') ? 'dark' : 'light';
        }
    };
    
    // بدء المراقبة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', observeThemeChanges);
    } else {
        observeThemeChanges();
    }
    
    console.log('✅ تم تحميل إصلاح الوضع المظلم بنجاح!');
})();

// دالة مساعدة سريعة لإجبار تحديث المخططات
function forceUpdateCharts() {
    if (window.simpleDarkModeFix) {
        window.simpleDarkModeFix.updateAllCharts();
    }
}

// دالة لتسجيل مخطط بسرعة
function quickRegisterChart(id, chart) {
    if (window.simpleDarkModeFix) {
        window.simpleDarkModeFix.registerChart(id, chart);
    }
}
