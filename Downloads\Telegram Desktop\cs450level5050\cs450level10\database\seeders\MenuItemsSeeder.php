<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\MenuItem;
use App\Models\Ingredient;
use App\Models\Recipe;
use Illuminate\Support\Facades\DB;

class MenuItemsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء مكونات
        $ingredient1 = Ingredient::firstOrCreate(
            ['name' => 'لحم بقري'],
            [
                'unit' => 'جرام',
                'cost_per_unit' => 0.15,
                'is_active' => true
            ]
        );

        $ingredient2 = Ingredient::firstOrCreate(
            ['name' => 'خبز برجر'],
            [
                'unit' => 'قطعة',
                'cost_per_unit' => 2.5,
                'is_active' => true
            ]
        );

        $ingredient3 = Ingredient::firstOrCreate(
            ['name' => 'جبنة شيدر'],
            [
                'unit' => 'جرام',
                'cost_per_unit' => 0.08,
                'is_active' => true
            ]
        );

        $ingredient4 = Ingredient::firstOrCreate(
            ['name' => 'عجينة بيتزا'],
            [
                'unit' => 'قطعة',
                'cost_per_unit' => 5.0,
                'is_active' => true
            ]
        );

        $ingredient5 = Ingredient::firstOrCreate(
            ['name' => 'جبنة موزاريلا'],
            [
                'unit' => 'جرام',
                'cost_per_unit' => 0.1,
                'is_active' => true
            ]
        );

        $ingredient6 = Ingredient::firstOrCreate(
            ['name' => 'خضروات مشكلة'],
            [
                'unit' => 'جرام',
                'cost_per_unit' => 0.05,
                'is_active' => true
            ]
        );

        // إنشاء منتج
        $menuItem1 = MenuItem::firstOrCreate(
            ['name' => 'برجر لحم أنجوس'],
            [
                'price' => 55,
                'category' => 'main',
                'description' => 'برجر لحم بقري فاخر مع صلصة خاصة وجبنة شيدر وخضار طازجة',
                'is_available' => true
            ]
        );

        // إضافة المكونات للمنتج
        if (Recipe::where('menu_item_id', $menuItem1->item_id)->count() == 0) {
            Recipe::create([
                'menu_item_id' => $menuItem1->item_id,
                'ingredient_id' => $ingredient1->ingredient_id,
                'quantity' => 200
            ]);

            Recipe::create([
                'menu_item_id' => $menuItem1->item_id,
                'ingredient_id' => $ingredient2->ingredient_id,
                'quantity' => 1
            ]);

            Recipe::create([
                'menu_item_id' => $menuItem1->item_id,
                'ingredient_id' => $ingredient3->ingredient_id,
                'quantity' => 50
            ]);
        }

        // إنشاء منتج آخر
        $menuItem2 = MenuItem::firstOrCreate(
            ['name' => 'بيتزا سوبريم'],
            [
                'price' => 65,
                'category' => 'main',
                'description' => 'بيتزا كلاسيكية مع صلصة طماطم وجبنة موزاريلا وفلفل وبصل وزيتون وفطر',
                'is_available' => true
            ]
        );

        // إضافة المكونات للمنتج
        if (Recipe::where('menu_item_id', $menuItem2->item_id)->count() == 0) {
            Recipe::create([
                'menu_item_id' => $menuItem2->item_id,
                'ingredient_id' => $ingredient4->ingredient_id,
                'quantity' => 1
            ]);

            Recipe::create([
                'menu_item_id' => $menuItem2->item_id,
                'ingredient_id' => $ingredient5->ingredient_id,
                'quantity' => 150
            ]);
        }

        // إنشاء منتج ثالث
        $menuItem3 = MenuItem::firstOrCreate(
            ['name' => 'سلطة شيف خاصة'],
            [
                'price' => 45,
                'category' => 'appetizer',
                'description' => 'خضروات طازجة مع تتبيلة خاصة مع قطع الدجاج المشوي وجبن البارميزان',
                'is_available' => true
            ]
        );

        // إضافة المكونات للمنتج
        if (Recipe::where('menu_item_id', $menuItem3->item_id)->count() == 0) {
            Recipe::create([
                'menu_item_id' => $menuItem3->item_id,
                'ingredient_id' => $ingredient6->ingredient_id,
                'quantity' => 300
            ]);
        }
    }
}
