<?php
/**
 * ملف فحص النظام - Eat Hub Restaurant System
 * يتحقق من جميع المتطلبات والإعدادات المطلوبة
 */

echo "========================================\n";
echo "   Eat Hub - فحص النظام\n";
echo "   Restaurant Management System\n";
echo "========================================\n\n";

$errors = [];
$warnings = [];
$success = [];

// فحص إصدار PHP
echo "[1] فحص إصدار PHP...\n";
$phpVersion = phpversion();
if (version_compare($phpVersion, '8.1.0', '>=')) {
    $success[] = "✓ PHP $phpVersion (مطلوب 8.1+)";
} else {
    $errors[] = "✗ PHP $phpVersion - مطلوب إصدار 8.1 أو أحدث";
}

// فحص الإضافات المطلوبة
echo "[2] فحص إضافات PHP المطلوبة...\n";
$requiredExtensions = [
    'pdo_mysql' => 'قاعدة البيانات MySQL',
    'mbstring' => 'دعم النصوص متعددة البايت',
    'xml' => 'معالجة XML',
    'curl' => 'طلبات HTTP',
    'zip' => 'ضغط الملفات',
    'gd' => 'معالجة الصور',
    'json' => 'معالجة JSON',
    'openssl' => 'التشفير'
];

foreach ($requiredExtensions as $ext => $desc) {
    if (extension_loaded($ext)) {
        $success[] = "✓ $ext ($desc)";
    } else {
        $errors[] = "✗ $ext - $desc";
    }
}

// فحص ملف .env
echo "[3] فحص ملف البيئة...\n";
if (file_exists('.env')) {
    $success[] = "✓ ملف .env موجود";
    
    // قراءة إعدادات قاعدة البيانات
    $envContent = file_get_contents('.env');
    if (strpos($envContent, 'APP_KEY=base64:') !== false) {
        $success[] = "✓ مفتاح التطبيق مُعرَّف";
    } else {
        $warnings[] = "⚠ مفتاح التطبيق غير مُعرَّف - شغل: php artisan key:generate";
    }
    
    if (strpos($envContent, 'DB_DATABASE=') !== false) {
        $success[] = "✓ إعدادات قاعدة البيانات موجودة";
    } else {
        $errors[] = "✗ إعدادات قاعدة البيانات مفقودة";
    }
} else {
    $errors[] = "✗ ملف .env مفقود - انسخ من .env.example";
}

// فحص مجلدات الصلاحيات
echo "[4] فحص صلاحيات المجلدات...\n";
$writableDirs = ['storage', 'bootstrap/cache'];
foreach ($writableDirs as $dir) {
    if (is_dir($dir) && is_writable($dir)) {
        $success[] = "✓ $dir قابل للكتابة";
    } else {
        $errors[] = "✗ $dir غير قابل للكتابة";
    }
}

// فحص Composer
echo "[5] فحص تبعيات Composer...\n";
if (file_exists('vendor/autoload.php')) {
    $success[] = "✓ تبعيات Composer مثبتة";
} else {
    $errors[] = "✗ تبعيات Composer غير مثبتة - شغل: composer install";
}

// فحص Node.js
echo "[6] فحص تبعيات Node.js...\n";
if (file_exists('node_modules')) {
    $success[] = "✓ تبعيات Node.js مثبتة";
} else {
    $warnings[] = "⚠ تبعيات Node.js غير مثبتة - شغل: npm install";
}

// فحص ملفات البناء
echo "[7] فحص ملفات الواجهة الأمامية...\n";
if (file_exists('public/build/manifest.json')) {
    $success[] = "✓ ملفات الواجهة الأمامية مبنية";
} else {
    $warnings[] = "⚠ ملفات الواجهة الأمامية غير مبنية - شغل: npm run build";
}

// محاولة الاتصال بقاعدة البيانات
echo "[8] فحص الاتصال بقاعدة البيانات...\n";
if (file_exists('.env')) {
    try {
        // تحميل Laravel bootstrap
        require_once 'vendor/autoload.php';
        $app = require_once 'bootstrap/app.php';
        $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
        
        // محاولة الاتصال
        $pdo = DB::connection()->getPdo();
        $success[] = "✓ الاتصال بقاعدة البيانات ناجح";
        
        // فحص الجداول
        $tables = DB::select("SHOW TABLES");
        if (count($tables) > 0) {
            $success[] = "✓ قاعدة البيانات تحتوي على " . count($tables) . " جدول";
        } else {
            $warnings[] = "⚠ قاعدة البيانات فارغة - شغل: php artisan migrate";
        }
        
    } catch (Exception $e) {
        $errors[] = "✗ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage();
    }
}

// عرض النتائج
echo "\n========================================\n";
echo "نتائج الفحص:\n";
echo "========================================\n\n";

if (!empty($success)) {
    echo "✅ نجح:\n";
    foreach ($success as $item) {
        echo "   $item\n";
    }
    echo "\n";
}

if (!empty($warnings)) {
    echo "⚠️  تحذيرات:\n";
    foreach ($warnings as $item) {
        echo "   $item\n";
    }
    echo "\n";
}

if (!empty($errors)) {
    echo "❌ أخطاء:\n";
    foreach ($errors as $item) {
        echo "   $item\n";
    }
    echo "\n";
}

// الخلاصة
echo "========================================\n";
if (empty($errors)) {
    if (empty($warnings)) {
        echo "🎉 النظام جاهز تماماً!\n";
        echo "يمكنك تشغيل: php artisan serve\n";
    } else {
        echo "✅ النظام جاهز مع بعض التحذيرات\n";
        echo "يمكنك تشغيل النظام ولكن يُنصح بحل التحذيرات\n";
    }
} else {
    echo "❌ يجب حل الأخطاء قبل تشغيل النظام\n";
    echo "راجع الأخطاء أعلاه وقم بحلها\n";
}
echo "========================================\n";

// إرجاع كود الخروج
exit(empty($errors) ? 0 : 1);
?>
