<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class OfferController extends Controller
{
    public function show($slug)
    {
        // بيانات العروض (يمكن استبدالها بقاعدة البيانات لاحقاً)
        $offers = [
            'family-discount' => [
                'slug' => 'family-discount',
                'title' => 'خصم 25% على طلبات الوجبات العائلية',
                'description' => 'استمتع بخصم 25% على جميع الوجبات العائلية في مطعمنا. العرض يشمل جميع الأطباق الرئيسية والمقبلات والحلويات المخصصة للعائلات.',
                'image' => '/images/offers/family-discount.jpg',
                'type' => 'حجز',
                'discount' => 25,
                'original_price' => 200,
                'discounted_price' => 150,
                'start_date' => '1 يونيو 2023',
                'end_date' => '30 يونيو 2023',
                'is_active' => true,
                'features' => [
                    'خصم 25% على جميع الوجبات العائلية',
                    'يشمل المقبلات والأطباق الرئيسية والحلويات',
                    'صالح للطاولات من 4 أشخاص فأكثر',
                    'إمكانية الحجز المسبق',
                    'خدمة مجانية للأطفال تحت 5 سنوات'
                ],
                'conditions' => [
                    'العرض صالح للطاولات من 4 أشخاص فأكثر',
                    'يجب الحجز المسبق قبل 24 ساعة',
                    'لا يمكن دمج هذا العرض مع عروض أخرى',
                    'العرض صالح من الأحد إلى الخميس فقط',
                    'يجب إبراز هوية شخصية عند الحضور'
                ]
            ],
            'new-dishes' => [
                'slug' => 'new-dishes',
                'title' => 'أطباق جديدة في قائمتنا',
                'description' => 'اكتشف مجموعة جديدة من الأطباق الشهية المضافة حديثاً إلى قائمة طعامنا. أطباق مبتكرة بنكهات عربية أصيلة وعالمية مميزة.',
                'image' => '/images/offers/new-dishes.jpg',
                'type' => 'طعام',
                'discount' => null,
                'original_price' => null,
                'discounted_price' => null,
                'start_date' => '5 يونيو 2023',
                'end_date' => '31 ديسمبر 2023',
                'is_active' => true,
                'features' => [
                    'أطباق جديدة بنكهات مبتكرة',
                    'مكونات طازجة ومحلية',
                    'خيارات نباتية ومتنوعة',
                    'أسعار تنافسية',
                    'تحضير سريع وطازج'
                ],
                'conditions' => [
                    'الأطباق متوفرة حسب التوفر اليومي',
                    'يمكن طلبها للتناول في المطعم أو التوصيل',
                    'بعض الأطباق قد تحتاج وقت تحضير إضافي',
                    'الأسعار قابلة للتغيير دون إشعار مسبق'
                ]
            ],
            'music-nights' => [
                'slug' => 'music-nights',
                'title' => 'ليالي الموسيقى الحية',
                'description' => 'استمتع بأمسيات موسيقية رائعة مع فرق موسيقية محلية وعالمية. أجواء مميزة مع أفضل الأطباق والمشروبات في أمسيات لا تُنسى.',
                'image' => '/images/offers/music-nights.jpg',
                'type' => 'حجز',
                'discount' => null,
                'original_price' => 100,
                'discounted_price' => 80,
                'start_date' => '15 يونيو 2023',
                'end_date' => '15 سبتمبر 2023',
                'is_active' => true,
                'features' => [
                    'عروض موسيقية حية كل ليلة جمعة وسبت',
                    'فرق موسيقية متنوعة',
                    'قائمة طعام خاصة للأمسيات',
                    'أجواء رومانسية ومميزة',
                    'إمكانية طلب أغاني خاصة'
                ],
                'conditions' => [
                    'الحجز مطلوب مسبقاً',
                    'العرض متاح أيام الجمعة والسبت فقط',
                    'يبدأ العرض الموسيقي في الساعة 8 مساءً',
                    'الحد الأدنى للحجز شخصين',
                    'رسوم إضافية للطاولات المميزة'
                ]
            ]
        ];

        // التحقق من وجود العرض
        if (!isset($offers[$slug])) {
            abort(404, 'العرض غير موجود');
        }

        $offer = $offers[$slug];

        // الحصول على عروض مشابهة (استثناء العرض الحالي)
        $relatedOffers = collect($offers)
            ->reject(function ($item) use ($slug) {
                return $item['slug'] === $slug;
            })
            ->take(3)
            ->values()
            ->toArray();

        return view('customer.offers.show', compact('offer', 'relatedOffers'));
    }

    public function index()
    {
        // عرض جميع العروض المتاحة
        $offers = [
            [
                'slug' => 'family-discount',
                'title' => 'خصم 25% على طلبات الوجبات العائلية',
                'description' => 'استمتع بخصم 25% على جميع الوجبات العائلية في مطعمنا.',
                'image' => '/images/offers/family-discount.jpg',
                'type' => 'حجز',
                'discount' => 25,
                'start_date' => '1 يونيو 2023',
                'end_date' => '30 يونيو 2023',
                'is_active' => true
            ],
            [
                'slug' => 'new-dishes',
                'title' => 'أطباق جديدة في قائمتنا',
                'description' => 'اكتشف مجموعة جديدة من الأطباق الشهية المضافة حديثاً إلى قائمة طعامنا.',
                'image' => '/images/offers/new-dishes.jpg',
                'type' => 'طعام',
                'discount' => null,
                'start_date' => '5 يونيو 2023',
                'end_date' => '31 ديسمبر 2023',
                'is_active' => true
            ],
            [
                'slug' => 'music-nights',
                'title' => 'ليالي الموسيقى الحية',
                'description' => 'استمتع بأمسيات موسيقية رائعة مع فرق موسيقية محلية وعالمية.',
                'image' => '/images/offers/music-nights.jpg',
                'type' => 'حجز',
                'discount' => 20,
                'start_date' => '15 يونيو 2023',
                'end_date' => '15 سبتمبر 2023',
                'is_active' => true
            ]
        ];

        return view('customer.offers.index', compact('offers'));
    }
}
