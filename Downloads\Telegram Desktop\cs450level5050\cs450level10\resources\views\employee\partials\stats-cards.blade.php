<!-- بطاقات الإحصائيات الرئيسية -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- بطاقة الطلبات اليوم - تصميم مبدع -->
    <div class="relative bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700 card-float stat-card overflow-hidden group">
        <!-- خلفية متدرجة متحركة -->
        <div class="absolute inset-0 gradient-primary opacity-5 group-hover:opacity-10 transition-opacity duration-300"></div>

        <!-- شريط علوي ملون -->
        <div class="absolute top-0 left-0 right-0 h-1 gradient-primary"></div>

        <div class="relative z-10 flex items-center justify-between">
            <div class="flex-1">
                <div class="flex items-center">
                    <!-- أيقونة مبدعة مع تأثيرات -->
                    <div class="relative p-4 rounded-2xl bg-gradient-to-br from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 shadow-primary group-hover:shadow-lg transition-all duration-300">
                        <div class="absolute inset-0 rounded-2xl bg-gradient-primary opacity-10"></div>
                        <i class="fas fa-shopping-cart text-orange-600 dark:text-orange-400 text-2xl icon-bounce relative z-10"></i>
                        <!-- تأثير التوهج -->
                        <div class="absolute inset-0 rounded-2xl border-2 border-orange-300 dark:border-orange-600 opacity-0 group-hover:opacity-50 transition-opacity duration-300"></div>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">طلبات اليوم</p>
                        <p class="text-4xl font-bold text-gradient-primary mb-2">{{ $todayStats['ordersCount'] ?? 0 }}</p>
                        <!-- شريط تقدم مبدع -->
                        <div class="w-16 h-1 bg-gradient-primary rounded-full"></div>
                    </div>
                </div>

                <!-- قسم الإحصائيات المفصلة -->
                <div class="mt-6 space-y-3">
                    <!-- إجمالي المبيعات -->
                    <div class="flex items-center justify-between p-3 rounded-xl bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 border border-orange-200 dark:border-orange-800">
                        <div class="flex items-center">
                            <div class="p-2 rounded-lg bg-gradient-primary">
                                <i class="fas fa-coins text-white text-sm"></i>
                            </div>
                            <div class="mr-3">
                                <p class="text-xs text-gray-600 dark:text-gray-400">إجمالي المبيعات</p>
                                <p class="text-lg font-bold text-orange-600 dark:text-orange-400">{{ number_format($todayStats['totalSales'] ?? 0, 2) }} د.ل</p>
                            </div>
                        </div>
                        <div class="text-right">
                            @php
                                $todayOrders = $todayStats['ordersCount'] ?? 0;
                                $yesterdayOrders = $yesterdayStats['ordersCount'] ?? 0;
                                $change = $yesterdayOrders > 0 ? (($todayOrders - $yesterdayOrders) / $yesterdayOrders) * 100 : 0;
                            @endphp
                            @if($change > 0)
                                <div class="flex items-center text-green-600 dark:text-green-400 text-sm font-medium">
                                    <i class="fas fa-arrow-up mr-1 icon-bounce"></i>
                                    <span>+{{ number_format($change, 1) }}%</span>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">مقارنة بالأمس</p>
                            @elseif($change < 0)
                                <div class="flex items-center text-red-600 dark:text-red-400 text-sm font-medium">
                                    <i class="fas fa-arrow-down mr-1"></i>
                                    <span>{{ number_format($change, 1) }}%</span>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">مقارنة بالأمس</p>
                            @else
                                <div class="flex items-center text-gray-500 dark:text-gray-400 text-sm font-medium">
                                    <i class="fas fa-minus mr-1"></i>
                                    <span>0%</span>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">نفس الأمس</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- زر الإجراء المبدع -->
        <div class="relative z-10 mt-6">
            <a href="{{ route('employee.orders') }}" class="btn-magical inline-flex items-center px-4 py-2 bg-gradient-primary text-white rounded-xl font-medium text-sm shadow-primary hover:shadow-lg transition-all duration-300 group">
                <span>عرض جميع الطلبات</span>
                <i class="fas fa-arrow-left mr-2 group-hover:translate-x-1 transition-transform duration-300"></i>
            </a>
        </div>
    </div>

    <!-- بطاقة الطلبات قيد التحضير - تصميم مبدع -->
    <div class="relative bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700 card-float stat-card overflow-hidden group">
        <!-- خلفية متدرجة متحركة -->
        <div class="absolute inset-0 gradient-sunset opacity-5 group-hover:opacity-10 transition-opacity duration-300"></div>

        <!-- شريط علوي ملون -->
        <div class="absolute top-0 left-0 right-0 h-1 gradient-sunset"></div>

        <div class="relative z-10 flex items-center justify-between">
            <div class="flex-1">
                <div class="flex items-center">
                    <!-- أيقونة مبدعة مع تأثيرات -->
                    <div class="relative p-4 rounded-2xl bg-gradient-to-br from-yellow-100 to-orange-100 dark:from-yellow-900/30 dark:to-orange-900/30 shadow-lg group-hover:shadow-xl transition-all duration-300">
                        <div class="absolute inset-0 rounded-2xl bg-gradient-sunset opacity-10"></div>
                        <i class="fas fa-utensils text-yellow-600 dark:text-yellow-400 text-2xl icon-pulse relative z-10"></i>
                        <!-- تأثير التوهج -->
                        <div class="absolute inset-0 rounded-2xl border-2 border-yellow-300 dark:border-yellow-600 opacity-0 group-hover:opacity-50 transition-opacity duration-300"></div>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">قيد التحضير</p>
                        <p class="text-4xl font-bold text-gradient-luxury mb-2">{{ $todayStats['pendingOrdersCount'] ?? 0 }}</p>
                        <!-- شريط تقدم مبدع -->
                        <div class="w-16 h-1 gradient-sunset rounded-full"></div>
                    </div>
                </div>

                <!-- قسم شريط التقدم المبدع -->
                <div class="mt-6">
                    @php
                        $totalOrders = max(($todayStats['ordersCount'] ?? 1), 1);
                        $pendingOrders = $todayStats['pendingOrdersCount'] ?? 0;
                        $completedPercentage = (($totalOrders - $pendingOrders) / $totalOrders) * 100;
                    @endphp

                    <!-- حالة التحضير -->
                    <div class="flex items-center justify-between mb-3">
                        @if($pendingOrders > 0)
                            <div class="flex items-center text-yellow-600 dark:text-yellow-400">
                                <div class="p-2 rounded-lg bg-yellow-100 dark:bg-yellow-900/30 mr-2">
                                    <i class="fas fa-clock text-yellow-600 dark:text-yellow-400 icon-rotate"></i>
                                </div>
                                <span class="font-medium">{{ $pendingOrders }} طلب يحتاج إكمال</span>
                            </div>
                        @else
                            <div class="flex items-center text-green-600 dark:text-green-400">
                                <div class="p-2 rounded-lg bg-green-100 dark:bg-green-900/30 mr-2">
                                    <i class="fas fa-check-circle text-green-600 dark:text-green-400 icon-bounce"></i>
                                </div>
                                <span class="font-medium">جميع الطلبات مكتملة!</span>
                            </div>
                        @endif
                        <span class="text-sm font-bold text-gray-700 dark:text-gray-300">{{ round($completedPercentage) }}%</span>
                    </div>

                    <!-- شريط التقدم المتطور -->
                    <div class="relative">
                        <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
                            <div class="gradient-forest h-3 rounded-full transition-all duration-500 ease-out relative" style="width: {{ $completedPercentage }}%">
                                <!-- تأثير اللمعان -->
                                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse"></div>
                            </div>
                        </div>
                        <!-- نقاط التقدم -->
                        <div class="absolute top-0 left-0 right-0 flex justify-between items-center h-3">
                            @for($i = 0; $i <= 4; $i++)
                                <div class="w-1 h-3 bg-white dark:bg-gray-800 rounded-full {{ $completedPercentage >= ($i * 25) ? 'opacity-100' : 'opacity-50' }}"></div>
                            @endfor
                        </div>
                    </div>

                    <!-- تسميات التقدم -->
                    <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-2">
                        <span>بداية</span>
                        <span>25%</span>
                        <span>50%</span>
                        <span>75%</span>
                        <span>مكتمل</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- زر الإجراء المبدع -->
        <div class="relative z-10 mt-6">
            <a href="{{ route('employee.orders', ['status' => 'preparing']) }}" class="btn-magical inline-flex items-center px-4 py-2 gradient-sunset text-white rounded-xl font-medium text-sm shadow-lg hover:shadow-xl transition-all duration-300 group">
                <span>عرض الطلبات قيد التحضير</span>
                <i class="fas fa-arrow-left mr-2 group-hover:translate-x-1 transition-transform duration-300"></i>
            </a>
        </div>
    </div>

    <!-- بطاقة حجوزات اليوم - تصميم مبدع -->
    <div class="relative bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700 card-float stat-card overflow-hidden group">
        <!-- خلفية متدرجة متحركة -->
        <div class="absolute inset-0 gradient-luxury opacity-5 group-hover:opacity-10 transition-opacity duration-300"></div>

        <!-- شريط علوي ملون -->
        <div class="absolute top-0 left-0 right-0 h-1 gradient-luxury"></div>

        <div class="relative z-10 flex items-center justify-between">
            <div class="flex-1">
                <div class="flex items-center">
                    <!-- أيقونة مبدعة مع تأثيرات -->
                    <div class="relative p-4 rounded-2xl bg-gradient-to-br from-purple-100 to-pink-100 dark:from-purple-900/30 dark:to-pink-900/30 shadow-luxury group-hover:shadow-xl transition-all duration-300">
                        <div class="absolute inset-0 rounded-2xl bg-gradient-luxury opacity-10"></div>
                        <i class="fas fa-calendar-check text-purple-600 dark:text-purple-400 text-2xl icon-pulse relative z-10"></i>
                        <!-- تأثير التوهج -->
                        <div class="absolute inset-0 rounded-2xl border-2 border-purple-300 dark:border-purple-600 opacity-0 group-hover:opacity-50 transition-opacity duration-300 neon-border"></div>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">حجوزات اليوم</p>
                        <p class="text-4xl font-bold text-gradient-luxury mb-2">{{ $todayStats['reservationsCount'] ?? 0 }}</p>
                        <!-- شريط تقدم مبدع -->
                        <div class="w-16 h-1 gradient-luxury rounded-full"></div>
                    </div>
                </div>

                <!-- قسم الحجوزات القادمة -->
                <div class="mt-6">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center text-purple-600 dark:text-purple-400">
                            <div class="p-2 rounded-lg bg-purple-100 dark:bg-purple-900/30 mr-2">
                                <i class="fas fa-clock text-purple-600 dark:text-purple-400 icon-bounce"></i>
                            </div>
                            <span class="font-medium">{{ $todayReservations->count() }} حجز قادم</span>
                        </div>
                    </div>

                    <!-- توزيع الحجوزات حسب الوقت -->
                    <div class="grid grid-cols-3 gap-3">
                        <!-- الصباح -->
                        <div class="text-center p-3 rounded-xl bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800 group hover:shadow-lg transition-all duration-300">
                            <div class="flex justify-center mb-2">
                                <div class="p-2 rounded-lg bg-gradient-to-br from-yellow-400 to-orange-400 text-white">
                                    <i class="fas fa-sun text-sm"></i>
                                </div>
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">صباحاً</div>
                            <div class="text-lg font-bold text-yellow-600 dark:text-yellow-400">{{ $morningReservations ?? 0 }}</div>
                            <div class="w-full bg-yellow-200 dark:bg-yellow-800 rounded-full h-1 mt-2">
                                <div class="bg-yellow-500 h-1 rounded-full transition-all duration-300" style="width: {{ ($morningReservations ?? 0) > 0 ? (($morningReservations ?? 0) / max(($todayStats['reservationsCount'] ?? 1), 1)) * 100 : 0 }}%"></div>
                            </div>
                        </div>

                        <!-- الظهر -->
                        <div class="text-center p-3 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 group hover:shadow-lg transition-all duration-300">
                            <div class="flex justify-center mb-2">
                                <div class="p-2 rounded-lg bg-gradient-to-br from-blue-400 to-indigo-400 text-white">
                                    <i class="fas fa-sun text-sm"></i>
                                </div>
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">ظهراً</div>
                            <div class="text-lg font-bold text-blue-600 dark:text-blue-400">{{ $afternoonReservations ?? 0 }}</div>
                            <div class="w-full bg-blue-200 dark:bg-blue-800 rounded-full h-1 mt-2">
                                <div class="bg-blue-500 h-1 rounded-full transition-all duration-300" style="width: {{ ($afternoonReservations ?? 0) > 0 ? (($afternoonReservations ?? 0) / max(($todayStats['reservationsCount'] ?? 1), 1)) * 100 : 0 }}%"></div>
                            </div>
                        </div>

                        <!-- المساء -->
                        <div class="text-center p-3 rounded-xl bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800 group hover:shadow-lg transition-all duration-300">
                            <div class="flex justify-center mb-2">
                                <div class="p-2 rounded-lg bg-gradient-to-br from-purple-400 to-pink-400 text-white">
                                    <i class="fas fa-moon text-sm"></i>
                                </div>
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">مساءً</div>
                            <div class="text-lg font-bold text-purple-600 dark:text-purple-400">{{ $eveningReservations ?? 0 }}</div>
                            <div class="w-full bg-purple-200 dark:bg-purple-800 rounded-full h-1 mt-2">
                                <div class="bg-purple-500 h-1 rounded-full transition-all duration-300" style="width: {{ ($eveningReservations ?? 0) > 0 ? (($eveningReservations ?? 0) / max(($todayStats['reservationsCount'] ?? 1), 1)) * 100 : 0 }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- زر الإجراء المبدع -->
        <div class="relative z-10 mt-6">
            <a href="{{ route('employee.reservations') }}" class="btn-magical inline-flex items-center px-4 py-2 gradient-luxury text-white rounded-xl font-medium text-sm shadow-luxury hover:shadow-xl transition-all duration-300 group">
                <span>عرض جميع الحجوزات</span>
                <i class="fas fa-arrow-left mr-2 group-hover:translate-x-1 transition-transform duration-300"></i>
            </a>
        </div>
    </div>

    <!-- بطاقة الطاولات المتاحة - تصميم مبدع -->
    <div class="relative bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700 card-float stat-card overflow-hidden group">
        <!-- خلفية متدرجة متحركة -->
        <div class="absolute inset-0 gradient-forest opacity-5 group-hover:opacity-10 transition-opacity duration-300"></div>

        <!-- شريط علوي ملون -->
        <div class="absolute top-0 left-0 right-0 h-1 gradient-forest"></div>

        <div class="relative z-10 flex items-center justify-between">
            <div class="flex-1">
                <div class="flex items-center">
                    <!-- أيقونة مبدعة مع تأثيرات -->
                    <div class="relative p-4 rounded-2xl bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 shadow-secondary group-hover:shadow-xl transition-all duration-300">
                        <div class="absolute inset-0 rounded-2xl bg-gradient-forest opacity-10"></div>
                        <i class="fas fa-chair text-green-600 dark:text-green-400 text-2xl icon-bounce relative z-10"></i>
                        <!-- تأثير التوهج -->
                        <div class="absolute inset-0 rounded-2xl border-2 border-green-300 dark:border-green-600 opacity-0 group-hover:opacity-50 transition-opacity duration-300"></div>
                    </div>
                    <div class="mr-4">
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">الطاولات المتاحة</p>
                        <p class="text-4xl font-bold text-gradient-ocean mb-2">{{ $tableStats['available'] ?? 0 }}</p>
                        <!-- شريط تقدم مبدع -->
                        <div class="w-16 h-1 gradient-forest rounded-full"></div>
                    </div>
                </div>

                <!-- قسم إحصائيات الطاولات -->
                <div class="mt-6">
                    @php
                        $availablePercentage = ($tableStats['total'] ?? 0) > 0 ? (($tableStats['available'] ?? 0) / ($tableStats['total'] ?? 1)) * 100 : 0;
                    @endphp

                    <!-- معلومات الطاولات -->
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center text-green-600 dark:text-green-400">
                            <div class="p-2 rounded-lg bg-green-100 dark:bg-green-900/30 mr-2">
                                <i class="fas fa-utensils text-green-600 dark:text-green-400"></i>
                            </div>
                            <span class="font-medium">من أصل {{ $tableStats['total'] ?? 0 }} طاولة</span>
                        </div>
                        <span class="text-lg font-bold text-green-600 dark:text-green-400">{{ round($availablePercentage) }}%</span>
                    </div>

                    <!-- شريط التقدم الدائري المبدع -->
                    <div class="relative mb-4">
                        <div class="flex items-center">
                            <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-3 overflow-hidden">
                                <div class="gradient-forest h-3 rounded-full transition-all duration-500 ease-out relative" style="width: {{ $availablePercentage }}%">
                                    <!-- تأثير اللمعان -->
                                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- توزيع الطاولات -->
                    <div class="grid grid-cols-3 gap-3">
                        <!-- متاحة -->
                        <div class="text-center p-3 rounded-xl bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800 group hover:shadow-lg transition-all duration-300">
                            <div class="flex justify-center mb-2">
                                <div class="p-2 rounded-lg bg-gradient-to-br from-green-400 to-emerald-400 text-white">
                                    <i class="fas fa-check text-sm"></i>
                                </div>
                            </div>
                            <div class="text-lg font-bold text-green-600 dark:text-green-400">{{ $tableStats['available'] ?? 0 }}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">متاحة</div>
                            <div class="w-full bg-green-200 dark:bg-green-800 rounded-full h-1 mt-2">
                                <div class="bg-green-500 h-1 rounded-full transition-all duration-300" style="width: {{ ($tableStats['available'] ?? 0) > 0 ? (($tableStats['available'] ?? 0) / max(($tableStats['total'] ?? 1), 1)) * 100 : 0 }}%"></div>
                            </div>
                        </div>

                        <!-- مشغولة -->
                        <div class="text-center p-3 rounded-xl bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 border border-red-200 dark:border-red-800 group hover:shadow-lg transition-all duration-300">
                            <div class="flex justify-center mb-2">
                                <div class="p-2 rounded-lg bg-gradient-to-br from-red-400 to-pink-400 text-white">
                                    <i class="fas fa-users text-sm"></i>
                                </div>
                            </div>
                            <div class="text-lg font-bold text-red-600 dark:text-red-400">{{ $tableStats['occupied'] ?? 0 }}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">مشغولة</div>
                            <div class="w-full bg-red-200 dark:bg-red-800 rounded-full h-1 mt-2">
                                <div class="bg-red-500 h-1 rounded-full transition-all duration-300" style="width: {{ ($tableStats['occupied'] ?? 0) > 0 ? (($tableStats['occupied'] ?? 0) / max(($tableStats['total'] ?? 1), 1)) * 100 : 0 }}%"></div>
                            </div>
                        </div>

                        <!-- محجوزة -->
                        <div class="text-center p-3 rounded-xl bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800 group hover:shadow-lg transition-all duration-300">
                            <div class="flex justify-center mb-2">
                                <div class="p-2 rounded-lg bg-gradient-to-br from-yellow-400 to-orange-400 text-white">
                                    <i class="fas fa-bookmark text-sm"></i>
                                </div>
                            </div>
                            <div class="text-lg font-bold text-yellow-600 dark:text-yellow-400">{{ $tableStats['reserved'] ?? 0 }}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">محجوزة</div>
                            <div class="w-full bg-yellow-200 dark:bg-yellow-800 rounded-full h-1 mt-2">
                                <div class="bg-yellow-500 h-1 rounded-full transition-all duration-300" style="width: {{ ($tableStats['reserved'] ?? 0) > 0 ? (($tableStats['reserved'] ?? 0) / max(($tableStats['total'] ?? 1), 1)) * 100 : 0 }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- زر الإجراء المبدع -->
        <div class="relative z-10 mt-6">
            <a href="{{ route('employee.tables') }}" class="btn-magical inline-flex items-center px-4 py-2 gradient-forest text-white rounded-xl font-medium text-sm shadow-secondary hover:shadow-xl transition-all duration-300 group">
                <span>عرض جميع الطاولات</span>
                <i class="fas fa-arrow-left mr-2 group-hover:translate-x-1 transition-transform duration-300"></i>
            </a>
        </div>
    </div>
</div>

<!-- إضافة مخطط مبيعات بسيط للموظف -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- مخطط المبيعات اليومية -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white">مبيعات اليوم</h3>
            <div class="text-sm text-gray-500 dark:text-gray-400">
                <i class="fas fa-chart-line mr-1"></i>
                آخر تحديث: {{ now()->format('H:i') }}
            </div>
        </div>
        <div id="employeeSalesChart" class="h-64"></div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white">إحصائيات سريعة</h3>
            <div class="text-sm text-gray-500 dark:text-gray-400">
                <i class="fas fa-clock mr-1"></i>
                {{ now()->format('Y-m-d') }}
            </div>
        </div>
        <div class="space-y-4">
            <div class="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div class="flex items-center">
                    <div class="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                        <i class="fas fa-shopping-cart text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="mr-3">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">متوسط قيمة الطلب</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">لهذا اليوم</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-lg font-bold text-blue-600 dark:text-blue-400">
                        {{ $todayStats['ordersCount'] > 0 ? number_format(($todayStats['totalSales'] ?? 0) / $todayStats['ordersCount'], 2) : '0.00' }} د.ل
                    </p>
                </div>
            </div>

            <div class="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div class="flex items-center">
                    <div class="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                        <i class="fas fa-users text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="mr-3">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">العملاء المخدومين</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">اليوم</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-lg font-bold text-green-600 dark:text-green-400">{{ $todayStats['ordersCount'] ?? 0 }}</p>
                </div>
            </div>

            <div class="flex items-center justify-between p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <div class="flex items-center">
                    <div class="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
                        <i class="fas fa-clock text-purple-600 dark:text-purple-400"></i>
                    </div>
                    <div class="mr-3">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">ساعات العمل</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">اليوم</p>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-lg font-bold text-purple-600 dark:text-purple-400">8 ساعات</p>
                </div>
            </div>
        </div>
    </div>
</div>