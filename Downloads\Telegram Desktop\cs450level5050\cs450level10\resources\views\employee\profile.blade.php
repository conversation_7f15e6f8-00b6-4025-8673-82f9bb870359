@extends('employee.layouts.app')

@section('title', 'الملف الشخصي - نظام إدارة المطعم')

@section('content')
<!-- خلفية متحركة مبدعة -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 relative overflow-hidden">
    <!-- عناصر زخرفية متحركة -->
    <div class="absolute inset-0 overflow-hidden">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full animate-pulse"></div>
        <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-r from-indigo-400/20 to-pink-400/20 rounded-full animate-bounce"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-purple-400/10 to-blue-400/10 rounded-full animate-spin" style="animation-duration: 20s;"></div>
    </div>

    <div class="relative z-10 container mx-auto px-4 py-8">
        <!-- Header مبدع -->
        <!-- <div class="text-center mb-12">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full shadow-2xl mb-6 animate-bounce">
                <i class="fas fa-user-circle text-3xl text-white"></i>
            </div>
            <h1 class="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-4">
                الملف الشخصي المتقدم
            </h1>
            <p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                🌟 مرحباً بك في ملفك الشخصي المتطور - اكتشف معلوماتك وحدثها بسهولة
            </p>
            <div class="w-32 h-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mx-auto mt-4"></div>
        </div> -->

        <!-- رسائل التنبيه المبدعة -->
        @if(session('success'))
        <div class="max-w-4xl mx-auto mb-8">
            <div class="bg-gradient-to-r from-green-400 to-emerald-500 text-white p-6 rounded-2xl shadow-2xl transform hover:scale-105 transition-all duration-300 border-l-8 border-green-600">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-2xl animate-bounce"></i>
                    </div>
                    <div class="mr-4">
                        <h3 class="text-lg font-bold">تم بنجاح! 🎉</h3>
                        <p class="text-green-100">{{ session('success') }}</p>
                    </div>
                </div>
            </div>
        </div>
        @endif

        @if(session('error'))
        <div class="max-w-4xl mx-auto mb-8">
            <div class="bg-gradient-to-r from-red-400 to-pink-500 text-white p-6 rounded-2xl shadow-2xl transform hover:scale-105 transition-all duration-300 border-l-8 border-red-600">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-2xl animate-pulse"></i>
                    </div>
                    <div class="mr-4">
                        <h3 class="text-lg font-bold">تنبيه! ⚠️</h3>
                        <p class="text-red-100">{{ session('error') }}</p>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <div class="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- بطاقة الملف الشخصي المبدعة -->
            <div class="lg:col-span-1">
                <!-- البطاقة الرئيسية -->
                <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg rounded-3xl shadow-2xl overflow-hidden border border-white/20 dark:border-gray-700/50 transform hover:scale-105 transition-all duration-500">
                    <!-- Header البطاقة -->
                    <div class="relative bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-700 p-8 text-center">
                        <!-- زخارف خلفية -->
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20"></div>
                        <div class="absolute top-4 right-4 w-16 h-16 bg-white/10 rounded-full animate-pulse"></div>
                        <div class="absolute bottom-4 left-4 w-12 h-12 bg-white/10 rounded-full animate-bounce"></div>

                        <!-- صورة المستخدم -->
                        <div class="relative z-10">
                            <div class="w-32 h-32 mx-auto bg-white/20 rounded-full flex items-center justify-center overflow-hidden mb-4 shadow-2xl border-4 border-white/30 transform hover:rotate-6 transition-all duration-300">
                                @if($user->profile_image)
                                <img src="{{ asset('storage/' . $user->profile_image) }}" alt="{{ $user->first_name }}" class="w-full h-full object-cover">
                                @else
                                <div class="text-5xl text-white/80">
                                    <i class="fas fa-user-circle"></i>
                                </div>
                                @endif
                            </div>

                            <!-- معلومات المستخدم -->
                            <h2 class="text-2xl font-bold text-white mb-2">{{ $user->first_name }} {{ $user->last_name }}</h2>
                            <div class="inline-flex items-center px-4 py-2 bg-white/20 rounded-full text-white/90 text-sm font-medium backdrop-blur-sm">
                                <i class="fas fa-user-tie ml-2"></i>
                                {{ $user->user_type == 'admin' ? '👑 مدير النظام' : '👨‍💼 موظف مطعم' }}
                            </div>
                        </div>
                    </div>

                    <!-- معلومات الاتصال -->
                    <div class="p-6 space-y-4">
                        <div class="flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl border border-blue-200/50 dark:border-blue-700/50 transform hover:scale-105 transition-all duration-300">
                            <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg">
                                <i class="fas fa-envelope text-white"></i>
                            </div>
                            <div class="mr-4 flex-1">
                                <p class="text-sm text-gray-500 dark:text-gray-400">البريد الإلكتروني</p>
                                <p class="font-medium text-gray-800 dark:text-white truncate">{{ $user->email }}</p>
                            </div>
                        </div>

                        <div class="flex items-center p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-2xl border border-green-200/50 dark:border-green-700/50 transform hover:scale-105 transition-all duration-300">
                            <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg">
                                <i class="fas fa-phone text-white"></i>
                            </div>
                            <div class="mr-4 flex-1">
                                <p class="text-sm text-gray-500 dark:text-gray-400">رقم الهاتف</p>
                                <p class="font-medium text-gray-800 dark:text-white">{{ $user->phone ?? 'غير محدد' }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- إحصائيات مبدعة -->
                    <div class="bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-700 dark:to-gray-600 p-6 border-t border-gray-200/50 dark:border-gray-600/50">
                        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4 text-center">📊 إحصائيات سريعة</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center p-4 bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-blue-200/50 dark:border-blue-700/50 transform hover:scale-105 transition-all duration-300">
                                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center mx-auto mb-2 shadow-lg">
                                    <i class="fas fa-calendar-alt text-white"></i>
                                </div>
                                <div class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">{{ $user->created_at->diffInDays() }}</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400 font-medium">يوم في الفريق</div>
                            </div>
                            <div class="text-center p-4 bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-green-200/50 dark:border-green-700/50 transform hover:scale-105 transition-all duration-300">
                                <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-2 shadow-lg">
                                    <i class="fas fa-clock text-white"></i>
                                </div>
                                <div class="text-sm font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                                    {{ $user->last_login ? $user->last_login->diffForHumans() : 'أول مرة' }}
                                </div>
                                <div class="text-xs text-gray-500 dark:text-gray-400 font-medium">آخر نشاط</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- بطاقة الروابط السريعة المبدعة -->
                <div class="mt-8 bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg rounded-3xl shadow-2xl overflow-hidden border border-white/20 dark:border-gray-700/50 transform hover:scale-105 transition-all duration-500">
                    <div class="bg-gradient-to-r from-purple-500 to-pink-600 p-6 text-center">
                        <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                            <i class="fas fa-rocket text-2xl text-white"></i>
                        </div>
                        <h2 class="text-xl font-bold text-white">🚀 الوصول السريع</h2>
                        <p class="text-purple-100 text-sm mt-2">اختصارات مفيدة لتسهيل عملك</p>
                    </div>
                    <div class="p-6 space-y-3">
                        <a href="{{ route('employee.dashboard') }}" class="flex items-center p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl border border-blue-200/50 dark:border-blue-700/50 hover:shadow-lg transform hover:scale-105 transition-all duration-300 group">
                            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-tachometer-alt text-white"></i>
                            </div>
                            <div class="mr-4">
                                <span class="font-medium text-gray-800 dark:text-white">لوحة التحكم</span>
                                <p class="text-sm text-gray-500 dark:text-gray-400">العودة للصفحة الرئيسية</p>
                            </div>
                            <i class="fas fa-chevron-left text-gray-400 mr-auto group-hover:text-blue-500 transition-colors duration-300"></i>
                        </a>

                        <a href="{{ route('employee.orders') }}" class="flex items-center p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-2xl border border-green-200/50 dark:border-green-700/50 hover:shadow-lg transform hover:scale-105 transition-all duration-300 group">
                            <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-shopping-cart text-white"></i>
                            </div>
                            <div class="mr-4">
                                <span class="font-medium text-gray-800 dark:text-white">إدارة الطلبات</span>
                                <p class="text-sm text-gray-500 dark:text-gray-400">عرض ومتابعة الطلبات</p>
                            </div>
                            <i class="fas fa-chevron-left text-gray-400 mr-auto group-hover:text-green-500 transition-colors duration-300"></i>
                        </a>

                        <a href="{{ route('employee.menu') }}" class="flex items-center p-4 bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-2xl border border-orange-200/50 dark:border-orange-700/50 hover:shadow-lg transform hover:scale-105 transition-all duration-300 group">
                            <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-utensils text-white"></i>
                            </div>
                            <div class="mr-4">
                                <span class="font-medium text-gray-800 dark:text-white">قائمة الطعام</span>
                                <p class="text-sm text-gray-500 dark:text-gray-400">استعراض المنتجات</p>
                            </div>
                            <i class="fas fa-chevron-left text-gray-400 mr-auto group-hover:text-orange-500 transition-colors duration-300"></i>
                        </a>

                        <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
                            <form method="POST" action="{{ route('logout') }}" class="w-full">
                                @csrf
                                <button type="submit" class="flex items-center w-full p-4 bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-2xl border border-red-200/50 dark:border-red-700/50 hover:shadow-lg transform hover:scale-105 transition-all duration-300 group">
                                    <div class="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                                        <i class="fas fa-sign-out-alt text-white"></i>
                                    </div>
                                    <div class="mr-4 text-right">
                                        <span class="font-medium text-gray-800 dark:text-white">تسجيل الخروج</span>
                                        <p class="text-sm text-gray-500 dark:text-gray-400">إنهاء الجلسة الحالية</p>
                                    </div>
                                    <i class="fas fa-chevron-left text-gray-400 mr-auto group-hover:text-red-500 transition-colors duration-300"></i>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- نموذج تحديث الملف الشخصي المبدع -->
            <div class="lg:col-span-2">
                <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-lg rounded-3xl shadow-2xl overflow-hidden border border-white/20 dark:border-gray-700/50">
                    <!-- Header النموذج -->
                    <div class="relative bg-gradient-to-r from-indigo-500 via-purple-600 to-pink-600 p-8">
                        <!-- زخارف خلفية -->
                        <div class="absolute inset-0 bg-gradient-to-r from-indigo-600/20 to-pink-600/20"></div>
                        <div class="absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full animate-pulse"></div>
                        <div class="absolute bottom-4 left-4 w-16 h-16 bg-white/10 rounded-full animate-bounce"></div>

                        <div class="relative z-10 text-center">
                            <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                                <i class="fas fa-edit text-2xl text-white"></i>
                            </div>
                            <h2 class="text-3xl font-bold text-white mb-2">✨ تحديث الملف الشخصي</h2>
                            <p class="text-indigo-100">قم بتحديث معلوماتك الشخصية وكلمة المرور بسهولة</p>
                        </div>
                    </div>

                    <!-- النموذج -->
                    <form action="{{ route('employee.profile.update') }}" method="POST" enctype="multipart/form-data" class="p-8 space-y-8">
                        @csrf
                        @method('PUT')

                        <!-- قسم المعلومات الأساسية -->
                        <div class="space-y-6">
                            <div class="flex items-center mb-6">
                                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg mr-4">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                <div>
                                    <h3 class="text-xl font-bold text-gray-800 dark:text-white">المعلومات الأساسية</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">تحديث الاسم ومعلومات الاتصال</p>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="space-y-2">
                                    <label for="first_name" class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                                        <i class="fas fa-user ml-2 text-blue-500"></i>
                                        الاسم الأول
                                    </label>
                                    <div class="relative">
                                        <input type="text" id="first_name" name="first_name" value="{{ old('first_name', $user->first_name) }}"
                                               class="w-full px-4 py-3 pr-12 border-2 border-gray-200 dark:border-gray-600 rounded-2xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-all duration-300 @error('first_name') border-red-500 dark:border-red-500 @enderror"
                                               placeholder="أدخل الاسم الأول">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <i class="fas fa-user text-gray-400"></i>
                                        </div>
                                    </div>
                                    @error('first_name')
                                    <p class="mt-1 text-sm text-red-500 flex items-center">
                                        <i class="fas fa-exclamation-circle ml-1"></i>
                                        {{ $message }}
                                    </p>
                                    @enderror
                                </div>

                                <div class="space-y-2">
                                    <label for="last_name" class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                                        <i class="fas fa-user ml-2 text-green-500"></i>
                                        الاسم الأخير
                                    </label>
                                    <div class="relative">
                                        <input type="text" id="last_name" name="last_name" value="{{ old('last_name', $user->last_name) }}"
                                               class="w-full px-4 py-3 pr-12 border-2 border-gray-200 dark:border-gray-600 rounded-2xl shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white transition-all duration-300 @error('last_name') border-red-500 dark:border-red-500 @enderror"
                                               placeholder="أدخل الاسم الأخير">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <i class="fas fa-user text-gray-400"></i>
                                        </div>
                                    </div>
                                    @error('last_name')
                                    <p class="mt-1 text-sm text-red-500 flex items-center">
                                        <i class="fas fa-exclamation-circle ml-1"></i>
                                        {{ $message }}
                                    </p>
                                    @enderror
                                </div>

                                <div class="space-y-2">
                                    <label for="email" class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                                        <i class="fas fa-envelope ml-2 text-purple-500"></i>
                                        البريد الإلكتروني
                                    </label>
                                    <div class="relative">
                                        <input type="email" id="email" name="email" value="{{ old('email', $user->email) }}"
                                               class="w-full px-4 py-3 pr-12 border-2 border-gray-200 dark:border-gray-600 rounded-2xl shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:text-white transition-all duration-300 @error('email') border-red-500 dark:border-red-500 @enderror"
                                               placeholder="أدخل البريد الإلكتروني">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <i class="fas fa-envelope text-gray-400"></i>
                                        </div>
                                    </div>
                                    @error('email')
                                    <p class="mt-1 text-sm text-red-500 flex items-center">
                                        <i class="fas fa-exclamation-circle ml-1"></i>
                                        {{ $message }}
                                    </p>
                                    @enderror
                                </div>

                                <div class="space-y-2">
                                    <label for="phone" class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                                        <i class="fas fa-phone ml-2 text-orange-500"></i>
                                        رقم الهاتف
                                    </label>
                                    <div class="relative">
                                        <input type="text" id="phone" name="phone" value="{{ old('phone', $user->phone) }}"
                                               class="w-full px-4 py-3 pr-12 border-2 border-gray-200 dark:border-gray-600 rounded-2xl shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 dark:bg-gray-700 dark:text-white transition-all duration-300 @error('phone') border-red-500 dark:border-red-500 @enderror"
                                               placeholder="أدخل رقم الهاتف">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                            <i class="fas fa-phone text-gray-400"></i>
                                        </div>
                                    </div>
                                    @error('phone')
                                    <p class="mt-1 text-sm text-red-500 flex items-center">
                                        <i class="fas fa-exclamation-circle ml-1"></i>
                                        {{ $message }}
                                    </p>
                                    @enderror
                                </div>
                            </div>

                            <!-- قسم صورة الملف الشخصي -->
                            <div class="md:col-span-2 mt-8">
                                <div class="flex items-center mb-6">
                                    <div class="w-12 h-12 bg-gradient-to-r from-pink-500 to-red-600 rounded-full flex items-center justify-center shadow-lg mr-4">
                                        <i class="fas fa-camera text-white"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-xl font-bold text-gray-800 dark:text-white">صورة الملف الشخصي</h3>
                                        <p class="text-sm text-gray-600 dark:text-gray-400">اختر صورة شخصية جديدة</p>
                                    </div>
                                </div>

                                <div class="bg-gradient-to-r from-pink-50 to-red-50 dark:from-pink-900/20 dark:to-red-900/20 p-6 rounded-2xl border-2 border-dashed border-pink-300 dark:border-pink-700">
                                    <div class="flex items-center space-x-6 space-x-reverse">
                                        <!-- معاينة الصورة الحالية -->
                                        <div class="flex-shrink-0">
                                            <div class="w-24 h-24 rounded-full overflow-hidden bg-gradient-to-r from-pink-200 to-red-200 dark:from-pink-800 dark:to-red-800 shadow-lg border-4 border-white dark:border-gray-700">
                                                @if($user->profile_image)
                                                <img src="{{ asset('storage/' . $user->profile_image) }}" alt="{{ $user->first_name }}" class="w-full h-full object-cover">
                                                @else
                                                <div class="w-full h-full flex items-center justify-center text-pink-600 dark:text-pink-400">
                                                    <i class="fas fa-user text-3xl"></i>
                                                </div>
                                                @endif
                                            </div>
                                        </div>

                                        <!-- رفع الصورة -->
                                        <div class="flex-1">
                                            <label for="profile_image" class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                                <i class="fas fa-upload ml-2 text-pink-500"></i>
                                                اختر صورة جديدة
                                            </label>
                                            <input type="file" id="profile_image" name="profile_image" accept="image/*"
                                                   class="w-full px-4 py-3 border-2 border-gray-200 dark:border-gray-600 rounded-2xl shadow-sm focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-pink-500 dark:bg-gray-700 dark:text-white transition-all duration-300 @error('profile_image') border-red-500 dark:border-red-500 @enderror">
                                            <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                                                <i class="fas fa-info-circle ml-1"></i>
                                                يُفضل استخدام صور بحجم 400x400 بكسل أو أكبر
                                            </p>
                                        </div>
                                    </div>
                                    @error('profile_image')
                                    <p class="mt-3 text-sm text-red-500 flex items-center">
                                        <i class="fas fa-exclamation-circle ml-1"></i>
                                        {{ $message }}
                                    </p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- قسم تغيير كلمة المرور -->
                        <div class="border-t-2 border-gray-200 dark:border-gray-700 pt-8 mt-8">
                            <div class="flex items-center mb-6">
                                <div class="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-600 rounded-full flex items-center justify-center shadow-lg mr-4">
                                    <i class="fas fa-lock text-white"></i>
                                </div>
                                <div>
                                    <h3 class="text-xl font-bold text-gray-800 dark:text-white">🔐 تغيير كلمة المرور</h3>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">اترك الحقول فارغة إذا كنت لا ترغب في تغيير كلمة المرور</p>
                                </div>
                            </div>

                            <div class="bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 p-6 rounded-2xl border border-red-200/50 dark:border-red-700/50 space-y-6">
                                <!-- كلمة المرور الحالية -->
                                <div class="space-y-2">
                                    <label for="current_password" class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                                        <i class="fas fa-key ml-2 text-red-500"></i>
                                        كلمة المرور الحالية
                                    </label>
                                    <div class="relative">
                                        <input type="password" id="current_password" name="current_password"
                                               class="w-full px-4 py-3 pr-12 border-2 border-gray-200 dark:border-gray-600 rounded-2xl shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 dark:bg-gray-700 dark:text-white transition-all duration-300 @error('current_password') border-red-500 dark:border-red-500 @enderror"
                                               placeholder="أدخل كلمة المرور الحالية">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                            <button type="button" onclick="togglePassword('current_password')" class="text-gray-400 hover:text-gray-600 focus:outline-none">
                                                <i class="fas fa-eye" id="current_password_eye"></i>
                                            </button>
                                        </div>
                                    </div>
                                    @error('current_password')
                                    <p class="mt-1 text-sm text-red-500 flex items-center">
                                        <i class="fas fa-exclamation-circle ml-1"></i>
                                        {{ $message }}
                                    </p>
                                    @enderror
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- كلمة المرور الجديدة -->
                                    <div class="space-y-2">
                                        <label for="password" class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                                            <i class="fas fa-lock ml-2 text-green-500"></i>
                                            كلمة المرور الجديدة
                                        </label>
                                        <div class="relative">
                                            <input type="password" id="password" name="password"
                                                   class="w-full px-4 py-3 pr-12 border-2 border-gray-200 dark:border-gray-600 rounded-2xl shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 dark:bg-gray-700 dark:text-white transition-all duration-300 @error('password') border-red-500 dark:border-red-500 @enderror"
                                                   placeholder="أدخل كلمة المرور الجديدة">
                                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                                <button type="button" onclick="togglePassword('password')" class="text-gray-400 hover:text-gray-600 focus:outline-none">
                                                    <i class="fas fa-eye" id="password_eye"></i>
                                                </button>
                                            </div>
                                        </div>
                                        @error('password')
                                        <p class="mt-1 text-sm text-red-500 flex items-center">
                                            <i class="fas fa-exclamation-circle ml-1"></i>
                                            {{ $message }}
                                        </p>
                                        @enderror
                                    </div>

                                    <!-- تأكيد كلمة المرور -->
                                    <div class="space-y-2">
                                        <label for="password_confirmation" class="flex items-center text-sm font-medium text-gray-700 dark:text-gray-300">
                                            <i class="fas fa-check-circle ml-2 text-blue-500"></i>
                                            تأكيد كلمة المرور
                                        </label>
                                        <div class="relative">
                                            <input type="password" id="password_confirmation" name="password_confirmation"
                                                   class="w-full px-4 py-3 pr-12 border-2 border-gray-200 dark:border-gray-600 rounded-2xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-all duration-300"
                                                   placeholder="أعد إدخال كلمة المرور الجديدة">
                                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                                <button type="button" onclick="togglePassword('password_confirmation')" class="text-gray-400 hover:text-gray-600 focus:outline-none">
                                                    <i class="fas fa-eye" id="password_confirmation_eye"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- نصائح الأمان -->
                                <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-xl border border-blue-200 dark:border-blue-700">
                                    <div class="flex items-start">
                                        <i class="fas fa-shield-alt text-blue-500 mt-1 ml-2"></i>
                                        <div>
                                            <h4 class="text-sm font-medium text-blue-800 dark:text-blue-300 mb-2">نصائح لكلمة مرور قوية:</h4>
                                            <ul class="text-xs text-blue-700 dark:text-blue-400 space-y-1">
                                                <li>• استخدم على الأقل 8 أحرف</li>
                                                <li>• امزج بين الأحرف الكبيرة والصغيرة</li>
                                                <li>• أضف أرقام ورموز خاصة</li>
                                                <li>• تجنب المعلومات الشخصية</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                        <!-- أزرار الحفظ -->
                        <div class="flex flex-col sm:flex-row gap-4 justify-end pt-8 border-t-2 border-gray-200 dark:border-gray-700">
                            <button type="button" onclick="resetForm()" class="px-8 py-4 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-2xl hover:from-gray-600 hover:to-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-300 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center">
                                <i class="fas fa-undo ml-2"></i>
                                إعادة تعيين
                            </button>
                            <button type="submit" class="px-8 py-4 bg-gradient-to-r from-blue-500 via-purple-600 to-indigo-600 text-white rounded-2xl hover:from-blue-600 hover:via-purple-700 hover:to-indigo-700 focus:outline-none focus:ring-4 focus:ring-blue-300 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center">
                                <i class="fas fa-save ml-2"></i>
                                💾 حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript للتفاعل -->
<script>
    // تبديل إظهار/إخفاء كلمة المرور
    function togglePassword(fieldId) {
        const field = document.getElementById(fieldId);
        const eye = document.getElementById(fieldId + '_eye');

        if (field.type === 'password') {
            field.type = 'text';
            eye.classList.remove('fa-eye');
            eye.classList.add('fa-eye-slash');
        } else {
            field.type = 'password';
            eye.classList.remove('fa-eye-slash');
            eye.classList.add('fa-eye');
        }
    }

    // إعادة تعيين النموذج
    function resetForm() {
        if (confirm('هل أنت متأكد من إعادة تعيين جميع التغييرات؟')) {
            document.querySelector('form').reset();
        }
    }

    // تأثيرات بصرية للحقول
    document.addEventListener('DOMContentLoaded', function() {
        const inputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="password"]');

        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('transform', 'scale-105');
            });

            input.addEventListener('blur', function() {
                this.parentElement.classList.remove('transform', 'scale-105');
            });
        });

        // تأثير الكتابة
        inputs.forEach(input => {
            input.addEventListener('input', function() {
                if (this.value.length > 0) {
                    this.classList.add('border-green-400', 'dark:border-green-500');
                } else {
                    this.classList.remove('border-green-400', 'dark:border-green-500');
                }
            });
        });
    });
</script>

<style>
    /* تأثيرات CSS إضافية */
    .animate-gradient {
        background-size: 200% 200%;
        animation: gradient 3s ease infinite;
    }

    @keyframes gradient {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    /* تأثير التمرير السلس */
    html {
        scroll-behavior: smooth;
    }

    /* تحسين الانتقالات */
    * {
        transition: all 0.3s ease;
    }
</style>

@endsection
