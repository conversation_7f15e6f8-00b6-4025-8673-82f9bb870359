@extends('layouts.admin')

@section('title', 'إدارة الأدوار')

@section('content')
<div class="mb-6">
    <div class="flex justify-between items-center">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white">إدارة الأدوار</h2>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">إنشاء وتعديل الأدوار وصلاحياتها</p>
        </div>
        <div class="flex space-x-2 space-x-reverse">
            <a href="{{ route('admin.permissions.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-md flex items-center transition-all">
                <i class="fas fa-arrow-right ml-2"></i>
                <span>العودة</span>
            </a>
            <button onclick="toggleCreateForm()" class="bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md flex items-center transition-all">
                <i class="fas fa-plus ml-2"></i>
                <span>إضافة دور جديد</span>
            </button>
        </div>
    </div>
</div>

<!-- نموذج إضافة دور جديد -->
<div id="createRoleForm" class="hidden bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">إضافة دور جديد</h3>
    <form action="{{ route('admin.permissions.create-role') }}" method="POST">
        @csrf
        <div class="mb-4">
            <label for="role_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">اسم الدور</label>
            <input type="text" id="role_name" name="name" required 
                   class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
        </div>
        
        <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الصلاحيات</label>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                @foreach($permissions as $group => $groupPermissions)
                <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-3">
                    <h4 class="font-medium text-gray-800 dark:text-white mb-2 cursor-pointer" onclick="toggleGroupPermissions('create_{{ $group }}')">
                        @switch($group)
                            @case('users') إدارة المستخدمين @break
                            @case('menu') إدارة القائمة @break
                            @case('orders') إدارة الطلبات @break
                            @case('reservations') إدارة الحجوزات @break
                            @case('inventory') إدارة المخزون @break
                            @case('ingredients') إدارة المكونات @break
                            @case('expenses') إدارة المصروفات @break
                            @case('reports') التقارير @break
                            @case('tables') إدارة الطاولات @break
                            @case('payments') إدارة المدفوعات @break
                            @case('notifications') إدارة الإشعارات @break
                            @case('settings') الإعدادات @break
                            @case('dashboard') لوحة التحكم @break
                            @default {{ $group }} @break
                        @endswitch
                        <i class="fas fa-chevron-down text-xs"></i>
                    </h4>
                    <div id="create_{{ $group }}" class="space-y-1">
                        @foreach($groupPermissions as $permission)
                        <label class="flex items-center text-sm">
                            <input type="checkbox" name="permissions[]" value="{{ $permission->name }}" 
                                   class="h-3 w-3 text-primary focus:ring-primary border-gray-300 rounded">
                            <span class="mr-2 text-gray-700 dark:text-gray-300">
                                @switch(explode('.', $permission->name)[1])
                                    @case('view') عرض @break
                                    @case('create') إضافة @break
                                    @case('edit') تعديل @break
                                    @case('delete') حذف @break
                                    @case('status') تغيير الحالة @break
                                    @case('export') تصدير @break
                                    @case('send') إرسال @break
                                    @case('permissions') إدارة الصلاحيات @break
                                    @case('admin') المدير @break
                                    @case('employee') الموظف @break
                                    @case('financial') مالية @break
                                    @case('sales') مبيعات @break
                                    @case('inventory') مخزون @break
                                    @default {{ explode('.', $permission->name)[1] }} @break
                                @endswitch
                            </span>
                        </label>
                        @endforeach
                    </div>
                </div>
                @endforeach
            </div>
        </div>

        <div class="flex justify-end space-x-2 space-x-reverse">
            <button type="button" onclick="toggleCreateForm()" 
                    class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">
                إلغاء
            </button>
            <button type="submit" 
                    class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-all">
                <i class="fas fa-save ml-2"></i>
                إنشاء الدور
            </button>
        </div>
    </form>
</div>

<!-- قائمة الأدوار -->
<div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
    @foreach($roles as $role)
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="p-6">
            <div class="flex justify-between items-start mb-4">
                <div>
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white">{{ $role->name }}</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400">{{ $role->permissions->count() }} صلاحية</p>
                </div>
                @if(!in_array($role->name, ['admin', 'employee', 'manager']))
                <form action="{{ route('admin.permissions.delete-role', $role->id) }}" method="POST" 
                      onsubmit="return confirm('هل أنت متأكد من حذف هذا الدور؟')">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="text-red-500 hover:text-red-700">
                        <i class="fas fa-trash"></i>
                    </button>
                </form>
                @endif
            </div>

            <div class="mb-4">
                <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الصلاحيات:</h4>
                <div class="max-h-32 overflow-y-auto">
                    @if($role->permissions->count() > 0)
                        <div class="flex flex-wrap gap-1">
                            @foreach($role->permissions->take(6) as $permission)
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                {{ explode('.', $permission->name)[1] }}
                            </span>
                            @endforeach
                            @if($role->permissions->count() > 6)
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200">
                                +{{ $role->permissions->count() - 6 }}
                            </span>
                            @endif
                        </div>
                    @else
                        <p class="text-sm text-gray-500 dark:text-gray-400">لا توجد صلاحيات</p>
                    @endif
                </div>
            </div>

            <button onclick="toggleRoleForm('role_{{ $role->id }}')" 
                    class="w-full bg-secondary hover:bg-secondary/90 text-white py-2 px-4 rounded-md transition-all">
                <i class="fas fa-edit ml-2"></i>
                تعديل الصلاحيات
            </button>
        </div>

        <!-- نموذج تعديل الدور -->
        <div id="role_{{ $role->id }}" class="hidden border-t border-gray-200 dark:border-gray-700 p-6">
            <form action="{{ route('admin.permissions.update-role', $role->id) }}" method="POST">
                @csrf
                @method('PUT')
                <div class="grid grid-cols-1 gap-3 max-h-64 overflow-y-auto">
                    @foreach($permissions as $group => $groupPermissions)
                    <div class="border border-gray-200 dark:border-gray-600 rounded p-2">
                        <h5 class="font-medium text-sm text-gray-800 dark:text-white mb-1 cursor-pointer" 
                            onclick="toggleGroupPermissions('edit_{{ $role->id }}_{{ $group }}')">
                            @switch($group)
                                @case('users') إدارة المستخدمين @break
                                @case('menu') إدارة القائمة @break
                                @case('orders') إدارة الطلبات @break
                                @case('reservations') إدارة الحجوزات @break
                                @case('inventory') إدارة المخزون @break
                                @case('ingredients') إدارة المكونات @break
                                @case('expenses') إدارة المصروفات @break
                                @case('reports') التقارير @break
                                @case('tables') إدارة الطاولات @break
                                @case('payments') إدارة المدفوعات @break
                                @case('notifications') إدارة الإشعارات @break
                                @case('settings') الإعدادات @break
                                @case('dashboard') لوحة التحكم @break
                                @default {{ $group }} @break
                            @endswitch
                        </h5>
                        <div id="edit_{{ $role->id }}_{{ $group }}" class="space-y-1">
                            @foreach($groupPermissions as $permission)
                            <label class="flex items-center text-xs">
                                <input type="checkbox" name="permissions[]" value="{{ $permission->name }}" 
                                       {{ $role->hasPermissionTo($permission->name) ? 'checked' : '' }}
                                       class="h-3 w-3 text-primary focus:ring-primary border-gray-300 rounded">
                                <span class="mr-1 text-gray-700 dark:text-gray-300">
                                    @switch(explode('.', $permission->name)[1])
                                        @case('view') عرض @break
                                        @case('create') إضافة @break
                                        @case('edit') تعديل @break
                                        @case('delete') حذف @break
                                        @case('status') تغيير الحالة @break
                                        @case('export') تصدير @break
                                        @case('send') إرسال @break
                                        @case('permissions') إدارة الصلاحيات @break
                                        @case('admin') المدير @break
                                        @case('employee') الموظف @break
                                        @case('financial') مالية @break
                                        @case('sales') مبيعات @break
                                        @case('inventory') مخزون @break
                                        @default {{ explode('.', $permission->name)[1] }} @break
                                    @endswitch
                                </span>
                            </label>
                            @endforeach
                        </div>
                    </div>
                    @endforeach
                </div>

                <div class="flex justify-end space-x-2 space-x-reverse mt-4">
                    <button type="button" onclick="toggleRoleForm('role_{{ $role->id }}')" 
                            class="px-3 py-1 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded text-sm hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">
                        إلغاء
                    </button>
                    <button type="submit" 
                            class="px-3 py-1 bg-primary text-white rounded text-sm hover:bg-primary/90 transition-all">
                        حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>
    @endforeach
</div>
@endsection

@section('scripts')
<script>
    function toggleCreateForm() {
        const form = document.getElementById('createRoleForm');
        form.classList.toggle('hidden');
    }

    function toggleRoleForm(roleId) {
        const form = document.getElementById(roleId);
        form.classList.toggle('hidden');
    }

    function toggleGroupPermissions(groupId) {
        const group = document.getElementById(groupId);
        const checkboxes = group.querySelectorAll('input[type="checkbox"]');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);
        
        checkboxes.forEach(cb => {
            cb.checked = !allChecked;
        });
    }
</script>
@endsection
