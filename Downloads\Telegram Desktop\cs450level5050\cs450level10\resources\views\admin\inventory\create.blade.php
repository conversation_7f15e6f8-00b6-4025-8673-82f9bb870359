@extends('layouts.admin')

@section('title', 'إضافة مخزون جديد')

@section('content')
<div class="mb-6">
    <h2 class="text-2xl font-bold text-gray-800 dark:text-white">إضافة مخزون جديد</h2>
    <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">أضف كمية جديدة من المكونات إلى المخزون</p>
</div>

<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
    <div class="p-6">
        <form action="{{ route('admin.inventory.store') }}" method="POST">
            @csrf

            <div class="mb-6">
                <label for="ingredient_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    المكون <span class="text-red-500">*</span>
                </label>
                <select id="ingredient_id" name="ingredient_id" required class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary transition-all">
                    <option value="">اختر المكون</option>
                    @foreach($ingredients as $ingredient)
                        <option value="{{ $ingredient->ingredient_id }}" {{ old('ingredient_id') == $ingredient->ingredient_id || (isset($selectedIngredient) && $selectedIngredient == $ingredient->ingredient_id) ? 'selected' : '' }}>
                            {{ $ingredient->name }} ({{ $ingredient->unit }})
                        </option>
                    @endforeach
                </select>
                @error('ingredient_id')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-6">
                <label for="quantity" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    الكمية <span class="text-red-500">*</span>
                </label>
                <div class="flex">
                    <input type="number" id="quantity" name="quantity" value="{{ old('quantity') }}" step="0.01" min="0.01" required class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary transition-all">
                    <span id="unit-display" class="inline-flex items-center px-3 py-2 text-gray-500 bg-gray-100 dark:bg-gray-600 dark:text-gray-300 border border-r-0 border-gray-300 dark:border-gray-600 rounded-l-md">
                        وحدة
                    </span>
                </div>
                @error('quantity')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-6">
                <label for="cost_per_unit" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    التكلفة للوحدة <span class="text-red-500">*</span>
                </label>
                <div class="flex">
                    <input type="number" id="cost_per_unit" name="cost_per_unit" value="{{ old('cost_per_unit') }}" step="0.01" min="0.01" required class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary transition-all">
                    <span class="inline-flex items-center px-3 py-2 text-gray-500 bg-gray-100 dark:bg-gray-600 dark:text-gray-300 border border-r-0 border-gray-300 dark:border-gray-600 rounded-l-md">
                        دينار
                    </span>
                </div>
                @error('cost_per_unit')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                @enderror
            </div>

            <div class="mb-6">
                <label for="expiry_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ الانتهاء (اختياري)</label>
                <input type="date" id="expiry_date" name="expiry_date" value="{{ old('expiry_date') }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                @error('expiry_date')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                @enderror
            </div>

            <!-- عرض التكلفة الإجمالية -->
            <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="flex justify-between items-center">
                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">التكلفة الإجمالية:</span>
                    <span id="total-cost" class="text-lg font-bold text-primary">0.00 د.ل</span>
                </div>
            </div>

            <div class="flex justify-end space-x-2 space-x-reverse">
                <a href="{{ route('admin.inventory') }}" class="px-6 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-all flex items-center">
                    <i class="fas fa-times ml-2"></i>
                    إلغاء
                </a>
                <button type="submit" class="px-6 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-all flex items-center">
                    <i class="fas fa-plus ml-2"></i>
                    إضافة المخزون
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const ingredientSelect = document.getElementById('ingredient_id');
        const unitDisplay = document.getElementById('unit-display');
        const quantityInput = document.getElementById('quantity');
        const costPerUnitInput = document.getElementById('cost_per_unit');
        const totalCostDisplay = document.getElementById('total-cost');

        // تحديث وحدة القياس عند تغيير المكون
        function updateUnit() {
            const selectedOption = ingredientSelect.options[ingredientSelect.selectedIndex];
            if (selectedOption.value) {
                const unitText = selectedOption.text.match(/\((.*?)\)/)[1];
                unitDisplay.textContent = unitText;
            } else {
                unitDisplay.textContent = 'وحدة';
            }
        }

        // حساب التكلفة الإجمالية
        function calculateTotalCost() {
            const quantity = parseFloat(quantityInput.value) || 0;
            const costPerUnit = parseFloat(costPerUnitInput.value) || 0;
            const totalCost = quantity * costPerUnit;

            totalCostDisplay.textContent = totalCost.toFixed(2) + ' د.ل';

            // تغيير لون النص حسب القيمة
            if (totalCost > 0) {
                totalCostDisplay.classList.remove('text-gray-500');
                totalCostDisplay.classList.add('text-primary');
            } else {
                totalCostDisplay.classList.remove('text-primary');
                totalCostDisplay.classList.add('text-gray-500');
            }
        }

        // تحديث الوحدة عند تحميل الصفحة
        updateUnit();
        calculateTotalCost();

        // تحديث الوحدة عند تغيير المكون
        ingredientSelect.addEventListener('change', updateUnit);

        // تحديث التكلفة عند تغيير الكمية أو التكلفة
        quantityInput.addEventListener('input', calculateTotalCost);
        costPerUnitInput.addEventListener('input', calculateTotalCost);
    });
</script>
@endsection
