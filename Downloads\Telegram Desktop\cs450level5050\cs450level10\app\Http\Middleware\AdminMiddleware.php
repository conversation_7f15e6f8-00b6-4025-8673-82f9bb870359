<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    public function handle(Request $request, Closure $next): Response
    {
        $user = $request->user();

        if (!$user) {
            return redirect()->route('login')->with('error', 'يجب تسجيل الدخول أولاً');
        }

        // السماح للمديرين بالوصول لكل شيء
        if ($user->user_type === 'admin') {
            return $next($request);
        }

        // السماح للموظفين الذين لديهم صلاحية الوصول للوحة التحكم
        if ($user->user_type === 'employee' && $user->can('dashboard.admin')) {
            return $next($request);
        }

        // إعادة توجيه حسب نوع المستخدم
        if ($user->user_type === 'employee') {
            return redirect()->route('employee.dashboard')->with('error', 'ليس لديك صلاحية للوصول إلى لوحة تحكم المدير');
        }

        if ($user->user_type === 'customer') {
            return redirect()->route('customer.dashboard')->with('error', 'ليس لديك صلاحية للوصول إلى لوحة تحكم المدير');
        }

        return redirect()->route('login')->with('error', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    }
}