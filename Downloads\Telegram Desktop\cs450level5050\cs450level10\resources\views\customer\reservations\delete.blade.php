@extends('customer.layouts.app')

@section('title', 'حذف الحجز - Eat Hub')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
        <!-- شريط التنقل السريع -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
            <div class="flex flex-wrap gap-2 justify-center md:justify-start">
                <a href="{{ route('customer.dashboard') }}" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white px-4 py-2 rounded-lg text-sm transition">
                    <i class="fas fa-tachometer-alt ml-1"></i>لوحة التحكم
                </a>
                <a href="{{ route('customer.reservations') }}" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white px-4 py-2 rounded-lg text-sm transition">
                    <i class="fas fa-calendar-alt ml-1"></i>حجوزاتي
                </a>
                <span class="bg-red-500 text-white px-4 py-2 rounded-lg text-sm">
                    <i class="fas fa-trash ml-1"></i>حذف الحجز
                </span>
            </div>
        </div>

        <!-- تحذير الحذف -->
        <div class="bg-red-50 dark:bg-red-900/20 border-2 border-red-200 dark:border-red-800 rounded-lg p-6 mb-6">
            <div class="flex items-center mb-4">
                <div class="w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center ml-4">
                    <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                </div>
                <div>
                    <h1 class="text-xl font-bold text-red-800 dark:text-red-200">تأكيد حذف الحجز</h1>
                    <p class="text-red-600 dark:text-red-400 text-sm">هذا الإجراء لا يمكن التراجع عنه</p>
                </div>
            </div>
        </div>

        <!-- معلومات الحجز المراد حذفه -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-lg font-bold text-gray-800 dark:text-white mb-4">
                <i class="fas fa-info-circle text-blue-500 ml-2"></i>
                معلومات الحجز المراد حذفه
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-hashtag text-primary text-lg ml-3"></i>
                        <div>
                            <p class="font-medium text-gray-800 dark:text-white">رقم الحجز</p>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">R0002</p>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-calendar text-primary text-lg ml-3"></i>
                        <div>
                            <p class="font-medium text-gray-800 dark:text-white">التاريخ والوقت</p>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">May 2025 28 - 12:30</p>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-chair text-primary text-lg ml-3"></i>
                        <div>
                            <p class="font-medium text-gray-800 dark:text-white">الطاولة</p>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">طاولة #2 - منطقة عامة</p>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                    <div class="flex items-center">
                        <i class="fas fa-users text-primary text-lg ml-3"></i>
                        <div>
                            <p class="font-medium text-gray-800 dark:text-white">عدد الأشخاص</p>
                            <p class="text-gray-600 dark:text-gray-400 text-sm">4 أشخاص</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- ملاحظات خاصة -->
            <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3">
                <p class="text-yellow-800 dark:text-yellow-200 text-sm">
                    <i class="fas fa-sticky-note ml-1"></i>
                    <strong>ملاحظة خاصة:</strong> طاولة بجانب النافذة، احتفال بعيد ميلاد
                </p>
            </div>
        </div>

        <!-- أسباب الحذف -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">
                <i class="fas fa-question-circle text-orange-500 ml-2"></i>
                سبب إلغاء الحجز (اختياري)
            </h3>
            <form id="deleteReservationForm" action="{{ route('customer.reservations.cancel', $reservation->reservation_id ?? $reservation->id) }}" method="POST">
                @csrf
                @method('DELETE')

                <div class="space-y-3 mb-4">
                    <label class="flex items-center">
                        <input type="radio" name="cancellation_reason" value="change_plans" class="ml-2 text-primary">
                        <span class="text-gray-700 dark:text-gray-300">تغيير في الخطط</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="cancellation_reason" value="emergency" class="ml-2 text-primary">
                        <span class="text-gray-700 dark:text-gray-300">ظرف طارئ</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="cancellation_reason" value="found_better" class="ml-2 text-primary">
                        <span class="text-gray-700 dark:text-gray-300">وجدت خيار أفضل</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="cancellation_reason" value="technical_issue" class="ml-2 text-primary">
                        <span class="text-gray-700 dark:text-gray-300">مشكلة تقنية في الحجز</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="cancellation_reason" value="other" class="ml-2 text-primary">
                        <span class="text-gray-700 dark:text-gray-300">سبب آخر</span>
                    </label>
                </div>

                <div class="mb-4">
                    <label for="additional_notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        ملاحظات إضافية (اختياري)
                    </label>
                    <textarea id="additional_notes"
                              name="additional_notes"
                              rows="3"
                              placeholder="أي ملاحظات إضافية حول سبب الإلغاء..."
                              class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white resize-none"></textarea>
                </div>
            </form>
        </div>

        <!-- تحذيرات مهمة -->
        <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
            <div class="flex items-start">
                <i class="fas fa-exclamation-triangle text-yellow-600 ml-2 mt-1"></i>
                <div>
                    <h3 class="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">تحذيرات مهمة</h3>
                    <ul class="text-yellow-700 dark:text-yellow-300 text-sm space-y-1">
                        <li>• سيتم إلغاء الحجز نهائياً ولا يمكن استرداده</li>
                        <li>• إذا كان الحجز مدفوع، سيتم استرداد المبلغ خلال 3-5 أيام عمل</li>
                        <li>• سيتم إشعار المطعم بالإلغاء فوراً</li>
                        <li>• يمكنك إنشاء حجز جديد في أي وقت</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row gap-4 justify-between">
                <div class="flex flex-col sm:flex-row gap-4">
                    <button type="button"
                            onclick="confirmDelete()"
                            class="bg-red-500 hover:bg-red-600 text-white font-bold py-3 px-6 rounded-lg transition flex items-center justify-center">
                        <i class="fas fa-trash ml-2"></i>
                        تأكيد حذف الحجز
                    </button>
                    <a href="{{ route('customer.reservations') }}"
                       class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-lg transition flex items-center justify-center">
                        <i class="fas fa-times ml-2"></i>
                        إلغاء العملية
                    </a>
                </div>
                <a href="{{ route('customer.reservations.edit', $reservation->reservation_id ?? $reservation->id) }}"
                   class="bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition flex items-center justify-center">
                    <i class="fas fa-edit ml-2"></i>
                    تعديل بدلاً من الحذف
                </a>
            </div>
        </div>

        <!-- معلومات الاتصال -->
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mt-6">
            <div class="flex items-start">
                <i class="fas fa-phone text-blue-600 ml-2 mt-1"></i>
                <div>
                    <h3 class="font-semibold text-blue-800 dark:text-blue-200">هل تحتاج مساعدة؟</h3>
                    <p class="text-blue-700 dark:text-blue-300 text-sm mt-1">
                        يمكنك الاتصال بنا على: <strong>+218 91 234 5678</strong> أو مراسلتنا عبر الواتساب للمساعدة في تعديل الحجز بدلاً من حذفه.
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function confirmDelete() {
    const form = document.getElementById('deleteReservationForm');
    const reason = form.querySelector('input[name="cancellation_reason"]:checked');
    const notes = form.querySelector('#additional_notes').value;

    let confirmMessage = 'هل أنت متأكد من حذف هذا الحجز؟\n\n';
    confirmMessage += 'رقم الحجز: R0002\n';
    confirmMessage += 'التاريخ: May 2025 28 - 12:30\n';
    confirmMessage += 'الطاولة: #2\n\n';

    if (reason) {
        const reasonText = reason.nextElementSibling.textContent;
        confirmMessage += 'السبب: ' + reasonText + '\n';
    }

    if (notes.trim()) {
        confirmMessage += 'ملاحظات: ' + notes.trim() + '\n';
    }

    confirmMessage += '\nلا يمكن التراجع عن هذا الإجراء.';

    if (confirm(confirmMessage)) {
        // إرسال النموذج
        showLoadingMessage();
        form.submit();
    }
}

function showLoadingMessage() {
    const button = document.querySelector('button[onclick="confirmDelete()"]');
    button.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري الحذف...';
    button.disabled = true;
    button.classList.add('opacity-50');
}

// إضافة تأثيرات تفاعلية
document.addEventListener('DOMContentLoaded', function() {
    // تمييز السبب المختار
    document.querySelectorAll('input[name="cancellation_reason"]').forEach(radio => {
        radio.addEventListener('change', function() {
            // إزالة التمييز من جميع الخيارات
            document.querySelectorAll('label').forEach(label => {
                label.classList.remove('bg-primary/10', 'border-primary');
            });

            // إضافة التمييز للخيار المختار
            if (this.checked) {
                this.closest('label').classList.add('bg-primary/10', 'border-primary', 'border', 'rounded-lg', 'p-2');
            }
        });
    });
});
</script>
@endpush
