@extends('layouts.admin')

@section('title', 'تقرير الطلبات')

@section('content')
<div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
    <div>
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white">تقرير الطلبات</h2>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">عرض تفاصيل الطلبات وإحصائياتها</p>
    </div>
    <div class="mt-4 md:mt-0">
        <a href="{{ route('admin.reports') }}" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
            <i class="fas fa-arrow-right ml-2"></i>
            <span>العودة للتقارير</span>
        </a>
    </div>
</div>

<!-- فلتر البحث -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <form action="{{ route('admin.reports.orders') }}" method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
            <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">من تاريخ</label>
            <input type="date" id="start_date" name="start_date" value="{{ $startDate }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
        </div>
        <div>
            <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إلى تاريخ</label>
            <input type="date" id="end_date" name="end_date" value="{{ $endDate }}" class="w-full px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
        </div>
        <div class="flex items-end">
            <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-all">تطبيق الفلتر</button>
        </div>
    </form>
</div>

<!-- إحصائيات الطلبات -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">إجمالي الطلبات</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ $totalOrders }}</h3>
            </div>
            <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3">
                <i class="fas fa-shopping-cart text-blue-500 text-xl"></i>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">الطلبات المكتملة</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ $completedOrders }}</h3>
            </div>
            <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-3">
                <i class="fas fa-check text-green-500 text-xl"></i>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">الطلبات قيد الانتظار</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ $pendingOrders }}</h3>
            </div>
            <div class="rounded-full bg-yellow-100 dark:bg-yellow-900/30 p-3">
                <i class="fas fa-clock text-yellow-500 text-xl"></i>
            </div>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">الطلبات الملغاة</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ $cancelledOrders }}</h3>
            </div>
            <div class="rounded-full bg-red-100 dark:bg-red-900/30 p-3">
                <i class="fas fa-times text-red-500 text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- متوسط قيمة الطلب -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">متوسط قيمة الطلب</h3>
    <div class="flex items-center">
        <div class="rounded-full bg-primary/20 p-3 ml-4">
            <i class="fas fa-money-bill-wave text-primary text-xl"></i>
        </div>
        <div>
            <p class="text-3xl font-bold text-gray-800 dark:text-white">{{ number_format($averageOrderValue, 2) }} د.ل</p>
        </div>
    </div>
</div>

<!-- قائمة الطلبات -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
    <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white">قائمة الطلبات</h3>
    </div>
    
    <div class="overflow-x-auto">
        <table class="w-full">
            <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">رقم الطلب</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">العميل</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">التاريخ</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المبلغ</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحالة</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الإجراءات</th>
                </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                @forelse($orders as $order)
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ $order->order_id }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white">
                            {{ $order->user ? $order->user->first_name . ' ' . $order->user->last_name : 'غير معروف' }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ $order->created_at->format('Y-m-d H:i') }}</td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white">{{ number_format($order->total_amount, 2) }} د.ل</td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            @if($order->status == 'completed')
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200">مكتمل</span>
                            @elseif($order->status == 'pending')
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">قيد الانتظار</span>
                            @elseif($order->status == 'cancelled')
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200">ملغي</span>
                            @else
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300">{{ $order->status }}</span>
                            @endif
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-800 dark:text-white">
                            <a href="{{ route('admin.orders.show', $order->order_id) }}" class="text-blue-500 hover:text-blue-700 transition-all">
                                <i class="fas fa-eye"></i>
                            </a>
                        </td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-gray-500 dark:text-gray-400">لا توجد طلبات متاحة</td>
                    </tr>
                @endforelse
            </tbody>
        </table>
    </div>
    
    <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        {{ $orders->links('pagination.tailwind') }}
    </div>
</div>
@endsection
