@extends('customer.layouts.app')

@section('title', 'لوحة التحكم - Eat Hub')

@section('content')
<div class="container mx-auto px-4 py-8">
    <!-- شريط التنقل السريع -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
        <div class="flex flex-wrap gap-2 justify-center md:justify-start">
            <a href="{{ route('customer.dashboard') }}" class="bg-primary text-white px-4 py-2 rounded-lg text-sm transition">
                <i class="fas fa-tachometer-alt ml-1"></i>لوحة التحكم
            </a>
            <a href="{{ route('customer.profile') }}" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white px-4 py-2 rounded-lg text-sm transition">
                <i class="fas fa-user ml-1"></i>الملف الشخصي
            </a>
            <a href="{{ route('customer.orders') }}" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white px-4 py-2 rounded-lg text-sm transition">
                <i class="fas fa-shopping-bag ml-1"></i>طلباتي
            </a>
            <a href="{{ route('customer.reservations') }}" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white px-4 py-2 rounded-lg text-sm transition">
                <i class="fas fa-calendar-alt ml-1"></i>حجوزاتي
            </a>
            <a href="{{ route('customer.index') }}" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white px-4 py-2 rounded-lg text-sm transition">
                <i class="fas fa-home ml-1"></i>الرئيسية
            </a>
        </div>
    </div>

    <div class="mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
            <div class="p-6 bg-gradient-to-r from-primary/90 to-primary text-white">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <div>
                        <h2 class="text-2xl font-bold mb-2">مرحباً، {{ $user->first_name }} {{ $user->last_name }}</h2>
                        <p class="text-white/90">نحن سعداء برؤيتك مرة أخرى!</p>
                    </div>
                    <div class="mt-4 md:mt-0">
                        <a href="{{ route('customer.menu') }}" class="bg-white text-primary hover:bg-gray-100 font-bold py-2 px-6 rounded-full transition duration-200 inline-block">
                            تصفح القائمة
                        </a>
                    </div>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 p-6">
                <div class="bg-white dark:bg-gray-700 rounded-lg shadow p-4 flex items-center">
                    <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3 ml-4">
                        <i class="fas fa-clipboard-list text-blue-500 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-gray-500 dark:text-gray-400 text-sm">إجمالي الطلبات</p>
                        <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ $orderStats['totalOrders'] }}</h3>
                    </div>
                </div>
                <div class="bg-white dark:bg-gray-700 rounded-lg shadow p-4 flex items-center">
                    <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-3 ml-4">
                        <i class="fas fa-calendar-check text-green-500 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-gray-500 dark:text-gray-400 text-sm">حجوزات نشطة</p>
                        <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ $upcomingReservations->count() }}</h3>
                    </div>
                </div>
                <div class="bg-white dark:bg-gray-700 rounded-lg shadow p-4 flex items-center">
                    <div class="rounded-full bg-purple-100 dark:bg-purple-900/30 p-3 ml-4">
                        <i class="fas fa-star text-purple-500 text-xl"></i>
                    </div>
                    <div>
                        <p class="text-gray-500 dark:text-gray-400 text-sm">إجمالي الإنفاق</p>
                        <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ number_format($orderStats['totalSpent'], 2) }} د.ل</h3>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <!-- آخر الطلبات -->
        <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">آخر الطلبات</h3>
                <a href="{{ route('customer.orders') }}" class="text-primary text-sm hover:underline">عرض الكل</a>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full text-sm">
                    <thead>
                        <tr class="bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                            <th class="py-2 px-3 text-right">#</th>
                            <th class="py-2 px-3 text-right">التاريخ</th>
                            <th class="py-2 px-3 text-right">المبلغ</th>
                            <th class="py-2 px-3 text-right">الحالة</th>
                            <th class="py-2 px-3 text-right">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($recentOrders as $order)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                            <td class="py-3 px-3">{{ $order->order_number ?? '#' . $order->order_id }}</td>
                            <td class="py-3 px-3">
                                @if(is_object($order->created_at) && method_exists($order->created_at, 'diffForHumans'))
                                    {{ $order->created_at->diffForHumans() }}
                                @else
                                    {{ $order->created_at }}
                                @endif
                            </td>
                            <td class="py-3 px-3">{{ number_format($order->total_amount, 2) }} د.ل</td>
                            <td class="py-3 px-3">
                                @if($order->status == 'completed')
                                    <span class="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full text-xs">مكتمل</span>
                                @elseif($order->status == 'processing')
                                    <span class="px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 rounded-full text-xs">قيد التجهيز</span>
                                @else
                                    <span class="px-2 py-1 bg-gray-100 dark:bg-gray-900/30 text-gray-600 dark:text-gray-400 rounded-full text-xs">{{ $order->status }}</span>
                                @endif
                            </td>
                            <td class="py-3 px-3">
                                <a href="{{ route('customer.orders.show', $order->order_id) }}" class="text-primary hover:text-primary/80" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="5" class="py-6 text-center text-gray-500 dark:text-gray-400">
                                لا توجد طلبات حتى الآن
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        <!-- حجوزات قادمة -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">حجوزات قادمة</h3>
                <a href="{{ route('customer.reservations') }}" class="text-primary text-sm hover:underline">عرض الكل</a>
            </div>
            <div class="space-y-4">
                @forelse($upcomingReservations as $reservation)
                <div class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border-r-4 border-blue-500">
                    <div class="flex justify-between">
                        <div>
                            <p class="font-bold text-gray-800 dark:text-white">طاولة #{{ $reservation->table->table_number ?? '8' }}</p>
                            <p class="text-sm text-gray-600 dark:text-gray-300">{{ $reservation->table->capacity ?? '2' }} أشخاص</p>
                        </div>
                        <div class="text-right">
                            @if(is_object($reservation->reservation_time))
                                <p class="font-bold text-gray-800 dark:text-white">{{ $reservation->reservation_time->format('d/m H:i') }}</p>
                            @else
                                <p class="font-bold text-gray-800 dark:text-white">{{ $reservation->reservation_time }}</p>
                            @endif
                            <p class="text-xs text-gray-500 dark:text-gray-400">لمدة {{ $reservation->duration ?? '60' }} دقيقة</p>
                        </div>
                    </div>
                    <div class="mt-2 flex justify-between">
                        <a href="{{ route('customer.reservations.show', $reservation->reservation_id) }}" class="text-primary hover:text-primary/80 text-sm">
                            <i class="fas fa-eye ml-1"></i>التفاصيل
                        </a>
                        <button class="text-red-500 hover:text-red-700 text-sm">
                            <i class="fas fa-times ml-1"></i>إلغاء
                        </button>
                    </div>
                </div>
                @empty
                <div class="text-center py-6">
                    <p class="text-gray-500 dark:text-gray-400 mb-4">لا توجد حجوزات قادمة</p>
                    <a href="{{ route('customer.reservations.create') }}" class="bg-primary/10 text-primary hover:bg-primary/20 font-medium py-2 px-4 rounded-lg transition">
                        <i class="fas fa-plus ml-1"></i>حجز طاولة جديدة
                    </a>
                </div>
                @endforelse
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- الأطباق المفضلة -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">الأطباق المفضلة</h3>
                <a href="{{ route('customer.menu') }}" class="text-primary text-sm hover:underline">اطلب الآن</a>
            </div>
            <div class="space-y-4">
                @forelse($orderStats['favoriteItems'] as $item)
                <div class="flex items-center">
                    <img src="https://images.unsplash.com/photo-1513104890138-7c749659a591?ixlib=rb-1.2.1&auto=format&fit=crop&w=100&q=80" alt="{{ $item->name }}" class="w-12 h-12 object-cover rounded-md ml-3">
                    <div class="flex-1">
                        <p class="font-medium text-gray-800 dark:text-white">{{ $item->name }}</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">{{ $item->total }} مرات</p>
                    </div>
                    <button class="text-primary hover:text-primary/80 px-2 py-1 rounded-full bg-primary/10 hover:bg-primary/20">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
                @empty
                <div class="text-center py-6">
                    <p class="text-gray-500 dark:text-gray-400">لا توجد أطباق مفضلة بعد</p>
                </div>
                @endforelse
            </div>
        </div>

        <!-- الإشعارات -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">الإشعارات</h3>
                <a href="#" class="text-primary text-sm hover:underline">عرض الكل</a>
            </div>
            <div class="space-y-4">
                @forelse($notifications as $notification)
                <div class="border-b border-gray-200 dark:border-gray-700 pb-3">
                    <div class="flex items-start">
                        <div class="w-2 h-2 bg-primary rounded-full mt-2 ml-3 flex-shrink-0"></div>
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-800 dark:text-white text-sm">{{ $notification->title }}</h4>
                            <p class="text-gray-600 dark:text-gray-400 text-xs mt-1">{{ $notification->message }}</p>
                            @if(is_object($notification->created_at) && method_exists($notification->created_at, 'diffForHumans'))
                                <p class="text-gray-500 dark:text-gray-500 text-xs mt-1">{{ $notification->created_at->diffForHumans() }}</p>
                            @endif
                        </div>
                    </div>
                </div>
                @empty
                <div class="text-center py-6">
                    <p class="text-gray-500 dark:text-gray-400">لا توجد إشعارات جديدة</p>
                </div>
                @endforelse
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">إحصائيات سريعة</h3>
            </div>
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <span class="text-gray-600 dark:text-gray-400 text-sm">إجمالي المبلغ المنفق</span>
                    <span class="font-bold text-gray-800 dark:text-white">{{ number_format($orderStats['totalSpent'], 2) }} د.ل</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-gray-600 dark:text-gray-400 text-sm">متوسط الطلب</span>
                    <span class="font-bold text-gray-800 dark:text-white">
                        {{ $orderStats['totalOrders'] > 0 ? number_format($orderStats['totalSpent'] / $orderStats['totalOrders'], 2) : '0.00' }} د.ل
                    </span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-gray-600 dark:text-gray-400 text-sm">نقاط الولاء</span>
                    <span class="font-bold text-primary">{{ floor($orderStats['totalSpent'] / 10) }} نقطة</span>
                </div>
            </div>
        </div>
    </div>

    <!-- روابط سريعة -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mt-8">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">روابط سريعة</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a href="{{ route('customer.orders') }}" class="flex flex-col items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-primary/10 dark:hover:bg-primary/20 transition group">
                <i class="fas fa-shopping-bag text-2xl text-primary group-hover:text-primary mb-2"></i>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-primary">طلباتي</span>
            </a>
            <a href="{{ route('customer.reservations') }}" class="flex flex-col items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-primary/10 dark:hover:bg-primary/20 transition group">
                <i class="fas fa-calendar-alt text-2xl text-primary group-hover:text-primary mb-2"></i>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-primary">حجوزاتي</span>
            </a>
            <a href="{{ route('customer.profile') }}" class="flex flex-col items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-primary/10 dark:hover:bg-primary/20 transition group">
                <i class="fas fa-user text-2xl text-primary group-hover:text-primary mb-2"></i>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-primary">الملف الشخصي</span>
            </a>
            <a href="{{ route('customer.index') }}" class="flex flex-col items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-primary/10 dark:hover:bg-primary/20 transition group">
                <i class="fas fa-home text-2xl text-primary group-hover:text-primary mb-2"></i>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-primary">الرئيسية</span>
            </a>
        </div>
    </div>
</div>
@endsection
