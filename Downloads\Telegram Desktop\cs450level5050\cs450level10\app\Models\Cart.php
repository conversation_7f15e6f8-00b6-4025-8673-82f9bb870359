<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Cart extends Model
{
    use HasFactory;

    protected $table = 'cart';
    protected $primaryKey = 'cart_id';

    protected $fillable = [
        'user_id',
        'menu_item_id',
        'quantity',
        'price',
        'special_instructions'
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'quantity' => 'integer'
    ];

    // العلاقات
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id');
    }

    public function menuItem()
    {
        return $this->belongsTo(MenuItem::class, 'menu_item_id', 'item_id');
    }

    // الحصول على المجموع الفرعي
    public function getSubtotalAttribute()
    {
        return $this->quantity * $this->price;
    }

    // دوال مساعدة
    public static function getCartTotal($userId)
    {
        return self::where('user_id', $userId)->sum(\DB::raw('quantity * price'));
    }

    public static function getCartItemsCount($userId)
    {
        return self::where('user_id', $userId)->sum('quantity');
    }

    public static function clearCart($userId)
    {
        return self::where('user_id', $userId)->delete();
    }
}
