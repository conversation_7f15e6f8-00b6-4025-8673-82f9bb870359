<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('reservations', function (Blueprint $table) {
            $table->integer('party_size')->after('duration');
            $table->text('special_requests')->nullable()->after('party_size');
            $table->enum('status', ['pending', 'confirmed', 'canceled', 'completed'])->default('pending')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('reservations', function (Blueprint $table) {
            $table->dropColumn(['party_size', 'special_requests']);
            $table->enum('status', ['confirmed', 'canceled', 'completed'])->default('confirmed')->change();
        });
    }
};
