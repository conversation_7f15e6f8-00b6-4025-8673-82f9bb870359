@extends('employee.layouts.app')

@section('title', 'تسجيل دفعة جديدة - نظام إدارة المطعم')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">تسجيل دفعة جديدة</h1>
        <p class="text-gray-600 dark:text-gray-400">تسجيل دفعة جديدة للطلب رقم #{{ $order->order_id }}</p>
    </div>

    @if(session('error'))
    <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 mb-6 rounded-md dark:bg-red-900/30 dark:text-red-500 dark:border-red-500">
        <p>{{ session('error') }}</p>
    </div>
    @endif

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- نموذج تسجيل الدفعة -->
        <div class="lg:col-span-2">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-bold text-gray-800 dark:text-white">معلومات الدفعة</h2>
                </div>
                
                <form action="{{ route('employee.payments.store') }}" method="POST" class="p-6 space-y-6">
                    @csrf
                    <input type="hidden" name="order_id" value="{{ $order->order_id }}">
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- المبلغ المتبقي -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">المبلغ المتبقي</label>
                            <div class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
                                {{ number_format($remainingAmount, 2) }} د.ل
                            </div>
                        </div>
                        
                        <!-- مبلغ الدفعة -->
                        <div>
                            <label for="amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">مبلغ الدفعة</label>
                            <input type="number" id="amount" name="amount" step="0.01" min="0.01" max="{{ $remainingAmount }}" value="{{ old('amount', $remainingAmount) }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white @error('amount') border-red-500 dark:border-red-500 @enderror" required>
                            @error('amount')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <!-- طريقة الدفع -->
                        <div>
                            <label for="payment_method" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">طريقة الدفع</label>
                            <select id="payment_method" name="payment_method" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white @error('payment_method') border-red-500 dark:border-red-500 @enderror" required>
                                <option value="cash" {{ old('payment_method') == 'cash' ? 'selected' : '' }}>نقدي</option>
                                <option value="card" {{ old('payment_method') == 'card' ? 'selected' : '' }}>بطاقة</option>
                            </select>
                            @error('payment_method')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <!-- تاريخ العملية -->
                        <div>
                            <label for="transaction_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">تاريخ العملية</label>
                            <input type="datetime-local" id="transaction_date" name="transaction_date" value="{{ old('transaction_date', now()->format('Y-m-d\TH:i')) }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white @error('transaction_date') border-red-500 dark:border-red-500 @enderror">
                            @error('transaction_date')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <!-- ملاحظات -->
                        <div class="md:col-span-2">
                            <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">ملاحظات</label>
                            <textarea id="notes" name="notes" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white @error('notes') border-red-500 dark:border-red-500 @enderror">{{ old('notes') }}</textarea>
                            @error('notes')
                            <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="flex justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
                        <a href="{{ route('employee.orders.show', $order->order_id) }}" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                            إلغاء
                        </a>
                        <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50">
                            تسجيل الدفعة
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- سجل المدفوعات السابقة -->
            <div class="mt-6 bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-bold text-gray-800 dark:text-white">سجل المدفوعات السابقة</h2>
                </div>
                
                @if(count($payments) > 0)
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    رقم العملية
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    المبلغ
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    طريقة الدفع
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    تاريخ العملية
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach($payments as $payment)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                    {{ $payment->payment_id }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                    {{ number_format($payment->amount, 2) }} د.ل
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                        {{ $payment->payment_method == 'cash' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300' }}">
                                        {{ $payment->payment_method == 'cash' ? 'نقدي' : 'بطاقة' }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                    {{ \Carbon\Carbon::parse($payment->transaction_date)->format('Y-m-d H:i') }}
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="p-6 text-center">
                    <p class="text-gray-500 dark:text-gray-400">لا توجد مدفوعات سابقة لهذا الطلب</p>
                </div>
                @endif
            </div>
        </div>
        
        <!-- معلومات الطلب والفاتورة -->
        <div>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
                <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-bold text-gray-800 dark:text-white">معلومات الطلب</h2>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">رقم الطلب:</span>
                            <span class="font-medium text-gray-900 dark:text-white">#{{ $order->order_id }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">العميل:</span>
                            <span class="font-medium text-gray-900 dark:text-white">{{ $order->user->first_name }} {{ $order->user->last_name }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">تاريخ الطلب:</span>
                            <span class="font-medium text-gray-900 dark:text-white">{{ $order->created_at->format('Y-m-d H:i') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">حالة الطلب:</span>
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {{ $order->status == 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 
                                   ($order->status == 'canceled' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' : 
                                   'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300') }}">
                                {{ $order->status == 'completed' ? 'مكتمل' : 
                                   ($order->status == 'canceled' ? 'ملغي' : 
                                   ($order->status == 'pending' ? 'قيد الانتظار' : 
                                   ($order->status == 'preparing' ? 'قيد التحضير' : $order->status))) }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <h3 class="text-base font-medium text-gray-900 dark:text-white mb-3">ملخص الطلب</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">إجمالي الطلب:</span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ number_format($order->total_amount, 2) }} د.ل</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">المدفوع:</span>
                                <span class="font-medium text-green-600 dark:text-green-400">{{ number_format($totalPaid, 2) }} د.ل</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">المتبقي:</span>
                                <span class="font-bold text-red-600 dark:text-red-400">{{ number_format($remainingAmount, 2) }} د.ل</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <a href="{{ route('employee.orders.show', $order->order_id) }}" class="block text-center w-full px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                            <i class="fas fa-eye ml-2"></i>
                            عرض تفاصيل الطلب
                        </a>
                    </div>
                </div>
            </div>
            
            @if(isset($invoice))
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-bold text-gray-800 dark:text-white">معلومات الفاتورة</h2>
                </div>
                <div class="p-6">
                    <div class="space-y-4">
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">رقم الفاتورة:</span>
                            <span class="font-medium text-gray-900 dark:text-white">#{{ $invoice->invoice_id }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">تاريخ الإصدار:</span>
                            <span class="font-medium text-gray-900 dark:text-white">{{ $invoice->issued_at->format('Y-m-d') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">تاريخ الاستحقاق:</span>
                            <span class="font-medium text-gray-900 dark:text-white">{{ $invoice->due_date->format('Y-m-d') }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">حالة الدفع:</span>
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                {{ $invoice->payment_status == 'paid' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 
                                   ($invoice->payment_status == 'unpaid' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' : 
                                   'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300') }}">
                                {{ $invoice->payment_status == 'paid' ? 'مدفوع' : 
                                   ($invoice->payment_status == 'unpaid' ? 'غير مدفوع' : 'مدفوع جزئياً') }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                        <h3 class="text-base font-medium text-gray-900 dark:text-white mb-3">تفاصيل الفاتورة</h3>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">إجمالي الطلب:</span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ number_format($order->total_amount, 2) }} د.ل</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">الضريبة:</span>
                                <span class="font-medium text-gray-900 dark:text-white">{{ number_format($invoice->tax_amount, 2) }} د.ل</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">الخصم:</span>
                                <span class="font-medium text-green-600 dark:text-green-400">{{ number_format($invoice->discount_amount, 2) }} د.ل</span>
                            </div>
                            <div class="flex justify-between font-bold">
                                <span class="text-gray-800 dark:text-white">الإجمالي النهائي:</span>
                                <span class="text-gray-900 dark:text-white">{{ number_format($invoice->total_amount, 2) }} د.ل</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <button type="button" class="block text-center w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50" onclick="printInvoice({{ $invoice->invoice_id }})">
                            <i class="fas fa-print ml-2"></i>
                            طباعة الفاتورة
                        </button>
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>

<script>
    function printInvoice(invoiceId) {
        // يمكن تنفيذ طباعة الفاتورة هنا
        alert('جاري طباعة الفاتورة رقم ' + invoiceId);
    }
    
    document.addEventListener('DOMContentLoaded', function() {
        const amountInput = document.getElementById('amount');
        const remainingAmount = {{ $remainingAmount }};
        
        // التأكد من أن مبلغ الدفعة لا يتجاوز المبلغ المتبقي
        amountInput.addEventListener('input', function() {
            if (parseFloat(this.value) > remainingAmount) {
                this.value = remainingAmount;
            }
        });
    });
</script>
@endsection
