@extends('employee.layouts.app')

@section('title', 'التقارير')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 py-8">
    <!-- عناصر زخرفية متحركة -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-purple-600/20 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-pink-400/20 to-red-600/20 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
        <div class="absolute top-40 left-40 w-80 h-80 bg-gradient-to-r from-yellow-400/20 to-orange-600/20 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <!-- العنوان الرئيسي -->
        <div class="text-center mb-12">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full shadow-2xl mb-6">
                <i class="fas fa-chart-bar text-3xl text-white"></i>
            </div>
            <h1 class="text-4xl md:text-5xl font-bold text-gray-800 dark:text-white mb-4">
                📊 التقارير والإحصائيات
            </h1>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                تتبع أداء المطعم وتحليل البيانات المالية والتشغيلية
            </p>
        </div>

        <!-- الإحصائيات العامة -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <!-- إجمالي الطلبات -->
            <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl p-6 shadow-xl border border-white/20 dark:border-gray-700/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-shopping-cart text-white text-xl"></i>
                    </div>
                    <span class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ number_format($totalOrders) }}</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">إجمالي الطلبات</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">منذ بداية التشغيل</p>
            </div>

            <!-- إجمالي الحجوزات -->
            <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl p-6 shadow-xl border border-white/20 dark:border-gray-700/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-calendar-check text-white text-xl"></i>
                    </div>
                    <span class="text-3xl font-bold text-purple-600 dark:text-purple-400">{{ number_format($totalReservations) }}</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">إجمالي الحجوزات</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">جميع الحجوزات</p>
            </div>

            <!-- إجمالي الإيرادات -->
            <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl p-6 shadow-xl border border-white/20 dark:border-gray-700/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-dollar-sign text-white text-xl"></i>
                    </div>
                    <span class="text-3xl font-bold text-green-600 dark:text-green-400">{{ number_format($totalRevenue, 2) }}</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">إجمالي الإيرادات</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">ريال سعودي</p>
            </div>

            <!-- طلبات اليوم -->
            <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl p-6 shadow-xl border border-white/20 dark:border-gray-700/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-clock text-white text-xl"></i>
                    </div>
                    <span class="text-3xl font-bold text-orange-600 dark:text-orange-400">{{ number_format($todayOrders) }}</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">طلبات اليوم</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">{{ now()->format('Y-m-d') }}</p>
            </div>
        </div>

        <!-- إحصائيات مفصلة -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
            <!-- إحصائيات اليوم -->
            <div class="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-3xl p-8 shadow-2xl border border-white/20 dark:border-gray-700/20">
                <div class="flex items-center mb-6">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg mr-4">
                        <i class="fas fa-calendar-day text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 dark:text-white">📅 اليوم</h3>
                </div>
                <div class="space-y-4">
                    <div class="flex justify-between items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-2xl">
                        <span class="text-gray-700 dark:text-gray-300">الطلبات</span>
                        <span class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $todayOrders }}</span>
                    </div>
                    <div class="flex justify-between items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-2xl">
                        <span class="text-gray-700 dark:text-gray-300">الإيرادات</span>
                        <span class="text-2xl font-bold text-green-600 dark:text-green-400">{{ number_format($todayRevenue, 2) }} ر.س</span>
                    </div>
                </div>
                <a href="{{ route('employee.reports.daily') }}" class="mt-6 w-full bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-3 px-6 rounded-2xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center justify-center">
                    <i class="fas fa-eye ml-2"></i>
                    عرض التفاصيل
                </a>
            </div>

            <!-- إحصائيات الأسبوع -->
            <div class="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-3xl p-8 shadow-2xl border border-white/20 dark:border-gray-700/20">
                <div class="flex items-center mb-6">
                    <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center shadow-lg mr-4">
                        <i class="fas fa-calendar-week text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 dark:text-white">📊 الأسبوع</h3>
                </div>
                <div class="space-y-4">
                    <div class="flex justify-between items-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-2xl">
                        <span class="text-gray-700 dark:text-gray-300">الطلبات</span>
                        <span class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ $weekOrders }}</span>
                    </div>
                    <div class="flex justify-between items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-2xl">
                        <span class="text-gray-700 dark:text-gray-300">الإيرادات</span>
                        <span class="text-2xl font-bold text-green-600 dark:text-green-400">{{ number_format($weekRevenue, 2) }} ر.س</span>
                    </div>
                </div>
                <a href="{{ route('employee.reports.weekly') }}" class="mt-6 w-full bg-gradient-to-r from-purple-500 to-pink-600 text-white py-3 px-6 rounded-2xl hover:from-purple-600 hover:to-pink-700 transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center justify-center">
                    <i class="fas fa-eye ml-2"></i>
                    عرض التفاصيل
                </a>
            </div>

            <!-- إحصائيات الشهر -->
            <div class="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-3xl p-8 shadow-2xl border border-white/20 dark:border-gray-700/20">
                <div class="flex items-center mb-6">
                    <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg mr-4">
                        <i class="fas fa-calendar-alt text-white"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 dark:text-white">📈 الشهر</h3>
                </div>
                <div class="space-y-4">
                    <div class="flex justify-between items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-2xl">
                        <span class="text-gray-700 dark:text-gray-300">الطلبات</span>
                        <span class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $monthOrders }}</span>
                    </div>
                    <div class="flex justify-between items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-2xl">
                        <span class="text-gray-700 dark:text-gray-300">الإيرادات</span>
                        <span class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ number_format($monthRevenue, 2) }} ر.س</span>
                    </div>
                </div>
                <a href="{{ route('employee.reports.monthly') }}" class="mt-6 w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white py-3 px-6 rounded-2xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center justify-center">
                    <i class="fas fa-eye ml-2"></i>
                    عرض التفاصيل
                </a>
            </div>
        </div>

        <!-- أزرار التصدير -->
        <div class="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-3xl p-8 shadow-2xl border border-white/20 dark:border-gray-700/20">
            <div class="flex items-center mb-6">
                <div class="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg mr-4">
                    <i class="fas fa-download text-white"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">💾 تصدير التقارير</h3>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button class="bg-gradient-to-r from-red-500 to-pink-600 text-white py-4 px-6 rounded-2xl hover:from-red-600 hover:to-pink-700 transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center justify-center">
                    <i class="fas fa-file-pdf ml-2"></i>
                    تصدير PDF
                </button>
                <button class="bg-gradient-to-r from-green-500 to-emerald-600 text-white py-4 px-6 rounded-2xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center justify-center">
                    <i class="fas fa-file-excel ml-2"></i>
                    تصدير Excel
                </button>
                <button class="bg-gradient-to-r from-blue-500 to-indigo-600 text-white py-4 px-6 rounded-2xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center justify-center">
                    <i class="fas fa-print ml-2"></i>
                    طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<style>
@keyframes blob {
    0% { transform: translate(0px, 0px) scale(1); }
    33% { transform: translate(30px, -50px) scale(1.1); }
    66% { transform: translate(-20px, 20px) scale(0.9); }
    100% { transform: translate(0px, 0px) scale(1); }
}

.animate-blob {
    animation: blob 7s infinite;
}

.animation-delay-2000 {
    animation-delay: 2s;
}

.animation-delay-4000 {
    animation-delay: 4s;
}
</style>
@endsection
