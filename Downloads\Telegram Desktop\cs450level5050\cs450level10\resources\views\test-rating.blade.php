<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام التقييم</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="{{ asset('css/rating-system.css') }}" rel="stylesheet">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8 text-center">اختبار نظام التقييم</h1>
        
        <!-- مثال 1: تقييم تفاعلي -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">تقييم تفاعلي</h2>
            <p class="text-gray-600 mb-4">انقر على النجوم لإعطاء تقييم:</p>
            
            <div class="rating-input" id="rating1" data-max-stars="5" data-initial-rating="0"></div>
            <input type="hidden" id="ratingValue1" name="rating" value="0">
            
            <div class="mt-4">
                <span class="text-gray-700">التقييم المحدد: </span>
                <span id="selectedRating1" class="font-bold text-primary">0</span>
                <span class="text-gray-700"> من 5</span>
            </div>
        </div>
        
        <!-- مثال 2: تقييم للعرض فقط -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">تقييم للعرض فقط</h2>
            <p class="text-gray-600 mb-4">تقييم 4.5 من 5:</p>
            
            <div class="rating-display" data-rating="4.5" data-max-stars="5"></div>
        </div>
        
        <!-- مثال 3: أحجام مختلفة -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">أحجام مختلفة</h2>
            
            <div class="space-y-4">
                <div>
                    <p class="text-gray-600 mb-2">صغير:</p>
                    <div class="rating-input small" data-max-stars="5" data-initial-rating="3"></div>
                </div>
                
                <div>
                    <p class="text-gray-600 mb-2">متوسط (افتراضي):</p>
                    <div class="rating-input" data-max-stars="5" data-initial-rating="4"></div>
                </div>
                
                <div>
                    <p class="text-gray-600 mb-2">كبير:</p>
                    <div class="rating-input large" data-max-stars="5" data-initial-rating="5"></div>
                </div>
            </div>
        </div>
        
        <!-- مثال 4: نموذج تقييم كامل -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">نموذج تقييم كامل</h2>
            
            <form id="reviewForm" class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">تقييم الخدمة:</label>
                    <div class="rating-input" id="serviceRating" data-max-stars="5" data-initial-rating="0"></div>
                    <input type="hidden" id="serviceRatingValue" name="service_rating" value="0">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">تقييم الطعام:</label>
                    <div class="rating-input" id="foodRating" data-max-stars="5" data-initial-rating="0"></div>
                    <input type="hidden" id="foodRatingValue" name="food_rating" value="0">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">التعليق:</label>
                    <textarea class="w-full p-3 border border-gray-300 rounded-lg" rows="3" placeholder="اكتب تعليقك هنا..."></textarea>
                </div>
                
                <button type="submit" class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition">
                    إرسال التقييم
                </button>
            </form>
        </div>
    </div>

    <script src="{{ asset('js/rating-system.js') }}"></script>
    <script>
        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة التقييم التفاعلي الأول مع callback
            const rating1Container = document.getElementById('rating1');
            if (rating1Container) {
                new StarRating(rating1Container, {
                    maxStars: 5,
                    initialRating: 0,
                    onRate: function(rating) {
                        document.getElementById('ratingValue1').value = rating;
                        document.getElementById('selectedRating1').textContent = rating;
                    }
                });
            }
            
            // تهيئة تقييم الخدمة
            const serviceRatingContainer = document.getElementById('serviceRating');
            if (serviceRatingContainer) {
                new StarRating(serviceRatingContainer, {
                    maxStars: 5,
                    initialRating: 0,
                    onRate: function(rating) {
                        document.getElementById('serviceRatingValue').value = rating;
                    }
                });
            }
            
            // تهيئة تقييم الطعام
            const foodRatingContainer = document.getElementById('foodRating');
            if (foodRatingContainer) {
                new StarRating(foodRatingContainer, {
                    maxStars: 5,
                    initialRating: 0,
                    onRate: function(rating) {
                        document.getElementById('foodRatingValue').value = rating;
                    }
                });
            }
            
            // معالج إرسال النموذج
            document.getElementById('reviewForm').addEventListener('submit', function(e) {
                e.preventDefault();
                
                const serviceRating = document.getElementById('serviceRatingValue').value;
                const foodRating = document.getElementById('foodRatingValue').value;
                
                if (serviceRating == 0 || foodRating == 0) {
                    alert('يرجى إعطاء تقييم للخدمة والطعام');
                    return;
                }
                
                alert(`تم إرسال التقييم!\nالخدمة: ${serviceRating}/5\nالطعام: ${foodRating}/5`);
            });
        });
    </script>
</body>
</html>
