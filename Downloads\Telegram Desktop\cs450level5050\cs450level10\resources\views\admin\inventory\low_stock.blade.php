@extends('layouts.admin')

@section('title', 'المخزون المنخفض')

@section('content')
<div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
    <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">المخزون المنخفض</h2>
    <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 sm:space-x-reverse">
        <a href="{{ route('admin.inventory') }}" class="bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
            <i class="fas fa-arrow-right ml-2"></i>
            <span>العودة إلى المخزون</span>
        </a>
    </div>
</div>

<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
    <div class="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center">
        <i class="fas fa-exclamation-triangle text-red-500 ml-2"></i>
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">المكونات منخفضة المخزون</h3>
    </div>
    
    @if($lowStockItems->isEmpty())
        <div class="p-6 text-center">
            <p class="text-gray-500 dark:text-gray-400">لا توجد مكونات منخفضة المخزون حالياً.</p>
        </div>
    @else
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50 dark:bg-gray-700">
                    <tr>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المكون</th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الكمية المتبقية</th>
                        <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    @foreach($lowStockItems as $item)
                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900 dark:text-white">{{ $item->name }}</div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mr-2">
                                        <div class="bg-red-500 h-2.5 rounded-full" style="width: {{ min(($item->total_quantity / 10) * 100, 100) }}%"></div>
                                    </div>
                                    <div class="text-sm text-gray-500 dark:text-gray-300">
                                        {{ number_format($item->total_quantity, 2) }} {{ $item->unit }}
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-4 whitespace-nowrap">
                                <a href="{{ route('admin.inventory.create', ['ingredient_name' => $item->name]) }}" class="inline-flex items-center px-3 py-1 bg-primary text-white text-sm font-medium rounded-md hover:bg-primary/90 transition-all">
                                    <i class="fas fa-plus ml-1"></i>
                                    <span>إضافة مخزون</span>
                                </a>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    @endif
</div>

<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white">نصائح لإدارة المخزون</h3>
    </div>
    <div class="p-6">
        <ul class="list-disc list-inside space-y-2 text-gray-700 dark:text-gray-300">
            <li>تأكد من مراقبة مستويات المخزون بانتظام لتجنب نفاد المكونات.</li>
            <li>قم بطلب المكونات قبل وصولها إلى المستوى المنخفض للغاية.</li>
            <li>استخدم نظام "الوارد أولاً يصرف أولاً" (FIFO) للمكونات لتقليل الهدر.</li>
            <li>تحقق من تواريخ انتهاء الصلاحية بانتظام واستخدم المكونات التي تقترب من انتهاء صلاحيتها أولاً.</li>
            <li>قم بتحليل استهلاك المكونات بشكل دوري لتحسين عمليات الشراء.</li>
        </ul>
    </div>
</div>
@endsection
