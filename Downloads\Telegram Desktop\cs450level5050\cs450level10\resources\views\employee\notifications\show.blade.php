@extends('employee.layouts.app')

@section('title', 'تفاصيل الإشعار')

@section('content')
<div id="notification-details-page" class="page fade-in">
    <div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-2">تفاصيل الإشعار</h2>
            <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <a href="{{ route('employee.dashboard') }}" class="hover:text-primary">لوحة التحكم</a>
                <i class="fas fa-chevron-left mx-2 text-xs"></i>
                <a href="{{ route('employee.notifications') }}" class="hover:text-primary">الإشعارات</a>
                <i class="fas fa-chevron-left mx-2 text-xs"></i>
                <span>تفاصيل الإشعار</span>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="border-b border-gray-200 dark:border-gray-700 px-6 py-4">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white flex items-center">
                @if($notification->type == 'order')
                    <i class="fas fa-shopping-cart text-blue-500 dark:text-blue-300 ml-2"></i>
                @elseif($notification->type == 'reservation')
                    <i class="fas fa-calendar-check text-green-500 dark:text-green-300 ml-2"></i>
                @elseif($notification->type == 'inventory')
                    <i class="fas fa-box text-yellow-500 dark:text-yellow-300 ml-2"></i>
                @elseif($notification->type == 'system')
                    <i class="fas fa-cog text-purple-500 dark:text-purple-300 ml-2"></i>
                @else
                    <i class="fas fa-bell text-gray-500 dark:text-gray-300 ml-2"></i>
                @endif
                {{ $notification->title }}
            </h3>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {{ $notification->created_at->format('Y-m-d H:i') }}
            </p>
        </div>
        <div class="p-6">
            <div class="bg-gray-50 dark:bg-gray-700/30 rounded-lg p-4 mb-6">
                <p class="text-gray-700 dark:text-gray-300">
                    {{ $notification->message }}
                </p>
            </div>

            @if($notification->data)
                <div class="mb-6">
                    <h4 class="text-md font-semibold text-gray-800 dark:text-white mb-3">تفاصيل إضافية</h4>
                    <div class="bg-gray-50 dark:bg-gray-700/30 rounded-lg p-4">
                        <dl class="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-3">
                            @foreach(json_decode($notification->data, true) as $key => $value)
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">{{ $key }}</dt>
                                    <dd class="mt-1 text-sm text-gray-800 dark:text-white">{{ $value }}</dd>
                                </div>
                            @endforeach
                        </dl>
                    </div>
                </div>
            @endif

            <div class="flex justify-between items-center mt-8">
                <div>
                    @if(!$notification->is_read)
                        <form action="{{ route('employee.notifications.mark-read', $notification->notification_id) }}" method="POST" class="inline">
                            @csrf
                            <button type="submit" class="inline-flex items-center px-4 py-2 bg-primary hover:bg-primary/90 text-white rounded-md transition-colors">
                                <i class="fas fa-check ml-2"></i>
                                <span>تحديد كمقروء</span>
                            </button>
                        </form>
                    @else
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                            <i class="fas fa-check-circle ml-1.5"></i>مقروء
                        </span>
                    @endif
                </div>
                <div class="flex space-x-3 space-x-reverse">
                    <form action="{{ route('employee.notifications.delete', $notification->notification_id) }}" method="POST">
                        @csrf
                        @method('DELETE')
                        <button type="submit" class="inline-flex items-center px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-md transition-colors">
                            <i class="fas fa-trash-alt ml-2"></i>
                            <span>حذف</span>
                        </button>
                    </form>
                    <a href="{{ route('employee.notifications') }}" class="inline-flex items-center px-4 py-2 bg-gray-300 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-400 dark:hover:bg-gray-600 transition-colors">
                        <i class="fas fa-arrow-right ml-2"></i>
                        <span>العودة للإشعارات</span>
                    </a>
                </div>
            </div>

            @if($notification->action_url)
                <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <a href="{{ $notification->action_url }}" class="inline-flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors">
                        <i class="fas fa-external-link-alt ml-2"></i>
                        <span>{{ $notification->action_text ?: 'الانتقال إلى الصفحة المرتبطة' }}</span>
                    </a>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
