<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="test-token">
    <title>اختبار سريع للأزرار</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-2xl font-bold mb-6 text-center">اختبار سريع لأزرار الحجوزات</h1>
        
        <div class="reservation-card bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-lg font-bold mb-4">R0002</h3>
            
            <div class="space-y-3">
                <button class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                    <i class="fas fa-edit ml-1"></i>تعديل الحجز
                </button>
                
                <button class="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
                    <i class="fas fa-share ml-1"></i>مشاركة التفاصيل
                </button>
                
                <button class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                    <i class="fas fa-times ml-1"></i>إلغاء الحجز
                </button>
                
                <button class="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
                    <i class="fas fa-directions ml-1"></i>الاتجاهات
                </button>
                
                <button class="bg-orange-500 text-white px-4 py-2 rounded hover:bg-orange-600">
                    <i class="fas fa-redo ml-1"></i>حجز مماثل
                </button>
                
                <button class="bg-yellow-500 text-white px-4 py-2 rounded hover:bg-yellow-600">
                    <i class="fas fa-star ml-1"></i>تقييم التجربة
                </button>
                
                <button class="bg-indigo-500 text-white px-4 py-2 rounded hover:bg-indigo-600">
                    <i class="fas fa-download ml-1"></i>تحميل التأكيد
                </button>
            </div>
        </div>
        
        <div id="log" class="bg-gray-800 text-green-400 p-4 rounded-lg h-64 overflow-y-auto font-mono text-sm">
            <div>جاهز للاختبار...</div>
        </div>
    </div>

    <script>
    function log(message) {
        const logDiv = document.getElementById('log');
        const time = new Date().toLocaleTimeString();
        logDiv.innerHTML += `<div>[${time}] ${message}</div>`;
        logDiv.scrollTop = logDiv.scrollHeight;
    }

    // إعداد أزرار الإجراءات
    function setupActionButtons() {
        document.querySelectorAll('button').forEach(btn => {
            const buttonText = btn.textContent.trim();
            
            if (buttonText.includes('تعديل الحجز')) {
                btn.addEventListener('click', function() {
                    log('✅ تم النقر على تعديل الحجز');
                    alert('تعديل الحجز يعمل!');
                });
            }
            else if (buttonText.includes('مشاركة التفاصيل')) {
                btn.addEventListener('click', function() {
                    log('✅ تم النقر على مشاركة التفاصيل');
                    alert('مشاركة التفاصيل تعمل!');
                });
            }
            else if (buttonText.includes('إلغاء الحجز')) {
                btn.addEventListener('click', function() {
                    log('✅ تم النقر على إلغاء الحجز');
                    if (confirm('هل تريد إلغاء الحجز؟')) {
                        alert('إلغاء الحجز يعمل!');
                    }
                });
            }
            else if (buttonText.includes('الاتجاهات')) {
                btn.addEventListener('click', function() {
                    log('✅ تم النقر على الاتجاهات');
                    alert('الاتجاهات تعمل!');
                });
            }
            else if (buttonText.includes('حجز مماثل')) {
                btn.addEventListener('click', function() {
                    log('✅ تم النقر على حجز مماثل');
                    alert('حجز مماثل يعمل!');
                });
            }
            else if (buttonText.includes('تقييم التجربة')) {
                btn.addEventListener('click', function() {
                    log('✅ تم النقر على تقييم التجربة');
                    alert('تقييم التجربة يعمل!');
                });
            }
            else if (buttonText.includes('تحميل التأكيد')) {
                btn.addEventListener('click', function() {
                    log('✅ تم النقر على تحميل التأكيد');
                    alert('تحميل التأكيد يعمل!');
                });
            }
        });
    }

    document.addEventListener('DOMContentLoaded', function() {
        log('🚀 تم تحميل الصفحة');
        setupActionButtons();
        log('✅ تم إعداد جميع الأزرار');
    });
    </script>
</body>
</html>
