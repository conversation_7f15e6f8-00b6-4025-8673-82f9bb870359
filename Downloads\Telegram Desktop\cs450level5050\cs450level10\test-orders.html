<!DOCTYPE html>
<html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>اختبار نظام الطلبات</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link
            rel="stylesheet"
            href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
        />
    </head>
    <body class="bg-gray-100">
        <div class="container mx-auto px-4 py-8">
            <h1 class="text-3xl font-bold text-center mb-8">
                اختبار نظام الطلبات
            </h1>

            <!-- روابط الاختبار -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- اختبار صفحة الطلبات -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-bold mb-4">
                        <i class="fas fa-shopping-bag text-blue-500 ml-2"></i>
                        صفحة الطلبات
                    </h3>
                    <p class="text-gray-600 mb-4">
                        عرض جميع الطلبات مع أزرار الإجراءات
                    </p>
                    <a
                        href="http://127.0.0.1:8000/customer/orders"
                        class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg inline-block"
                    >
                        <i class="fas fa-external-link-alt ml-1"></i>
                        فتح الصفحة
                    </a>
                </div>

                <!-- اختبار صفحة إلغاء الطلب -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-bold mb-4">
                        <i class="fas fa-times-circle text-red-500 ml-2"></i>
                        إلغاء الطلب
                    </h3>
                    <p class="text-gray-600 mb-4">
                        صفحة إلغاء الطلب مع الأسباب
                    </p>
                    <a
                        href="http://127.0.0.1:8000/customer/orders/1/cancel"
                        class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg inline-block"
                    >
                        <i class="fas fa-external-link-alt ml-1"></i>
                        فتح الصفحة
                    </a>
                </div>

                <!-- اختبار صفحة الفاتورة -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-bold mb-4">
                        <i class="fas fa-file-invoice text-green-500 ml-2"></i>
                        الفاتورة
                    </h3>
                    <p class="text-gray-600 mb-4">
                        صفحة الفاتورة القابلة للطباعة
                    </p>
                    <a
                        href="http://127.0.0.1:8000/customer/orders/1/invoice"
                        class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg inline-block"
                    >
                        <i class="fas fa-external-link-alt ml-1"></i>
                        فتح الصفحة
                    </a>
                </div>

                <!-- اختبار تفاصيل الطلب -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-bold mb-4">
                        <i class="fas fa-eye text-purple-500 ml-2"></i>
                        تفاصيل الطلب
                    </h3>
                    <p class="text-gray-600 mb-4">
                        صفحة تفاصيل الطلب مع الأزرار
                    </p>
                    <a
                        href="http://127.0.0.1:8000/customer/orders/1"
                        class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg inline-block"
                    >
                        <i class="fas fa-external-link-alt ml-1"></i>
                        فتح الصفحة
                    </a>
                </div>

                <!-- اختبار تسجيل الدخول -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-bold mb-4">
                        <i class="fas fa-sign-in-alt text-orange-500 ml-2"></i>
                        تسجيل الدخول
                    </h3>
                    <p class="text-gray-600 mb-4">صفحة تسجيل الدخول للعملاء</p>
                    <a
                        href="http://127.0.0.1:8000/login"
                        class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg inline-block"
                    >
                        <i class="fas fa-external-link-alt ml-1"></i>
                        فتح الصفحة
                    </a>
                </div>

                <!-- اختبار الصفحة الرئيسية -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-bold mb-4">
                        <i class="fas fa-home text-indigo-500 ml-2"></i>
                        الصفحة الرئيسية
                    </h3>
                    <p class="text-gray-600 mb-4">الصفحة الرئيسية للعملاء</p>
                    <a
                        href="http://127.0.0.1:8000/"
                        class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded-lg inline-block"
                    >
                        <i class="fas fa-external-link-alt ml-1"></i>
                        فتح الصفحة
                    </a>
                </div>

                <!-- إنشاء بيانات تجريبية -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-bold mb-4">
                        <i class="fas fa-database text-yellow-500 ml-2"></i>
                        إنشاء بيانات تجريبية
                    </h3>
                    <p class="text-gray-600 mb-4">
                        إنشاء مستخدم وطلبات تجريبية
                    </p>
                    <a
                        href="http://127.0.0.1:8000/create-test-orders"
                        class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg inline-block"
                    >
                        <i class="fas fa-plus ml-1"></i>
                        إنشاء البيانات
                    </a>
                </div>

                <!-- تسجيل دخول تجريبي -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h3 class="text-lg font-bold mb-4">
                        <i class="fas fa-user-check text-teal-500 ml-2"></i>
                        تسجيل دخول تجريبي
                    </h3>
                    <p class="text-gray-600 mb-4">
                        تسجيل دخول تلقائي للعميل التجريبي
                    </p>
                    <a
                        href="http://127.0.0.1:8000/test-customer-login"
                        class="bg-teal-500 hover:bg-teal-600 text-white px-4 py-2 rounded-lg inline-block"
                    >
                        <i class="fas fa-sign-in-alt ml-1"></i>
                        تسجيل الدخول
                    </a>
                </div>
            </div>

            <!-- معلومات مهمة -->
            <div
                class="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6"
            >
                <h3 class="text-lg font-bold text-yellow-800 mb-4">
                    <i class="fas fa-info-circle ml-2"></i>
                    معلومات مهمة
                </h3>
                <ul class="text-yellow-700 space-y-2">
                    <li>
                        • تأكد من تشغيل الخادم المحلي:
                        <code>php artisan serve</code>
                    </li>
                    <li>• قد تحتاج لتسجيل الدخول أولاً لرؤية صفحات الطلبات</li>
                    <li>• استخدم معرف طلب موجود في قاعدة البيانات</li>
                    <li>• تحقق من أن قاعدة البيانات تحتوي على بيانات اختبار</li>
                </ul>
            </div>

            <!-- حالة الخادم -->
            <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="text-lg font-bold text-blue-800 mb-4">
                    <i class="fas fa-server ml-2"></i>
                    حالة الخادم
                </h3>
                <div id="server-status" class="text-blue-700">
                    جاري التحقق من حالة الخادم...
                </div>
            </div>
        </div>

        <script>
            // التحقق من حالة الخادم
            fetch("http://127.0.0.1:8000/")
                .then((response) => {
                    if (response.ok) {
                        document.getElementById("server-status").innerHTML =
                            '<i class="fas fa-check-circle text-green-500 ml-1"></i> الخادم يعمل بشكل طبيعي';
                    } else {
                        document.getElementById("server-status").innerHTML =
                            '<i class="fas fa-exclamation-triangle text-yellow-500 ml-1"></i> الخادم يعمل لكن هناك مشاكل';
                    }
                })
                .catch((error) => {
                    document.getElementById("server-status").innerHTML =
                        '<i class="fas fa-times-circle text-red-500 ml-1"></i> الخادم لا يعمل - تأكد من تشغيل php artisan serve';
                });
        </script>
    </body>
</html>
