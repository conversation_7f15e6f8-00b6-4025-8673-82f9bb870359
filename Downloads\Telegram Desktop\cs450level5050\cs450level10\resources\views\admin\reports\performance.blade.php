@extends('layouts.admin')

@section('title', 'تقرير مقارنة الأداء')

@section('content')
<div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
    <div>
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white">تقرير مقارنة الأداء</h2>
        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">مقارنة أداء العام الحالي مع العام السابق</p>
    </div>
    <div class="mt-4 md:mt-0">
        <a href="{{ route('admin.reports') }}" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
            <i class="fas fa-arrow-right ml-2"></i>
            <span>العودة للتقارير</span>
        </a>
    </div>
</div>

<!-- ملخص المقارنة -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
    <!-- المبيعات -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">المبيعات</h3>
        <div class="flex items-center mb-4">
            <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3 ml-4">
                <i class="fas fa-shopping-cart text-blue-500 text-xl"></i>
            </div>
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">العام الحالي</p>
                <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ number_format($currentYearSales, 2) }}د.ل</p>
            </div>
        </div>
        <div class="flex items-center">
            <div class="rounded-full bg-gray-100 dark:bg-gray-700 p-3 ml-4">
                <i class="fas fa-shopping-cart text-gray-500 text-xl"></i>
            </div>
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">العام السابق</p>
                <p class="text-xl font-bold text-gray-600 dark:text-gray-400">{{ number_format($previousYearSales, 2) }} د.ل</p>
            </div>
        </div>
        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center">
                <div class="flex-1">
                    <p class="text-sm text-gray-600 dark:text-gray-400">نسبة النمو</p>
                </div>
                <div>
                    <p class="text-lg font-bold {{ $salesGrowthRate >= 0 ? 'text-green-500' : 'text-red-500' }}">
                        {{ number_format($salesGrowthRate, 1) }}%
                        @if($salesGrowthRate >= 0)
                            <i class="fas fa-arrow-up"></i>
                        @else
                            <i class="fas fa-arrow-down"></i>
                        @endif
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- المصروفات -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">المصروفات</h3>
        <div class="flex items-center mb-4">
            <div class="rounded-full bg-red-100 dark:bg-red-900/30 p-3 ml-4">
                <i class="fas fa-money-bill-wave text-red-500 text-xl"></i>
            </div>
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">العام الحالي</p>
                <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ number_format($currentYearExpenses, 2) }} د.ل</p>س</p>
            </div>
        </div>
        <div class="flex items-center">
            <div class="rounded-full bg-gray-100 dark:bg-gray-700 p-3 ml-4">
                <i class="fas fa-money-bill-wave text-gray-500 text-xl"></i>
            </div>
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">العام السابق</p>
                <p class="text-xl font-bold text-gray-600 dark:text-gray-400">{{ number_format($previousYearExpenses, 2) }} د.ل</p>
            </div>
        </div>
        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center">
                <div class="flex-1">
                    <p class="text-sm text-gray-600 dark:text-gray-400">نسبة التغير</p>
                </div>
                <div>
                    @php
                        $expensesGrowthRate = $previousYearExpenses > 0 ? (($currentYearExpenses - $previousYearExpenses) / $previousYearExpenses) * 100 : 0;
                    @endphp
                    <p class="text-lg font-bold {{ $expensesGrowthRate <= 0 ? 'text-green-500' : 'text-red-500' }}">
                        {{ number_format($expensesGrowthRate, 1) }}%
                        @if($expensesGrowthRate <= 0)
                            <i class="fas fa-arrow-down"></i>
                        @else
                            <i class="fas fa-arrow-up"></i>
                        @endif
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- الأرباح -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">الأرباح</h3>
        <div class="flex items-center mb-4">
            <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-3 ml-4">
                <i class="fas fa-chart-line text-green-500 text-xl"></i>
            </div>
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">العام الحالي</p>
                <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ number_format($currentYearProfit, 2) }} د.ل</p>
            </div>
        </div>
        <div class="flex items-center">
            <div class="rounded-full bg-gray-100 dark:bg-gray-700 p-3 ml-4">
                <i class="fas fa-chart-line text-gray-500 text-xl"></i>
            </div>
            <div>
                <p class="text-sm text-gray-600 dark:text-gray-400">العام السابق</p>
                <p class="text-xl font-bold text-gray-600 dark:text-gray-400">{{ number_format($previousYearProfit, 2) }} د.ل</p>
            </div>
        </div>
        <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center">
                <div class="flex-1">
                    <p class="text-sm text-gray-600 dark:text-gray-400">نسبة النمو</p>
                </div>
                <div>
                    <p class="text-lg font-bold {{ $profitGrowthRate >= 0 ? 'text-green-500' : 'text-red-500' }}">
                        {{ number_format($profitGrowthRate, 1) }}%
                        @if($profitGrowthRate >= 0)
                            <i class="fas fa-arrow-up"></i>
                        @else
                            <i class="fas fa-arrow-down"></i>
                        @endif
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- مقارنة المبيعات الشهرية -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">مقارنة المبيعات الشهرية</h3>
    <div class="h-80">
        <canvas id="monthlySalesChart"></canvas>
    </div>
</div>

<!-- مقارنة الأرباح والمصروفات -->
<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">مقارنة المبيعات</h3>
        <div class="h-64">
            <canvas id="salesComparisonChart"></canvas>
        </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">مقارنة الأرباح</h3>
        <div class="h-64">
            <canvas id="profitComparisonChart"></canvas>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // مقارنة المبيعات الشهرية
        const monthlyCtx = document.getElementById('monthlySalesChart').getContext('2d');
        const monthlyData = {
            labels: [
                @foreach($monthlyData as $data)
                    '{{ $data['month'] }}',
                @endforeach
            ],
            datasets: [
                {
                    label: 'العام الحالي',
                    data: [
                        @foreach($monthlyData as $data)
                            {{ $data['current_year'] }},
                        @endforeach
                    ],
                    backgroundColor: 'rgba(66, 153, 225, 0.2)',
                    borderColor: 'rgba(66, 153, 225, 1)',
                    borderWidth: 2,
                    tension: 0.4
                },
                {
                    label: 'العام السابق',
                    data: [
                        @foreach($monthlyData as $data)
                            {{ $data['previous_year'] }},
                        @endforeach
                    ],
                    backgroundColor: 'rgba(160, 174, 192, 0.2)',
                    borderColor: 'rgba(160, 174, 192, 1)',
                    borderWidth: 2,
                    tension: 0.4
                }
            ]
        };
        
        new Chart(monthlyCtx, {
            type: 'line',
            data: monthlyData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            font: {
                                family: 'Tajawal'
                            }
                        }
                    }
                }
            }
        });
        
        // مقارنة المبيعات
        const salesCtx = document.getElementById('salesComparisonChart').getContext('2d');
        const salesData = {
            labels: ['العام الحالي', 'العام السابق'],
            datasets: [{
                data: [{{ $currentYearSales }}, {{ $previousYearSales }}],
                backgroundColor: [
                    'rgba(66, 153, 225, 0.7)',
                    'rgba(160, 174, 192, 0.7)'
                ],
                borderColor: [
                    'rgba(66, 153, 225, 1)',
                    'rgba(160, 174, 192, 1)'
                ],
                borderWidth: 1
            }]
        };
        
        new Chart(salesCtx, {
            type: 'pie',
            data: salesData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                family: 'Tajawal'
                            }
                        }
                    }
                }
            }
        });
        
        // مقارنة الأرباح
        const profitCtx = document.getElementById('profitComparisonChart').getContext('2d');
        const profitData = {
            labels: ['العام الحالي', 'العام السابق'],
            datasets: [{
                data: [{{ $currentYearProfit }}, {{ $previousYearProfit }}],
                backgroundColor: [
                    'rgba(72, 187, 120, 0.7)',
                    'rgba(160, 174, 192, 0.7)'
                ],
                borderColor: [
                    'rgba(72, 187, 120, 1)',
                    'rgba(160, 174, 192, 1)'
                ],
                borderWidth: 1
            }]
        };
        
        new Chart(profitCtx, {
            type: 'pie',
            data: profitData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            font: {
                                family: 'Tajawal'
                            }
                        }
                    }
                }
            }
        });
    });
</script>
@endpush
@endsection
