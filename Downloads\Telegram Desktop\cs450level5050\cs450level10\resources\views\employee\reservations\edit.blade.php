@extends('employee.layouts.app')

@section('title', 'تعديل الحجز - نظام إدارة المطعم')

@section('page-title', 'تعديل الحجز')

@section('content')
<div class="container mx-auto px-4 py-6">
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-800 dark:text-white">تعديل الحجز</h1>
        <p class="text-gray-600 dark:text-gray-400">تعديل معلومات الحجز رقم {{ $reservation->reservation_id }}</p>
    </div>

    @if(session('error'))
    <div class="bg-red-100 border-r-4 border-red-500 text-red-700 p-4 mb-6 rounded-md dark:bg-red-900/30 dark:text-red-500 dark:border-red-500">
        <p>{{ session('error') }}</p>
    </div>
    @endif

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-lg font-bold text-gray-800 dark:text-white">معلومات الحجز</h2>
        </div>

        <form action="{{ route('employee.reservations.update', $reservation->reservation_id) }}" method="POST" class="p-6 space-y-6">
            @csrf
            @method('PUT')

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- معلومات العميل (غير قابلة للتعديل) -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">العميل</label>
                    <div class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
                        {{ $reservation->user->first_name }} {{ $reservation->user->last_name }}
                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ $reservation->user->phone }}</div>
                    </div>
                </div>

                <!-- اختيار الطاولة -->
                <div>
                    <label for="table_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الطاولة</label>
                    <select id="table_id" name="table_id" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white @error('table_id') border-red-500 dark:border-red-500 @enderror">
                        @foreach($tables as $table)
                        <option value="{{ $table->table_id }}" {{ old('table_id', $reservation->table_id) == $table->table_id ? 'selected' : '' }}>
                            طاولة رقم {{ $table->table_number }} ({{ $table->capacity }} أشخاص)
                        </option>
                        @endforeach
                    </select>
                    @error('table_id')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- وقت الحجز -->
                <div>
                    <label for="reservation_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">وقت الحجز</label>
                    <input type="datetime-local" id="reservation_time" name="reservation_time" value="{{ old('reservation_time', $reservation->reservation_time->format('Y-m-d\TH:i')) }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white @error('reservation_time') border-red-500 dark:border-red-500 @enderror">
                    @error('reservation_time')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- مدة الحجز -->
                <div>
                    <label for="duration" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">مدة الحجز (بالدقائق)</label>
                    <select id="duration" name="duration" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white @error('duration') border-red-500 dark:border-red-500 @enderror">
                        <option value="30" {{ old('duration', $reservation->duration) == 30 ? 'selected' : '' }}>30 دقيقة</option>
                        <option value="60" {{ old('duration', $reservation->duration) == 60 ? 'selected' : '' }}>ساعة واحدة</option>
                        <option value="90" {{ old('duration', $reservation->duration) == 90 ? 'selected' : '' }}>ساعة ونصف</option>
                        <option value="120" {{ old('duration', $reservation->duration) == 120 ? 'selected' : '' }}>ساعتان</option>
                        <option value="180" {{ old('duration', $reservation->duration) == 180 ? 'selected' : '' }}>3 ساعات</option>
                        <option value="240" {{ old('duration', $reservation->duration) == 240 ? 'selected' : '' }}>4 ساعات</option>
                    </select>
                    @error('duration')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- حالة الحجز -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">حالة الحجز</label>
                    <select id="status" name="status" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white @error('status') border-red-500 dark:border-red-500 @enderror">
                        <option value="confirmed" {{ old('status', $reservation->status) == 'confirmed' ? 'selected' : '' }}>مؤكد</option>
                        <option value="canceled" {{ old('status', $reservation->status) == 'canceled' ? 'selected' : '' }}>ملغي</option>
                        <option value="completed" {{ old('status', $reservation->status) == 'completed' ? 'selected' : '' }}>مكتمل</option>
                    </select>
                    @error('status')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>

                <!-- ملاحظات -->
                <div class="md:col-span-2">
                    <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">ملاحظات</label>
                    <textarea id="notes" name="notes" rows="3" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white @error('notes') border-red-500 dark:border-red-500 @enderror">{{ old('notes', $reservation->notes) }}</textarea>
                    @error('notes')
                    <p class="mt-1 text-sm text-red-500">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <div class="flex justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
                <a href="{{ route('employee.reservations') }}" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600">
                    إلغاء
                </a>
                <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50">
                    حفظ التغييرات
                </button>
            </div>
        </form>
    </div>

    <div class="mt-8">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
            <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                <h2 class="text-lg font-bold text-gray-800 dark:text-white">معلومات إضافية</h2>
            </div>

            <div class="p-6 space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                        <h3 class="font-medium text-gray-800 dark:text-white mb-2">تفاصيل الحجز</h3>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">رقم الحجز:</span>
                                <span class="text-gray-800 dark:text-white">{{ $reservation->reservation_id }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">تاريخ الإنشاء:</span>
                                <span class="text-gray-800 dark:text-white">{{ $reservation->created_at->format('Y-m-d H:i') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">آخر تحديث:</span>
                                <span class="text-gray-800 dark:text-white">{{ $reservation->updated_at->format('Y-m-d H:i') }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-md">
                        <h3 class="font-medium text-gray-800 dark:text-white mb-2">معلومات العميل</h3>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">الاسم:</span>
                                <span class="text-gray-800 dark:text-white">{{ $reservation->user->first_name }} {{ $reservation->user->last_name }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">البريد الإلكتروني:</span>
                                <span class="text-gray-800 dark:text-white">{{ $reservation->user->email }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">رقم الهاتف:</span>
                                <span class="text-gray-800 dark:text-white">{{ $reservation->user->phone }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-md border-r-4 border-yellow-500 dark:border-yellow-600">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-yellow-500 dark:text-yellow-600"></i>
                        </div>
                        <div class="mr-3">
                            <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-500">ملاحظة هامة</h3>
                            <div class="mt-1 text-sm text-yellow-700 dark:text-yellow-400">
                                <p>تأكد من عدم وجود تعارض مع حجوزات أخرى عند تغيير وقت الحجز أو الطاولة. سيتم إرسال إشعار للعميل عند تحديث حالة الحجز.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحديث حالة الطاولات عند تغيير وقت الحجز
        const reservationTimeInput = document.getElementById('reservation_time');
        const durationSelect = document.getElementById('duration');
        const tableSelect = document.getElementById('table_id');

        if (reservationTimeInput && durationSelect && tableSelect) {
            const updateTableAvailability = function() {
                const reservationTime = reservationTimeInput.value;
                const duration = durationSelect.value;
                const tableId = tableSelect.value;

                if (reservationTime && duration && tableId) {
                    // يمكن إضافة طلب AJAX هنا للتحقق من توافر الطاولات في الوقت المحدد
                    console.log('Checking availability for table:', tableId, 'time:', reservationTime, 'duration:', duration);
                }
            };

            reservationTimeInput.addEventListener('change', updateTableAvailability);
            durationSelect.addEventListener('change', updateTableAvailability);
            tableSelect.addEventListener('change', updateTableAvailability);
        }
    });
</script>
@endsection
