# 📋 دليل نسخ المشروع إلى جهاز جديد

## الملفات المطلوبة للنسخ

### ✅ الملفات الأساسية (يجب نسخها):
```
cs450level10/
├── app/                    # كود التطبيق
├── bootstrap/              # ملفات البدء
├── config/                 # ملفات الإعدادات
├── database/               # الهجرات والبذور
├── lang/                   # ملفات اللغة
├── public/                 # الملفات العامة
├── resources/              # القوالب والأصول
├── routes/                 # ملفات التوجيه
├── storage/app/            # ملفات التطبيق
├── tests/                  # الاختبارات
├── .env.example            # مثال ملف البيئة
├── artisan                 # أداة Laravel
├── composer.json           # تبعيات PHP
├── composer.lock           # قفل التبعيات
├── package.json            # تبعيات Node.js
├── package-lock.json       # قفل تبعيات Node.js
├── phpunit.xml             # إعدادات الاختبار
├── postcss.config.js       # إعدادات PostCSS
├── tailwind.config.js      # إعدادات Tailwind
├── vite.config.js          # إعدادات Vite
├── setup.bat               # إعداد تلقائي Windows
├── setup.sh                # إعداد تلقائي Linux/Mac
├── check_system.php        # فحص النظام
├── database_setup.sql      # إعداد قاعدة البيانات
└── جميع ملفات .md          # الوثائق
```

### ❌ الملفات التي لا تحتاج نسخ:
```
├── .env                    # ملف البيئة (خاص بكل جهاز)
├── vendor/                 # تبعيات PHP (يتم تثبيتها)
├── node_modules/           # تبعيات Node.js (يتم تثبيتها)
├── storage/logs/           # ملفات السجل
├── storage/framework/      # ملفات مؤقتة
├── public/hot              # ملف Vite المؤقت
├── public/build/           # ملفات مبنية (يتم إنشاؤها)
└── database/database.sqlite # قاعدة البيانات القديمة
```

## 🚀 خطوات النسخ والإعداد

### الطريقة 1: النسخ اليدوي
1. **انسخ المجلد كاملاً** (باستثناء الملفات المذكورة أعلاه)
2. **احذف الملفات غير المطلوبة**:
   ```bash
   # في الجهاز الجديد
   rm -rf vendor/ node_modules/ .env storage/logs/* public/build/
   ```
3. **شغل الإعداد التلقائي**:
   ```bash
   # Windows
   setup.bat
   
   # Linux/Mac
   chmod +x setup.sh && ./setup.sh
   ```

### الطريقة 2: استخدام Git (الأفضل)
```bash
# في الجهاز الأصلي - إنشاء repository
git init
git add .
git commit -m "Initial commit"

# رفع إلى GitHub/GitLab (اختياري)
git remote add origin [your-repo-url]
git push -u origin main

# في الجهاز الجديد
git clone [your-repo-url]
cd cs450level10
./setup.sh  # أو setup.bat
```

### الطريقة 3: ضغط وإرسال
```bash
# في الجهاز الأصلي
# احذف الملفات غير المطلوبة أولاً
rm -rf vendor/ node_modules/ .env storage/logs/* public/build/

# اضغط المجلد
zip -r eat_hub_project.zip cs450level10/

# في الجهاز الجديد
unzip eat_hub_project.zip
cd cs450level10
./setup.sh  # أو setup.bat
```

## ⚙️ إعدادات خاصة بكل جهاز

### 1. ملف .env
```env
# عدل هذه القيم حسب الجهاز الجديد
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=eat_hub_new
DB_USERNAME=root
DB_PASSWORD=your_new_password

APP_URL=http://localhost:8000
# أو http://your-domain.com للإنتاج
```

### 2. إعدادات البريد الإلكتروني
```env
MAIL_HOST=your-smtp-server.com
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
```

### 3. إعدادات الحسابات الاجتماعية
```env
GOOGLE_CLIENT_ID=your-google-client-id
FACEBOOK_CLIENT_ID=your-facebook-app-id
```

## 🔍 التحقق من النجاح

### 1. فحص النظام
```bash
php check_system.php
```

### 2. اختبار الاتصال بقاعدة البيانات
```bash
php artisan tinker
DB::connection()->getPdo();
exit
```

### 3. تشغيل الخادم
```bash
php artisan serve
```

### 4. فتح المتصفح
افتح: `http://localhost:8000`

## 🆘 حل المشاكل الشائعة

### مشكلة: "Class not found"
```bash
composer dump-autoload
```

### مشكلة: "Permission denied"
```bash
# Linux/Mac
chmod -R 775 storage bootstrap/cache

# Windows - شغل Command Prompt كمدير
```

### مشكلة: "Key not set"
```bash
php artisan key:generate
```

### مشكلة: "Database connection failed"
1. تأكد من تشغيل MySQL
2. تحقق من بيانات الاتصال في `.env`
3. تأكد من وجود قاعدة البيانات

## 📝 قائمة مراجعة سريعة

- [ ] نسخ جميع الملفات المطلوبة
- [ ] حذف الملفات غير المطلوبة
- [ ] تشغيل `composer install`
- [ ] تشغيل `npm install`
- [ ] نسخ `.env.example` إلى `.env`
- [ ] تعديل إعدادات قاعدة البيانات في `.env`
- [ ] إنشاء قاعدة البيانات
- [ ] تشغيل `php artisan key:generate`
- [ ] تشغيل `php artisan migrate`
- [ ] تشغيل `php artisan db:seed`
- [ ] تشغيل `php artisan storage:link`
- [ ] تشغيل `npm run build`
- [ ] اختبار `php artisan serve`

## 🎯 نصائح للنجاح

1. **استخدم الإعداد التلقائي**: ملفات `setup.bat` و `setup.sh` تقوم بمعظم العمل
2. **احتفظ بنسخة احتياطية**: من ملف `.env` الأصلي
3. **اختبر على بيئة محلية أولاً**: قبل النشر على الخادم
4. **استخدم Git**: لتتبع التغييرات وسهولة النسخ

---

**💡 نصيحة**: استخدم `setup.bat` أو `setup.sh` لإعداد تلقائي سريع وآمن!
