<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware) {
        // تسجيل middleware للروابط
        $middleware->alias([
            'admin' => \App\Http\Middleware\AdminMiddleware::class,
            'admin.access' => \App\Http\Middleware\AdminAccessMiddleware::class,
            'employee' => \App\Http\Middleware\EmployeeMiddleware::class,
            'permission' => \App\Http\Middleware\PermissionMiddleware::class,
            'locale' => \App\Http\Middleware\SetLocale::class,
        ]);

        // إضافة middleware اللغة لجميع الطلبات
        $middleware->append(\App\Http\Middleware\SetLocale::class);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        // تهيئة معالجة الاستثناءات
    })->create();