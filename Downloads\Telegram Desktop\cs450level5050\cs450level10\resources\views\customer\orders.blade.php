@extends('customer.layouts.app')

@section('title', 'طلباتي - Eat Hub')

@section('content')
<div class="container mx-auto px-4 py-8">
    <div class="max-w-6xl mx-auto">
        <!-- شريط التنقل السريع -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
            <div class="flex flex-wrap gap-2 justify-center md:justify-start">
                <a href="{{ route('customer.dashboard') }}" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white px-4 py-2 rounded-lg text-sm transition">
                    <i class="fas fa-tachometer-alt ml-1"></i>لوحة التحكم
                </a>
                <a href="{{ route('customer.profile') }}" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white px-4 py-2 rounded-lg text-sm transition">
                    <i class="fas fa-user ml-1"></i>الملف الشخصي
                </a>
                <a href="{{ route('customer.orders') }}" class="bg-primary text-white px-4 py-2 rounded-lg text-sm transition">
                    <i class="fas fa-shopping-bag ml-1"></i>طلباتي
                </a>
                <a href="{{ route('customer.reservations') }}" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white px-4 py-2 rounded-lg text-sm transition">
                    <i class="fas fa-calendar-alt ml-1"></i>حجوزاتي
                </a>
                <a href="{{ route('customer.index') }}" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white px-4 py-2 rounded-lg text-sm transition">
                    <i class="fas fa-home ml-1"></i>الرئيسية
                </a>
            </div>
        </div>

        <!-- عنوان الصفحة -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-2">طلباتي</h1>
                <p class="text-gray-600 dark:text-gray-400">تتبع جميع طلباتك السابقة والحالية</p>
            </div>
            <a href="{{ route('customer.menu') }}" class="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition">
                <i class="fas fa-plus ml-2"></i>
                طلب جديد
            </a>
        </div>

        <!-- فلاتر الطلبات -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <div class="flex flex-wrap gap-4 items-center">
                <div class="flex space-x-2 space-x-reverse">
                    <a href="{{ route('customer.orders') }}" class="order-filter-btn {{ $status == 'all' ? 'active bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white' }} px-4 py-2 rounded-full transition text-sm">
                        جميع الطلبات
                    </a>
                    <a href="{{ route('customer.orders', ['status' => 'pending']) }}" class="order-filter-btn {{ $status == 'pending' ? 'active bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white' }} px-4 py-2 rounded-full transition text-sm">
                        قيد التجهيز
                    </a>
                    <a href="{{ route('customer.orders', ['status' => 'completed']) }}" class="order-filter-btn {{ $status == 'completed' ? 'active bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white' }} px-4 py-2 rounded-full transition text-sm">
                        مكتملة
                    </a>
                    <a href="{{ route('customer.orders', ['status' => 'cancelled']) }}" class="order-filter-btn {{ $status == 'cancelled' ? 'active bg-primary text-white' : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-primary hover:text-white' }} px-4 py-2 rounded-full transition text-sm">
                        ملغية
                    </a>
                </div>
            </div>
        </div>



        <!-- قائمة الطلبات -->
        <div class="space-y-6" id="orders-container">
            @forelse($orders as $order)
            <div class="order-card bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="p-6">
                    <div class="flex justify-between items-start mb-4">
                        <div>
                            <div class="flex items-center space-x-4 space-x-reverse mb-2">
                                <h3 class="text-lg font-bold text-gray-800 dark:text-white">{{ $order->order_number }}</h3>
                                @if($order->status == 'completed')
                                    <span class="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full text-sm">مكتمل</span>
                                @elseif($order->status == 'processing' || $order->status == 'pending')
                                    <span class="px-3 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 rounded-full text-sm">قيد التجهيز</span>
                                @elseif($order->status == 'cancelled')
                                    <span class="px-3 py-1 bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 rounded-full text-sm">ملغي</span>
                                @else
                                    <span class="px-3 py-1 bg-gray-100 dark:bg-gray-900/30 text-gray-600 dark:text-gray-400 rounded-full text-sm">{{ $order->status }}</span>
                                @endif
                            </div>
                            @if(is_object($order->created_at) && method_exists($order->created_at, 'format'))
                                <p class="text-gray-600 dark:text-gray-400 text-sm">تم الطلب في {{ $order->created_at->format('d M Y - H:i') }}</p>
                            @else
                                <p class="text-gray-600 dark:text-gray-400 text-sm">تم الطلب في {{ $order->created_at }}</p>
                            @endif
                        </div>
                        <div class="text-right">
                            <p class="text-2xl font-bold text-gray-800 dark:text-white">{{ number_format($order->total_amount, 2) }} د.ل</p>
                            @if(isset($order->items) && $order->items->count() > 0)
                                <p class="text-gray-600 dark:text-gray-400 text-sm">{{ $order->items->count() }} عناصر</p>
                            @elseif(isset($order->orderItems) && $order->orderItems->count() > 0)
                                <p class="text-gray-600 dark:text-gray-400 text-sm">{{ $order->orderItems->count() }} عناصر</p>
                            @endif
                        </div>
                    </div>

                    @if($order->status == 'processing' || $order->status == 'pending')
                    <!-- شريط التقدم -->
                    <div class="mb-4">
                        <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-2">
                            <span>تم استلام الطلب</span>
                            <span>قيد التجهيز</span>
                            <span>جاهز للتسليم</span>
                            <span>تم التسليم</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div class="bg-primary h-2 rounded-full" style="width: 50%"></div>
                        </div>
                    </div>
                    @endif

                    <!-- عناصر الطلب -->
                    @if((isset($order->items) && $order->items->count() > 0) || (isset($order->orderItems) && $order->orderItems->count() > 0))
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                        <div class="space-y-3">
                            @if(isset($order->items) && $order->items->count() > 0)
                                @foreach($order->items as $item)
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-md flex items-center justify-center ml-3">
                                            @if($item->menuItem && $item->menuItem->image_path)
                                                <img src="{{ asset('storage/' . $item->menuItem->image_path) }}"
                                                     alt="{{ $item->menuItem->name }}"
                                                     class="w-full h-full object-cover rounded-md">
                                            @else
                                                <i class="fas fa-utensils text-gray-400"></i>
                                            @endif
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-800 dark:text-white">{{ $item->menuItem->name ?? 'عنصر محذوف' }}</p>
                                            <p class="text-gray-600 dark:text-gray-400 text-sm">الكمية: {{ $item->quantity }} × {{ number_format($item->price, 2) }} د.ل</p>
                                        </div>
                                    </div>
                                    <span class="font-medium text-gray-800 dark:text-white">{{ number_format($item->price * $item->quantity, 2) }} د.ل</span>
                                </div>
                                @endforeach
                            @elseif(isset($order->orderItems) && $order->orderItems->count() > 0)
                                @foreach($order->orderItems as $item)
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <img src="{{ $item->menuItem->image_url ?? 'https://images.unsplash.com/photo-1513104890138-7c749659a591' }}?ixlib=rb-1.2.1&auto=format&fit=crop&w=60&q=80" alt="{{ $item->menuItem->name }}" class="w-12 h-12 object-cover rounded-md ml-3">
                                        <div>
                                            <p class="font-medium text-gray-800 dark:text-white">{{ $item->menuItem->name }}</p>
                                            <p class="text-gray-600 dark:text-gray-400 text-sm">الكمية: {{ $item->quantity }} × {{ number_format($item->price, 2) }} د.ل</p>
                                        </div>
                                    </div>
                                    <span class="font-medium text-gray-800 dark:text-white">{{ number_format($item->price * $item->quantity, 2) }} د.ل</span>
                                </div>
                                @endforeach
                            @endif
                        </div>
                    </div>
                    @endif

                    <!-- إجراءات الطلب -->
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
                        <div class="flex justify-between items-center">
                            <div class="flex space-x-3 space-x-reverse">
                                <a href="{{ route('customer.orders.show', $order->order_id) }}" class="text-primary hover:text-primary/80 text-sm">
                                    <i class="fas fa-eye ml-1"></i>عرض التفاصيل
                                </a>
                                <!-- أزرار متاحة لجميع الطلبات -->
                                <a href="{{ route('customer.orders.invoice', $order->order_id) }}" class="text-primary hover:text-primary/80 text-sm">
                                    <i class="fas fa-download ml-1"></i>عرض الفاتورة
                                </a>

                                @if($order->status == 'completed')
                                    <button onclick="reorderOrder('{{ $order->order_id }}')" class="text-primary hover:text-primary/80 text-sm">
                                        <i class="fas fa-redo ml-1"></i>إعادة الطلب
                                    </button>
                                @elseif(in_array($order->status, ['pending', 'preparing']))
                                    <a href="{{ route('customer.orders.show', $order->order_id) }}" class="text-primary hover:text-primary/80 text-sm">
                                        <i class="fas fa-eye ml-1"></i>تتبع الطلب
                                    </a>
                                    <a href="{{ route('customer.orders.cancel.page', $order->order_id) }}" class="text-red-600 hover:text-red-700 text-sm">
                                        <i class="fas fa-times ml-1"></i>إلغاء الطلب
                                    </a>
                                @elseif($order->status == 'canceled')
                                    <button onclick="reorderOrder('{{ $order->order_id }}')" class="text-primary hover:text-primary/80 text-sm">
                                        <i class="fas fa-redo ml-1"></i>إعادة الطلب
                                    </button>
                                @endif
                            </div>
                            @if($order->status == 'completed')
                            <div class="flex items-center text-yellow-400">
                                <span class="text-gray-600 dark:text-gray-400 text-sm ml-2">تقييم الطلب:</span>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            @elseif($order->status == 'processing' || $order->status == 'pending')
                            <div class="text-sm text-gray-600 dark:text-gray-400">
                                <i class="fas fa-clock ml-1"></i>
                                الوقت المتبقي: 25 دقيقة
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            @empty
            <!-- رسالة عدم وجود طلبات -->
            <div class="text-center py-12">
                <div class="w-24 h-24 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-shopping-bag text-gray-400 text-3xl"></i>
                </div>
                <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2">لا توجد طلبات</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-6">لم تقم بأي طلبات بعد</p>
                <a href="{{ route('customer.menu') }}" class="bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg transition">
                    <i class="fas fa-plus ml-2"></i>
                    اطلب الآن
                </a>
            </div>
            @endforelse
        </div>

        <!-- Pagination -->
        @if(method_exists($orders, 'links'))
        <div class="mt-8">
            {{ $orders->links() }}
        </div>
        @endif
    </div>
</div>
@endsection

@push('scripts')
<script>
// إلغاء الطلب
function cancelOrder(orderId, orderNumber) {
    // التوجه إلى صفحة إلغاء الطلب
    window.location.href = `/customer/orders/${orderId}/cancel`;
}

// إعادة الطلب
function reorderOrder(orderId) {
    // إرسال طلب إعادة الطلب مباشرة بدون تأكيد
    fetch(`/customer/orders/${orderId}/reorder`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم إضافة العناصر إلى السلة بنجاح', 'success');
            // التوجه إلى السلة
            setTimeout(() => {
                window.location.href = '/customer/cart';
            }, 1500);
        } else {
            showNotification('حدث خطأ أثناء إعادة الطلب', 'error');
        }
    })
    .catch(error => {
        showNotification('حدث خطأ في الاتصال', 'error');
    });
}

// تحميل الفاتورة
function downloadInvoice(orderId) {
    window.open(`/customer/orders/${orderId}/invoice`, '_blank');
}

// عرض الإشعارات
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transition-all duration-300 transform translate-x-full`;

    // تحديد لون الإشعار حسب النوع
    if (type === 'success') {
        notification.className += ' bg-green-500 text-white';
    } else if (type === 'error') {
        notification.className += ' bg-red-500 text-white';
    } else if (type === 'warning') {
        notification.className += ' bg-yellow-500 text-white';
    } else {
        notification.className += ' bg-blue-500 text-white';
    }

    notification.innerHTML = `
        <div class="flex items-center">
            <span class="flex-1">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="mr-2 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // إضافة الإشعار إلى الصفحة
    document.body.appendChild(notification);

    // عرض الإشعار
    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    // إخفاء الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 300);
    }, 5000);
}
</script>
@endpush
