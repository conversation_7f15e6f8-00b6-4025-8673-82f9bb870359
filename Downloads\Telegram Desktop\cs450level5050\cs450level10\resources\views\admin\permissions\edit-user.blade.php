@extends('layouts.admin')

@section('title', 'تعديل صلاحيات المستخدم')

@section('content')
<div class="mb-6">
    <div class="flex justify-between items-center">
        <div>
            <h2 class="text-2xl font-bold text-gray-800 dark:text-white">تعديل صلاحيات المستخدم</h2>
            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                تعديل صلاحيات: {{ $user->first_name }} {{ $user->last_name }}
            </p>
        </div>
        <a href="{{ route('admin.permissions.index') }}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-md flex items-center transition-all">
            <i class="fas fa-arrow-right ml-2"></i>
            <span>العودة</span>
        </a>
    </div>
</div>

<!-- معلومات المستخدم -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
    <div class="flex items-center">
        <div class="flex-shrink-0 h-16 w-16">
            <div class="h-16 w-16 rounded-full bg-primary text-white flex items-center justify-center text-xl font-bold">
                {{ substr($user->first_name, 0, 1) }}{{ substr($user->last_name, 0, 1) }}
            </div>
        </div>
        <div class="mr-4">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white">
                {{ $user->first_name }} {{ $user->last_name }}
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400">{{ $user->email }}</p>
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1
                @if($user->user_type === 'admin') bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200
                @elseif($user->user_type === 'employee') bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                @else bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 @endif">
                @if($user->user_type === 'admin') مدير
                @elseif($user->user_type === 'employee') موظف
                @else عميل @endif
            </span>
        </div>
    </div>
</div>

<form action="{{ route('admin.permissions.update-user', $user->user_id) }}" method="POST">
    @csrf
    @method('PUT')

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- الأدوار -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">الأدوار</h3>
            <div class="space-y-3">
                @foreach($roles as $role)
                <label class="flex items-center p-3 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors cursor-pointer">
                    <input type="checkbox" name="roles[]" value="{{ $role->name }}"
                           {{ $user->hasRole($role->name) ? 'checked' : '' }}
                           class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                    <div class="mr-3">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">{{ $role->name }}</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">
                            {{ $role->permissions->count() }} صلاحية
                        </div>
                    </div>
                </label>
                @endforeach
            </div>
        </div>

        <!-- الصلاحيات المباشرة -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">الصلاحيات المباشرة</h3>
            <div class="space-y-4">
                @foreach($permissions as $group => $groupPermissions)
                <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <h4 class="font-medium text-gray-800 dark:text-white mb-3 capitalize cursor-pointer hover:text-primary transition-colors"
                        onclick="toggleGroupPermissions('{{ $group }}_permissions')"
                        title="اضغط لتحديد/إلغاء تحديد جميع الصلاحيات">
                        @switch($group)
                            @case('users') 👥 إدارة المستخدمين @break
                            @case('menu') 🍽️ إدارة القائمة @break
                            @case('orders') 🛒 إدارة الطلبات @break
                            @case('reservations') 📅 إدارة الحجوزات @break
                            @case('inventory') 📦 إدارة المخزون @break
                            @case('ingredients') 🥘 إدارة المكونات @break
                            @case('expenses') 💰 إدارة المصروفات @break
                            @case('reports') 📊 التقارير @break
                            @case('tables') 🪑 إدارة الطاولات @break
                            @case('payments') 💳 إدارة المدفوعات @break
                            @case('notifications') 🔔 إدارة الإشعارات @break
                            @case('settings') ⚙️ الإعدادات @break
                            @case('dashboard') 🏠 لوحة التحكم @break
                            @default {{ $group }} @break
                        @endswitch
                        <i class="fas fa-chevron-down text-xs mr-2"></i>
                    </h4>
                    <div class="grid grid-cols-1 gap-2">
                        @foreach($groupPermissions as $permission)
                        <label class="flex items-center">
                            <input type="checkbox" name="permissions[]" value="{{ $permission->name }}"
                                   {{ $user->hasDirectPermission($permission->name) ? 'checked' : '' }}
                                   class="h-3 w-3 text-primary focus:ring-primary border-gray-300 rounded">
                            <span class="mr-2 text-sm text-gray-700 dark:text-gray-300">
                                @switch(explode('.', $permission->name)[1])
                                    @case('view') عرض @break
                                    @case('create') إضافة @break
                                    @case('edit') تعديل @break
                                    @case('delete') حذف @break
                                    @case('status') تغيير الحالة @break
                                    @case('export') تصدير @break
                                    @case('send') إرسال @break
                                    @case('permissions') إدارة الصلاحيات @break
                                    @case('admin') المدير @break
                                    @case('employee') الموظف @break
                                    @case('financial') مالية @break
                                    @case('sales') مبيعات @break
                                    @case('inventory') مخزون @break
                                    @default {{ explode('.', $permission->name)[1] }} @break
                                @endswitch
                            </span>
                        </label>
                        @endforeach
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>

    <!-- أزرار الحفظ -->
    <div class="mt-6 flex justify-end space-x-2 space-x-reverse">
        <a href="{{ route('admin.permissions.index') }}"
           class="px-6 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-all">
            إلغاء
        </a>
        <button type="submit"
                class="px-6 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-all">
            <i class="fas fa-save ml-2"></i>
            حفظ التغييرات
        </button>
    </div>
</form>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تحديد/إلغاء تحديد جميع الصلاحيات في مجموعة
        const groupHeaders = document.querySelectorAll('h4');
        groupHeaders.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', function() {
                const container = this.parentElement;
                const checkboxes = container.querySelectorAll('input[type="checkbox"]');
                const allChecked = Array.from(checkboxes).every(cb => cb.checked);

                checkboxes.forEach(cb => {
                    cb.checked = !allChecked;
                });
            });
        });

        // إضافة تلميح للمجموعات
        groupHeaders.forEach(header => {
            header.title = 'اضغط لتحديد/إلغاء تحديد جميع الصلاحيات في هذه المجموعة';
        });
    });
</script>
@endsection
