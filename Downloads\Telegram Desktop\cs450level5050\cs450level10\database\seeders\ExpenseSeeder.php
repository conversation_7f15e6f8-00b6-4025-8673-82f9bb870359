<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Expense;
use Illuminate\Support\Facades\DB;

class ExpenseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // التأكد من عدم وجود بيانات سابقة
        DB::table('expenses')->truncate();

        // إضافة بيانات المصروفات
        $expenses = [
            [
                'description' => 'شراء لحوم طازجة',
                'category' => 'ingredients',
                'amount' => 3250,
                'payment_method' => 'bank_transfer',
                'expense_date' => '2024-03-15',
                'recorded_by' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'description' => 'فاتورة الكهرباء',
                'category' => 'utilities',
                'amount' => 1850,
                'payment_method' => 'bank_transfer',
                'expense_date' => '2024-03-10',
                'recorded_by' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'description' => 'رواتب الموظفين',
                'category' => 'salaries',
                'amount' => 10320,
                'payment_method' => 'bank_transfer',
                'expense_date' => '2024-03-05',
                'recorded_by' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'description' => 'صيانة الفرن',
                'category' => 'maintenance',
                'amount' => 850,
                'payment_method' => 'cash',
                'expense_date' => '2024-03-02',
                'recorded_by' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'description' => 'شراء خضروات طازجة',
                'category' => 'ingredients',
                'amount' => 1250,
                'payment_method' => 'cash',
                'expense_date' => '2024-03-01',
                'recorded_by' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        // إدخال البيانات في قاعدة البيانات
        foreach ($expenses as $expense) {
            Expense::create($expense);
        }
    }
}
