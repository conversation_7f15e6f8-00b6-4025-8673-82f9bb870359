# دليل منح الموظفين صلاحية الوصول للوحة الإدارة

## نظرة عامة
يمكن للموظفين الوصول لصفحات الإدارة إذا كانت لديهم الصلاحيات المناسبة. هذا يسمح بمرونة في إدارة الفريق.

## كيفية منح موظف صلاحية الوصول للإدارة

### الطريقة الأولى: من خلال واجهة الويب
1. سجل دخول كمدير
2. انتقل إلى `/admin/permissions`
3. اختر الموظف المطلوب
4. اضغط "تعديل الصلاحيات"
5. فعّل صلاحية `dashboard.admin`
6. أضف الصلاحيات الإضافية المطلوبة
7. احفظ التغييرات

### الطريقة الثانية: من خلال سطر الأوامر

#### منح صلاحية أساسية:
```bash
php artisan user:grant-admin-access <EMAIL>
```

#### منح صلاحيات محددة:
```bash
php artisan user:grant-admin-access <EMAIL> --permissions=users.view --permissions=menu.edit --permissions=reports.sales
```

#### إزالة صلاحية الوصول:
```bash
php artisan user:revoke-admin-access <EMAIL>
```

### الطريقة الثالثة: من خلال Tinker
```bash
php artisan tinker
```

```php
// البحث عن الموظف
$employee = App\Models\User::where('email', '<EMAIL>')->first();

// منح صلاحية الوصول للوحة الإدارة
$employee->givePermissionTo('dashboard.admin');

// منح صلاحيات إضافية
$employee->givePermissionTo([
    'orders.view', 'orders.create', 'orders.edit',
    'reservations.view', 'reservations.create',
    'inventory.view',
    'reports.view', 'reports.sales'
]);

// التحقق من الصلاحيات
$employee->can('dashboard.admin'); // true
$employee->getAllPermissions()->pluck('name'); // عرض جميع الصلاحيات
```

## أنواع الصلاحيات الإدارية

### صلاحيات أساسية للموظف المشرف:
- `dashboard.admin` - الوصول للوحة الإدارة
- `orders.view`, `orders.create`, `orders.edit`, `orders.status`
- `reservations.view`, `reservations.create`, `reservations.edit`
- `inventory.view`
- `reports.view`, `reports.sales`

### صلاحيات متقدمة للمدير المساعد:
- جميع الصلاحيات الأساسية +
- `users.view`, `users.create`, `users.edit`
- `menu.view`, `menu.create`, `menu.edit`
- `expenses.view`, `expenses.create`
- `reports.financial`, `reports.inventory`

### صلاحيات كاملة (للمدير الثاني):
- جميع الصلاحيات ما عدا `users.permissions`

## سيناريوهات عملية

### سيناريو 1: مشرف الوردية
```php
$supervisor = User::where('email', '<EMAIL>')->first();
$supervisor->givePermissionTo([
    'dashboard.admin',
    'orders.view', 'orders.create', 'orders.edit', 'orders.status',
    'reservations.view', 'reservations.create', 'reservations.edit', 'reservations.status',
    'tables.view', 'tables.status',
    'payments.view', 'payments.create'
]);
```

### سيناريو 2: مدير المخزون
```php
$inventoryManager = User::where('email', '<EMAIL>')->first();
$inventoryManager->givePermissionTo([
    'dashboard.admin',
    'inventory.view', 'inventory.create', 'inventory.edit', 'inventory.export',
    'ingredients.view', 'ingredients.create', 'ingredients.edit',
    'menu.view', 'menu.edit',
    'reports.view', 'reports.inventory'
]);
```

### سيناريو 3: مدير المبيعات
```php
$salesManager = User::where('email', '<EMAIL>')->first();
$salesManager->givePermissionTo([
    'dashboard.admin',
    'orders.view', 'orders.create', 'orders.edit', 'orders.status',
    'menu.view', 'menu.create', 'menu.edit',
    'reports.view', 'reports.sales', 'reports.financial',
    'expenses.view'
]);
```

## كيف يظهر الرابط للموظف

عندما يحصل الموظف على صلاحية `dashboard.admin`:

1. **في لوحة الموظف**: سيظهر رابط "لوحة الإدارة" مميز باللون البرتقالي
2. **الوصول المباشر**: يمكنه الذهاب مباشرة إلى `/admin`
3. **القائمة الجانبية**: سيرى فقط الأقسام التي لديه صلاحية للوصول إليها

## الأمان والحماية

### الحماية على مستوى الصفحات:
- كل صفحة محمية بـ middleware خاص
- التحقق من الصلاحيات قبل عرض المحتوى
- إخفاء الأزرار والروابط غير المصرح بها

### الحماية على مستوى البيانات:
```php
// في الكونترولر
public function index()
{
    // المديرون يرون كل شيء
    if (auth()->user()->user_type === 'admin') {
        $orders = Order::all();
    } 
    // الموظفون يرون طلباتهم فقط
    else {
        $orders = Order::where('created_by', auth()->id())->get();
    }
    
    return view('admin.orders.index', compact('orders'));
}
```

## استكشاف الأخطاء

### المشكلة: الموظف لا يرى رابط لوحة الإدارة
**الحل:**
```php
// التحقق من الصلاحية
$user = User::find($employeeId);
$user->can('dashboard.admin'); // يجب أن يكون true

// إعطاء الصلاحية إذا لم تكن موجودة
$user->givePermissionTo('dashboard.admin');
```

### المشكلة: الموظف يصل للوحة الإدارة لكن لا يرى أي محتوى
**الحل:**
```php
// إعطاء صلاحيات إضافية
$user->givePermissionTo(['orders.view', 'reservations.view']);
```

### المشكلة: خطأ 403 عند محاولة الوصول
**الحل:**
```bash
# مسح cache الصلاحيات
php artisan permission:cache-reset

# التحقق من الـ middleware
# تأكد من أن المسار يستخدم 'admin.access' بدلاً من 'admin' فقط
```

## أوامر مفيدة

```bash
# عرض جميع الموظفين الذين لديهم صلاحية الوصول للإدارة
php artisan tinker
>>> User::permission('dashboard.admin')->get(['first_name', 'last_name', 'email']);

# عرض صلاحيات موظف معين
>>> User::where('email', '<EMAIL>')->first()->getAllPermissions()->pluck('name');

# إزالة جميع الصلاحيات الإدارية من موظف
>>> $user = User::where('email', '<EMAIL>')->first();
>>> $user->syncPermissions(['dashboard.employee']);
```

## نصائح للاستخدام الأمثل

1. **ابدأ بالصلاحيات الأساسية** ثم أضف حسب الحاجة
2. **استخدم الأدوار** للمجموعات الكبيرة من الصلاحيات
3. **راجع الصلاحيات دورياً** وأزل غير المستخدمة
4. **وثّق الصلاحيات** لكل موظف لسهولة المتابعة
5. **اختبر الصلاحيات** قبل منحها للموظفين

## مثال كامل: إنشاء مدير مساعد

```php
// إنشاء الموظف
$assistantManager = User::create([
    'first_name' => 'أحمد',
    'last_name' => 'محمد',
    'email' => '<EMAIL>',
    'password' => bcrypt('password123'),
    'phone' => '+218912345678',
    'user_type' => 'employee',
    'is_active' => true
]);

// منح صلاحيات المدير المساعد
$assistantManager->givePermissionTo([
    'dashboard.admin',
    'users.view', 'users.create', 'users.edit',
    'menu.view', 'menu.create', 'menu.edit',
    'orders.view', 'orders.create', 'orders.edit', 'orders.status',
    'reservations.view', 'reservations.create', 'reservations.edit', 'reservations.status',
    'inventory.view', 'inventory.create', 'inventory.edit',
    'expenses.view', 'expenses.create', 'expenses.edit',
    'reports.view', 'reports.sales', 'reports.inventory',
    'tables.view', 'tables.create', 'tables.edit', 'tables.status',
    'payments.view', 'payments.create',
    'notifications.view', 'notifications.create'
]);

echo "تم إنشاء المدير المساعد بنجاح!";
echo "يمكنه الآن الوصول للوحة الإدارة بصلاحيات محدودة.";
```
