@extends('employee.layouts.app')

@section('title', 'تفاصيل العميل - ' . $customer->first_name . ' ' . $customer->last_name)

@section('content')
<div class="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 py-8">
    <!-- عناصر زخرفية متحركة -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-indigo-600/20 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-purple-400/20 to-pink-600/20 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <!-- شريط التنقل -->
        <div class="flex items-center mb-8">
            <a href="{{ route('employee.customers') }}" class="flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors duration-200">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة لقائمة العملاء
            </a>
        </div>

        <!-- معلومات العميل الأساسية -->
        <div class="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-3xl p-8 shadow-2xl border border-white/20 dark:border-gray-700/20 mb-8">
            <div class="flex flex-col md:flex-row items-center md:items-start gap-8">
                <!-- صورة العميل -->
                <div class="flex-shrink-0">
                    <div class="w-32 h-32 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-2xl overflow-hidden border-4 border-white dark:border-gray-700">
                        @if($customer->profile_image)
                        <img src="{{ asset('storage/' . $customer->profile_image) }}" alt="{{ $customer->first_name }}" class="w-full h-full object-cover">
                        @else
                        <i class="fas fa-user text-4xl text-white"></i>
                        @endif
                    </div>
                </div>

                <!-- معلومات العميل -->
                <div class="flex-1 text-center md:text-right">
                    <h1 class="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-2">
                        {{ $customer->first_name }} {{ $customer->last_name }}
                    </h1>
                    <p class="text-lg text-gray-600 dark:text-gray-400 mb-4">عميل مسجل</p>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-2xl">
                            <div class="flex items-center justify-center md:justify-start">
                                <i class="fas fa-envelope text-blue-500 ml-2"></i>
                                <span class="text-gray-800 dark:text-white">{{ $customer->email }}</span>
                            </div>
                        </div>
                        @if($customer->phone)
                        <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-2xl">
                            <div class="flex items-center justify-center md:justify-start">
                                <i class="fas fa-phone text-green-500 ml-2"></i>
                                <span class="text-gray-800 dark:text-white">{{ $customer->phone }}</span>
                            </div>
                        </div>
                        @endif
                    </div>

                    <div class="flex flex-col md:flex-row items-center gap-4">
                        @if($customer->is_active)
                        <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                            <div class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                            عميل نشط
                        </span>
                        @else
                        <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                            <div class="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                            عميل غير نشط
                        </span>
                        @endif
                        
                        <span class="text-sm text-gray-600 dark:text-gray-400">
                            عضو منذ {{ $customer->created_at->diffForHumans() }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات العميل -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl p-6 shadow-xl border border-white/20 dark:border-gray-700/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-shopping-cart text-white text-xl"></i>
                    </div>
                    <span class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ $customer->orders->count() }}</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">إجمالي الطلبات</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">طلب مكتمل</p>
            </div>

            <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl p-6 shadow-xl border border-white/20 dark:border-gray-700/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-calendar-check text-white text-xl"></i>
                    </div>
                    <span class="text-3xl font-bold text-purple-600 dark:text-purple-400">{{ $customer->reservations->count() }}</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">إجمالي الحجوزات</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">حجز مؤكد</p>
            </div>

            <div class="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-3xl p-6 shadow-xl border border-white/20 dark:border-gray-700/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-dollar-sign text-white text-xl"></i>
                    </div>
                    <span class="text-3xl font-bold text-green-600 dark:text-green-400">{{ number_format($customer->orders->where('status', 'completed')->sum('total_amount'), 2) }}</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">إجمالي الإنفاق</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">ريال سعودي</p>
            </div>
        </div>

        <!-- تبويبات المحتوى -->
        <div class="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/20 dark:border-gray-700/20 overflow-hidden">
            <!-- أزرار التبويبات -->
            <div class="flex border-b border-gray-200 dark:border-gray-700">
                <button class="tab-button active flex-1 px-6 py-4 text-center font-medium transition-colors duration-200" data-tab="orders">
                    <i class="fas fa-shopping-cart ml-2"></i>
                    الطلبات ({{ $customer->orders->count() }})
                </button>
                <button class="tab-button flex-1 px-6 py-4 text-center font-medium transition-colors duration-200" data-tab="reservations">
                    <i class="fas fa-calendar-check ml-2"></i>
                    الحجوزات ({{ $customer->reservations->count() }})
                </button>
            </div>

            <!-- محتوى التبويبات -->
            <div class="p-6">
                <!-- تبويب الطلبات -->
                <div id="orders-tab" class="tab-content">
                    @if($customer->orders->count() > 0)
                    <div class="space-y-4">
                        @foreach($customer->orders->take(10) as $order)
                        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 rounded-2xl border border-blue-200/50 dark:border-blue-700/50">
                            <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <span class="text-lg font-bold text-gray-800 dark:text-white">طلب #{{ $order->id }}</span>
                                        <span class="mr-4 px-3 py-1 rounded-full text-xs font-medium
                                            @if($order->status == 'completed') bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300
                                            @elseif($order->status == 'pending') bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300
                                            @elseif($order->status == 'cancelled') bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300
                                            @else bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 @endif">
                                            {{ $order->status == 'completed' ? 'مكتمل' : ($order->status == 'pending' ? 'قيد الانتظار' : ($order->status == 'cancelled' ? 'ملغي' : $order->status)) }}
                                        </span>
                                    </div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                        <i class="fas fa-calendar ml-1"></i>
                                        {{ $order->created_at->format('Y-m-d H:i') }}
                                    </div>
                                    @if($order->table)
                                    <div class="text-sm text-gray-600 dark:text-gray-400">
                                        <i class="fas fa-chair ml-1"></i>
                                        طاولة رقم {{ $order->table->table_number }}
                                    </div>
                                    @endif
                                </div>
                                <div class="text-right">
                                    <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                                        {{ number_format($order->total_amount, 2) }} ر.س
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    @else
                    <div class="text-center py-12">
                        <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-shopping-cart text-2xl text-gray-400"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2">لا توجد طلبات</h3>
                        <p class="text-gray-500 dark:text-gray-500">لم يقم هذا العميل بأي طلبات بعد</p>
                    </div>
                    @endif
                </div>

                <!-- تبويب الحجوزات -->
                <div id="reservations-tab" class="tab-content hidden">
                    @if($customer->reservations->count() > 0)
                    <div class="space-y-4">
                        @foreach($customer->reservations->take(10) as $reservation)
                        <div class="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-6 rounded-2xl border border-purple-200/50 dark:border-purple-700/50">
                            <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                                <div class="flex-1">
                                    <div class="flex items-center mb-2">
                                        <span class="text-lg font-bold text-gray-800 dark:text-white">حجز #{{ $reservation->id }}</span>
                                        <span class="mr-4 px-3 py-1 rounded-full text-xs font-medium
                                            @if($reservation->status == 'confirmed') bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300
                                            @elseif($reservation->status == 'pending') bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300
                                            @elseif($reservation->status == 'cancelled') bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300
                                            @else bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 @endif">
                                            {{ $reservation->status == 'confirmed' ? 'مؤكد' : ($reservation->status == 'pending' ? 'قيد الانتظار' : ($reservation->status == 'cancelled' ? 'ملغي' : $reservation->status)) }}
                                        </span>
                                    </div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                        <i class="fas fa-clock ml-1"></i>
                                        {{ $reservation->reservation_time->format('Y-m-d H:i') }}
                                    </div>
                                    @if($reservation->table)
                                    <div class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                                        <i class="fas fa-chair ml-1"></i>
                                        طاولة رقم {{ $reservation->table->table_number }}
                                    </div>
                                    @endif
                                    <div class="text-sm text-gray-600 dark:text-gray-400">
                                        <i class="fas fa-users ml-1"></i>
                                        {{ $reservation->party_size }} أشخاص
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    @else
                    <div class="text-center py-12">
                        <div class="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-calendar-check text-2xl text-gray-400"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-600 dark:text-gray-400 mb-2">لا توجد حجوزات</h3>
                        <p class="text-gray-500 dark:text-gray-500">لم يقم هذا العميل بأي حجوزات بعد</p>
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
@keyframes blob {
    0% { transform: translate(0px, 0px) scale(1); }
    33% { transform: translate(30px, -50px) scale(1.1); }
    66% { transform: translate(-20px, 20px) scale(0.9); }
    100% { transform: translate(0px, 0px) scale(1); }
}

.animate-blob {
    animation: blob 7s infinite;
}

.animation-delay-2000 {
    animation-delay: 2s;
}

.tab-button.active {
    background: linear-gradient(to right, #3b82f6, #6366f1);
    color: white;
}

.tab-button:not(.active) {
    color: #6b7280;
}

.tab-button:not(.active):hover {
    background-color: #f3f4f6;
    color: #374151;
}

.dark .tab-button:not(.active) {
    color: #9ca3af;
}

.dark .tab-button:not(.active):hover {
    background-color: #374151;
    color: #d1d5db;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // التبديل بين التبويبات
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tabName = this.getAttribute('data-tab');
            
            // إزالة الفئة النشطة من جميع الأزرار
            tabButtons.forEach(btn => btn.classList.remove('active'));
            
            // إضافة الفئة النشطة للزر المحدد
            this.classList.add('active');
            
            // إخفاء جميع المحتويات
            tabContents.forEach(content => content.classList.add('hidden'));
            
            // إظهار المحتوى المحدد
            document.getElementById(tabName + '-tab').classList.remove('hidden');
        });
    });
});
</script>
@endsection
