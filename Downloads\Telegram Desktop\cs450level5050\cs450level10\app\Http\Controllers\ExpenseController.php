<?php

namespace App\Http\Controllers;

use App\Models\Expense;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
class ExpenseController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('admin');
    }

    public function index()
    {
        $expenses = Expense::with('recorder')
            ->orderBy('expense_date', 'desc')
            ->paginate(15);

        // حساب إجمالي المصروفات للشهر الحالي
        $currentMonthTotal = Expense::thisMonth()->sum('amount');

        // مصروفات المخزون التلقائية
        $inventoryExpenses = Expense::thisMonth()
            ->automatic()
            ->sum('amount');

        // المصروفات اليدوية
        $manualExpenses = Expense::thisMonth()
            ->manual()
            ->sum('amount');

        // متوسط المصروفات اليومية
        $daysInMonth = now()->daysInMonth;
        $dailyAverage = $currentMonthTotal / $daysInMonth;

        // حساب إجمالي المصروفات حسب الفئة للشهر الحالي
        $categoryTotals = Expense::thisMonth()
            ->groupBy('category')
            ->select('category', DB::raw('SUM(amount) as total'))
            ->get();

        return view('admin.expenses.index', compact(
            'expenses',
            'currentMonthTotal',
            'inventoryExpenses',
            'manualExpenses',
            'dailyAverage',
            'categoryTotals'
        ));
    }

    public function create()
    {
        $categories = ['ingredients', 'utilities', 'salaries', 'maintenance', 'other'];
        $paymentMethods = ['cash', 'bank_transfer', 'cheque'];

        return view('admin.expenses.create', compact('categories', 'paymentMethods'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.01',
            'category' => 'required|in:ingredients,utilities,salaries,maintenance,other',
            'description' => 'required|string',
            'expense_date' => 'required|date|before_or_equal:today',
            'payment_method' => 'required|in:cash,bank_transfer,cheque',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        Expense::create([
            'amount' => $request->amount,
            'category' => $request->category,
            'description' => $request->description,
            'expense_date' => $request->expense_date,
            'recorded_by' => Auth::id(),
            'payment_method' => $request->payment_method,
        ]);

        return redirect()->route('admin.expenses')->with('success', 'تم تسجيل المصروف بنجاح');
    }

    public function edit($id)
    {
        $expense = Expense::findOrFail($id);
        $categories = ['ingredients', 'utilities', 'salaries', 'maintenance', 'other'];
        $paymentMethods = ['cash', 'bank_transfer', 'cheque'];

        return view('admin.expenses.edit', compact('expense', 'categories', 'paymentMethods'));
    }

    public function update(Request $request, $id)
    {
        $expense = Expense::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'amount' => 'required|numeric|min:0.01',
            'category' => 'required|in:ingredients,utilities,salaries,maintenance,other',
            'description' => 'required|string',
            'expense_date' => 'required|date|before_or_equal:today',
            'payment_method' => 'required|in:cash,bank_transfer,cheque',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $expense->update([
            'amount' => $request->amount,
            'category' => $request->category,
            'description' => $request->description,
            'expense_date' => $request->expense_date,
            'payment_method' => $request->payment_method,
        ]);

        return redirect()->route('admin.expenses')->with('success', 'تم تحديث المصروف بنجاح');
    }

    public function delete($id)
    {
        $expense = Expense::findOrFail($id);
        $expense->delete();

        return redirect()->route('admin.expenses')->with('success', 'تم حذف المصروف بنجاح');
    }

    // تقرير المصروفات الشهرية
    public function monthlyReport(Request $request)
    {
        $year = $request->year ? $request->year : now()->year;
        $month = $request->month ? $request->month : now()->month;

        $expenses = Expense::whereYear('expense_date', $year)
            ->whereMonth('expense_date', $month)
            ->orderBy('expense_date')
            ->get();

        // إجمالي المصروفات للشهر
        $totalAmount = $expenses->sum('amount');

        // المصروفات حسب الفئة
        $categoryTotals = Expense::whereYear('expense_date', $year)
            ->whereMonth('expense_date', $month)
            ->groupBy('category')
            ->select('category', DB::raw('SUM(amount) as total'))
            ->get();

        // المصروفات حسب طريقة الدفع
        $paymentMethodTotals = Expense::whereYear('expense_date', $year)
            ->whereMonth('expense_date', $month)
            ->groupBy('payment_method')
            ->select('payment_method', DB::raw('SUM(amount) as total'))
            ->get();

        return view('admin.expenses.monthly_report', compact(
            'expenses',
            'year',
            'month',
            'totalAmount',
            'categoryTotals',
            'paymentMethodTotals'
        ));
    }

    // مقارنة المصروفات بين فترتين
    public function compareReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'start_date1' => 'required|date',
            'end_date1' => 'required|date|after_or_equal:start_date1',
            'start_date2' => 'required|date',
            'end_date2' => 'required|date|after_or_equal:start_date2',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // المصروفات للفترة الأولى
        $period1Expenses = Expense::whereBetween('expense_date', [$request->start_date1, $request->end_date1])
            ->sum('amount');

        $period1CategoryTotals = Expense::whereBetween('expense_date', [$request->start_date1, $request->end_date1])
            ->groupBy('category')
            ->select('category', DB::raw('SUM(amount) as total'))
            ->get();

        // المصروفات للفترة الثانية
        $period2Expenses = Expense::whereBetween('expense_date', [$request->start_date2, $request->end_date2])
            ->sum('amount');

        $period2CategoryTotals = Expense::whereBetween('expense_date', [$request->start_date2, $request->end_date2])
            ->groupBy('category')
            ->select('category', DB::raw('SUM(amount) as total'))
            ->get();

        // حساب الفرق بين الفترتين
        $difference = $period2Expenses - $period1Expenses;
        $percentChange = $period1Expenses > 0 ? ($difference / $period1Expenses) * 100 : 0;

        return view('admin.expenses.compare_report', compact(
            'period1Expenses',
            'period1CategoryTotals',
            'period2Expenses',
            'period2CategoryTotals',
            'difference',
            'percentChange',
            'request'
        ));
    }

    // عرض تفاصيل المصروف
    public function show($id)
    {
        $expense = Expense::with('recorder')->findOrFail($id);
        return view('admin.expenses.show', compact('expense'));
    }

    // تصدير المصروفات
    public function export(Request $request)
    {
        $query = Expense::with('recorder')->orderBy('expense_date', 'desc');

        // تطبيق الفلاتر
        if ($request->has('search') && !empty($request->search)) {
            $query->where('description', 'like', '%' . $request->search . '%');
        }

        if ($request->has('category') && !empty($request->category)) {
            $query->where('category', $request->category);
        }

        if ($request->has('date') && !empty($request->date)) {
            $query->whereDate('expense_date', $request->date);
        }

        $expenses = $query->get();

        // إنشاء ملف CSV
        $filename = 'expenses_' . now()->format('Y-m-d_H-i-s') . '.csv';
        $headers = [
            'Content-Type' => 'text/csv; charset=UTF-8',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($expenses) {
            $file = fopen('php://output', 'w');

            // إضافة BOM للدعم العربي
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));

            // رأس الجدول
            fputcsv($file, [
                'الوصف',
                'الفئة',
                'المبلغ',
                'طريقة الدفع',
                'تاريخ المصروف',
                'المسجل',
                'تاريخ الإنشاء'
            ]);

            // البيانات
            foreach ($expenses as $expense) {
                fputcsv($file, [
                    $expense->description,
                    $expense->category,
                    $expense->amount,
                    $expense->payment_method,
                    $expense->expense_date->format('Y-m-d'),
                    $expense->recorder ? $expense->recorder->first_name . ' ' . $expense->recorder->last_name : 'غير معروف',
                    $expense->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    // إحصائيات المصروفات (API)
    public function stats()
    {
        // إجمالي المصروفات للشهر الحالي
        $currentMonthTotal = Expense::thisMonth()->sum('amount');

        // مصروفات المخزون التلقائية
        $inventoryExpenses = Expense::thisMonth()
            ->automatic()
            ->sum('amount');

        // المصروفات اليدوية
        $manualExpenses = Expense::thisMonth()
            ->manual()
            ->sum('amount');

        // متوسط المصروفات اليومية
        $daysInMonth = now()->daysInMonth;
        $dailyAverage = $currentMonthTotal / $daysInMonth;

        return response()->json([
            'currentMonthTotal' => number_format($currentMonthTotal, 2),
            'inventoryExpenses' => number_format($inventoryExpenses, 2),
            'manualExpenses' => number_format($manualExpenses, 2),
            'dailyAverage' => number_format($dailyAverage, 2)
        ]);
    }

    // إضافة مصروف تلقائي من المخزون
    public static function addInventoryExpense($description, $amount, $orderId = null)
    {
        return Expense::create([
            'amount' => $amount,
            'category' => 'inventory_auto',
            'description' => $description,
            'expense_date' => now(),
            'recorded_by' => null, // النظام
            'payment_method' => 'inventory',
            'is_automatic' => true,
            'order_id' => $orderId
        ]);
    }
}