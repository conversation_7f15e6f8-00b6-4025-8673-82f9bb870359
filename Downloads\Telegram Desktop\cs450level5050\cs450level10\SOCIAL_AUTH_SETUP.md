# إعداد تسجيل الدخول بالحسابات الاجتماعية - Eat Hub

## نظرة عامة
تم إضافة إمكانية تسجيل الدخول والتسجيل باستخدام:
- **Google** 🔍
- **Facebook** 📘  
- **Apple** 🍎

## الملفات المضافة/المحدثة

### 1. الكونترولر
- `app/Http/Controllers/Auth/SocialAuthController.php` - معالجة تسجيل الدخول الاجتماعي

### 2. قاعدة البيانات
- `database/migrations/2025_05_24_171504_add_social_login_fields_to_users_table.php`
- أعمدة جديدة: `google_id`, `facebook_id`, `apple_id`, `avatar`, `email_verified_at`

### 3. الإعدادات
- `.env` - إعدادات OAuth
- `config/services.php` - إعد<PERSON><PERSON><PERSON> مقدمي الخدمة

### 4. المسارات
- `routes/web.php` - مسارات جديدة للحسابات الاجتماعية

### 5. الواجهات
- `resources/views/auth/login.blade.php` - أزرار تسجيل الدخول
- `resources/views/auth/register.blade.php` - أزرار التسجيل

## المسارات المتاحة

### Google
- **التوجيه:** `/auth/google`
- **الاستجابة:** `/auth/google/callback`

### Facebook  
- **التوجيه:** `/auth/facebook`
- **الاستجابة:** `/auth/facebook/callback`

### Apple
- **التوجيه:** `/auth/apple`
- **الاستجابة:** `/auth/apple/callback`

## خطوات الإعداد

### 1. إعداد Google OAuth

#### أ. إنشاء مشروع في Google Cloud Console
1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com/)
2. أنشئ مشروع جديد أو اختر مشروع موجود
3. فعّل Google+ API

#### ب. إنشاء OAuth 2.0 Client
1. اذهب إلى **APIs & Services > Credentials**
2. انقر **Create Credentials > OAuth 2.0 Client ID**
3. اختر **Web Application**
4. أضف Authorized redirect URIs:
   ```
   http://127.0.0.1:8000/auth/google/callback
   http://localhost:8000/auth/google/callback
   ```

#### ج. تحديث .env
```env
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
```

### 2. إعداد Facebook OAuth

#### أ. إنشاء تطبيق Facebook
1. اذهب إلى [Facebook Developers](https://developers.facebook.com/)
2. أنشئ تطبيق جديد
3. أضف **Facebook Login** product

#### ب. إعداد Facebook Login
1. اذهب إلى **Facebook Login > Settings**
2. أضف Valid OAuth Redirect URIs:
   ```
   http://127.0.0.1:8000/auth/facebook/callback
   http://localhost:8000/auth/facebook/callback
   ```

#### ج. تحديث .env
```env
FACEBOOK_CLIENT_ID=your_facebook_app_id_here
FACEBOOK_CLIENT_SECRET=your_facebook_app_secret_here
```

### 3. إعداد Apple OAuth

#### أ. إعداد Apple Developer Account
1. اذهب إلى [Apple Developer](https://developer.apple.com/)
2. أنشئ **App ID** جديد
3. فعّل **Sign In with Apple**

#### ب. إنشاء Service ID
1. أنشئ **Services ID** جديد
2. فعّل **Sign In with Apple**
3. أضف Domain and Subdomain:
   ```
   Domain: 127.0.0.1
   Return URL: http://127.0.0.1:8000/auth/apple/callback
   ```

#### ج. إنشاء Private Key
1. أنشئ **Key** جديد
2. فعّل **Sign In with Apple**
3. حمّل الملف `.p8`

#### د. تحديث .env
```env
APPLE_CLIENT_ID=your_apple_service_id_here
APPLE_CLIENT_SECRET=your_apple_client_secret_here
```

## الاختبار

### 1. تشغيل الخادم
```bash
php artisan serve
```

### 2. اختبار الصفحات
- **تسجيل الدخول:** `http://127.0.0.1:8000/login`
- **التسجيل:** `http://127.0.0.1:8000/register`

### 3. اختبار الروابط
- **Google:** `http://127.0.0.1:8000/auth/google`
- **Facebook:** `http://127.0.0.1:8000/auth/facebook`  
- **Apple:** `http://127.0.0.1:8000/auth/apple`

## المميزات

### ✅ تم تنفيذه
- تثبيت Laravel Socialite
- إنشاء كونترولر للحسابات الاجتماعية
- إضافة أعمدة قاعدة البيانات
- إضافة المسارات
- تصميم أزرار جميلة في صفحات التسجيل
- معالجة الأخطاء
- إعادة التوجيه حسب دور المستخدم

### 🔧 يحتاج إعداد
- الحصول على مفاتيح OAuth من Google/Facebook/Apple
- تحديث ملف .env بالمفاتيح الحقيقية
- اختبار التكامل مع كل مقدم خدمة

## ملاحظات مهمة

1. **الأمان:** لا تشارك مفاتيح OAuth أبداً
2. **البيئة:** استخدم مفاتيح مختلفة للتطوير والإنتاج
3. **المجالات:** تأكد من إضافة جميع المجالات المطلوبة
4. **HTTPS:** Apple يتطلب HTTPS في الإنتاج

## استكشاف الأخطاء

### خطأ "Invalid redirect URI"
- تأكد من إضافة الرابط الصحيح في إعدادات OAuth
- تحقق من عدم وجود مسافات إضافية

### خطأ "Invalid client"
- تأكد من صحة Client ID و Client Secret
- تحقق من تفعيل الخدمة في لوحة التحكم

### خطأ "Scope not found"
- تأكد من طلب الصلاحيات المناسبة
- تحقق من إعدادات التطبيق

## الدعم
للمساعدة أو الاستفسارات، يرجى مراجعة:
- [Laravel Socialite Documentation](https://laravel.com/docs/socialite)
- [Google OAuth Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Facebook Login Documentation](https://developers.facebook.com/docs/facebook-login/)
- [Apple Sign In Documentation](https://developer.apple.com/sign-in-with-apple/)
