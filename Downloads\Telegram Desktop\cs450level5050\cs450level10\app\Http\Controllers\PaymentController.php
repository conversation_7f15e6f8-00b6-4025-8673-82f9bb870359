<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Payment;
use App\Models\Invoice;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Routing\Controller; 

class PaymentController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }
    
    // Admin methods
    public function adminInvoices()
    {
        
        $invoices = Invoice::with(['order.user'])
            ->orderBy('issued_at', 'desc')
            ->paginate(15);
            
        return view('admin.invoices.index', compact('invoices'));
    }
    
    public function viewInvoice($id)
    {
        
        $invoice = Invoice::with(['order.user', 'order.items.menuItem'])->findOrFail($id);
        $payments = Payment::where('order_id', $invoice->order_id)->get();
        
        return view('admin.invoices.view', compact('invoice', 'payments'));
    }
    
    // Employee methods
    public function employeeIndex()
    {
        $payments = Payment::with(['order.user'])
            ->orderBy('transaction_date', 'desc')
            ->paginate(15);

        // حساب الإحصائيات
        $totalPayments = Payment::sum('amount');
        $cashPayments = Payment::where('payment_method', 'cash')->sum('amount');
        $cardPayments = Payment::where('payment_method', 'card')->sum('amount');
        $todayPayments = Payment::whereDate('transaction_date', today())->sum('amount');

        return view('employee.payments.index', compact('payments', 'totalPayments', 'cashPayments', 'cardPayments', 'todayPayments'));
    }
    
    public function create($order_id)
    {
        
        $order = Order::with(['user', 'items.menuItem'])->findOrFail($order_id);
        $payments = Payment::where('order_id', $order_id)->get();
        $totalPaid = $payments->sum('amount');
        $remainingAmount = $order->total_amount - $totalPaid;
        
        // Check if an invoice exists for this order
        $invoice = Invoice::where('order_id', $order_id)->first();
        
        if (!$invoice) {
            // Create invoice if it doesn't exist
            $tax = $order->total_amount * 0.15; // 15% tax for example
            
            $invoice = Invoice::create([
                'order_id' => $order_id,
                'total_amount' => $order->total_amount + $tax,
                'tax_amount' => $tax,
                'discount_amount' => 0,
                'payment_status' => 'unpaid',
                'due_date' => now()->addDays(7),
            ]);
        }
        
        return view('employee.payments.create', compact('order', 'payments', 'totalPaid', 'remainingAmount', 'invoice'));
    }
    
    public function store(Request $request)
    {
        
        $validator = Validator::make($request->all(), [
            'order_id' => 'required|exists:orders,order_id',
            'amount' => 'required|numeric|min:0.01',
            'payment_method' => 'required|in:cash,card',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $order = Order::findOrFail($request->order_id);
        $payments = Payment::where('order_id', $request->order_id)->get();
        $totalPaid = $payments->sum('amount');
        $remainingAmount = $order->total_amount - $totalPaid;
        
        // Check if payment amount is valid
        if ($request->amount > $remainingAmount) {
            return redirect()->back()->with('error', 'مبلغ الدفع أكبر من المبلغ المتبقي')->withInput();
        }
        
        DB::beginTransaction();
        
        try {
            // Create payment
            $payment = Payment::create([
                'order_id' => $request->order_id,
                'amount' => $request->amount,
                'payment_method' => $request->payment_method,
            ]);
            
            // Update invoice payment status
            $invoice = Invoice::where('order_id', $request->order_id)->first();
            
            if ($invoice) {
                $newTotalPaid = $totalPaid + $request->amount;
                $newPaymentStatus = ($newTotalPaid >= $invoice->total_amount) ? 'paid' : 
                                    ($newTotalPaid > 0 ? 'partial' : 'unpaid');
                
                $invoice->update([
                    'payment_status' => $newPaymentStatus,
                ]);
            }
            
            // Create notification for the customer
            Notification::create([
                'user_id' => $order->user_id,
                'message' => 'تم استلام دفعة بقيمة ' . $request->amount . ' ريال للطلب رقم #' . $order->order_id,
                'is_read' => false,
            ]);
            
            DB::commit();
            
            return redirect()->route('employee.orders.show', $request->order_id)->with('success', 'تم تسجيل الدفعة بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء تسجيل الدفعة: ' . $e->getMessage())->withInput();
        }
    }
    
    // Customer methods for viewing payment history
    public function customerPayments()
    {
        $payments = Payment::whereHas('order', function($query) {
                $query->where('user_id', Auth::id());
            })
            ->with(['order'])
            ->orderBy('transaction_date', 'desc')
            ->paginate(10);
            
        return view('customer.payments.index', compact('payments'));
    }
    
    // طرق إضافية للفواتير
    public function createInvoice(Request $request)
    {
        $this->middleware('admin');
        
        $validator = Validator::make($request->all(), [
            'order_id' => 'required|exists:orders,order_id',
            'tax_amount' => 'required|numeric|min:0',
            'discount_amount' => 'required|numeric|min:0',
            'due_date' => 'required|date|after:today',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $order = Order::findOrFail($request->order_id);
        
        // Check if an invoice already exists
        $existingInvoice = Invoice::where('order_id', $request->order_id)->first();
        
        if ($existingInvoice) {
            return redirect()->back()->with('error', 'يوجد فاتورة بالفعل لهذا الطلب');
        }
        
        $totalAmount = $order->total_amount + $request->tax_amount - $request->discount_amount;
        
        Invoice::create([
            'order_id' => $request->order_id,
            'total_amount' => $totalAmount,
            'tax_amount' => $request->tax_amount,
            'discount_amount' => $request->discount_amount,
            'payment_status' => 'unpaid',
            'due_date' => $request->due_date,
        ]);

        return redirect()->route('admin.invoices')->with('success', 'تم إنشاء الفاتورة بنجاح');
    }
    
    public function updateInvoice(Request $request, $id)
    {
        $this->middleware('admin');
        
        $invoice = Invoice::findOrFail($id);
        
        $validator = Validator::make($request->all(), [
            'tax_amount' => 'required|numeric|min:0',
            'discount_amount' => 'required|numeric|min:0',
            'due_date' => 'required|date',
            'payment_status' => 'required|in:paid,unpaid,partial',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $order = Order::findOrFail($invoice->order_id);
        $totalAmount = $order->total_amount + $request->tax_amount - $request->discount_amount;
        
        $invoice->update([
            'total_amount' => $totalAmount,
            'tax_amount' => $request->tax_amount,
            'discount_amount' => $request->discount_amount,
            'payment_status' => $request->payment_status,
            'due_date' => $request->due_date,
        ]);

        return redirect()->route('admin.invoices.view', $id)->with('success', 'تم تحديث الفاتورة بنجاح');
    }
}