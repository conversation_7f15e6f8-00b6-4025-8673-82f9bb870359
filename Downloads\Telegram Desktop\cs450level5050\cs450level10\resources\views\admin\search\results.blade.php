@extends('layouts.admin')

@section('title', 'نتائج البحث - ' . $query)

@section('content')
<div class="mb-6">
    <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4">نتائج البحث: "{{ $query }}"</h2>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <form action="{{ route('search') }}" method="GET" class="mb-6">
            <div class="flex flex-col space-y-4">
                <div class="flex">
                    <input type="text" name="query" value="{{ $query }}" class="w-full px-4 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary">
                    <button type="submit" class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-l-md">
                        <i class="fas fa-search ml-2"></i>
                        بحث
                    </button>
                </div>

                <!-- خيارات البحث المتقدمة -->
                <div>
                    <button type="button" id="advancedSearchToggle" class="text-primary hover:underline flex items-center text-sm">
                        <i class="fas fa-sliders-h ml-1"></i>
                        خيارات البحث المتقدمة
                        <i class="fas fa-chevron-down mr-1 text-xs transition-transform" id="advancedSearchIcon"></i>
                    </button>

                    <div id="advancedSearchOptions" class="hidden mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-md">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <!-- نوع العنصر -->
                            <div>
                                <label class="block text-gray-700 dark:text-gray-300 mb-1 text-sm">البحث في</label>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="search_types[]" value="users" class="ml-2 text-primary focus:ring-primary" {{ request()->input('search_types') && in_array('users', request()->input('search_types')) ? 'checked' : '' }}>
                                        <span class="text-sm text-gray-700 dark:text-gray-300">المستخدمين</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="search_types[]" value="menu" class="ml-2 text-primary focus:ring-primary" {{ request()->input('search_types') && in_array('menu', request()->input('search_types')) ? 'checked' : '' }}>
                                        <span class="text-sm text-gray-700 dark:text-gray-300">قائمة الطعام</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="search_types[]" value="orders" class="ml-2 text-primary focus:ring-primary" {{ request()->input('search_types') && in_array('orders', request()->input('search_types')) ? 'checked' : '' }}>
                                        <span class="text-sm text-gray-700 dark:text-gray-300">الطلبات</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="search_types[]" value="reservations" class="ml-2 text-primary focus:ring-primary" {{ request()->input('search_types') && in_array('reservations', request()->input('search_types')) ? 'checked' : '' }}>
                                        <span class="text-sm text-gray-700 dark:text-gray-300">الحجوزات</span>
                                    </label>
                                </div>
                            </div>

                            <!-- تاريخ البحث -->
                            <div>
                                <label class="block text-gray-700 dark:text-gray-300 mb-1 text-sm">تاريخ البحث</label>
                                <div class="space-y-2">
                                    <div>
                                        <label class="text-sm text-gray-700 dark:text-gray-300 block mb-1">من</label>
                                        <input type="date" name="date_from" value="{{ request()->input('date_from') }}" class="w-full px-3 py-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary text-sm">
                                    </div>
                                    <div>
                                        <label class="text-sm text-gray-700 dark:text-gray-300 block mb-1">إلى</label>
                                        <input type="date" name="date_to" value="{{ request()->input('date_to') }}" class="w-full px-3 py-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary text-sm">
                                    </div>
                                </div>
                            </div>

                            <!-- خيارات إضافية -->
                            <div>
                                <label class="block text-gray-700 dark:text-gray-300 mb-1 text-sm">خيارات إضافية</label>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="exact_match" value="1" class="ml-2 text-primary focus:ring-primary" {{ request()->input('exact_match') ? 'checked' : '' }}>
                                        <span class="text-sm text-gray-700 dark:text-gray-300">تطابق تام</span>
                                    </label>
                                    <div>
                                        <label class="text-sm text-gray-700 dark:text-gray-300 block mb-1">ترتيب حسب</label>
                                        <select name="sort_by" class="w-full px-3 py-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary text-sm">
                                            <option value="relevance" {{ request()->input('sort_by') == 'relevance' ? 'selected' : '' }}>الصلة</option>
                                            <option value="newest" {{ request()->input('sort_by') == 'newest' ? 'selected' : '' }}>الأحدث</option>
                                            <option value="oldest" {{ request()->input('sort_by') == 'oldest' ? 'selected' : '' }}>الأقدم</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4 flex justify-end">
                            <button type="reset" class="bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-4 py-1 rounded-md text-sm ml-2 hover:bg-gray-300 dark:hover:bg-gray-500">
                                إعادة تعيين
                            </button>
                            <button type="submit" class="bg-primary hover:bg-primary/90 text-white px-4 py-1 rounded-md text-sm">
                                تطبيق الفلتر
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>

        <!-- سكريبت لتبديل خيارات البحث المتقدمة -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const advancedSearchToggle = document.getElementById('advancedSearchToggle');
                const advancedSearchOptions = document.getElementById('advancedSearchOptions');
                const advancedSearchIcon = document.getElementById('advancedSearchIcon');

                advancedSearchToggle.addEventListener('click', function() {
                    advancedSearchOptions.classList.toggle('hidden');
                    advancedSearchIcon.classList.toggle('rotate-180');
                });
            });
        </script>

        <!-- نتائج البحث -->
        <div class="space-y-8">
            <!-- المستخدمين -->
            @if(count($results['users']) > 0)
            <div>
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-users text-primary ml-2"></i>
                    المستخدمين
                </h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الاسم</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">البريد الإلكتروني</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">نوع المستخدم</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach($results['users'] as $user)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                                            {{ $user->first_name }} {{ $user->last_name }}
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ $user->email }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                        {{ $user->user_type == 'admin' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                                           ($user->user_type == 'employee' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                                           'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200') }}">
                                        {{ $user->user_type == 'admin' ? 'مدير' : ($user->user_type == 'employee' ? 'موظف' : 'عميل') }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="{{ route('admin.users.show', $user->user_id) }}" class="text-primary hover:text-primary-dark ml-2">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.users.edit', $user->user_id) }}" class="text-blue-600 hover:text-blue-900 ml-2">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                <div class="mt-4">
                    <a href="{{ route('admin.users', ['search' => $query]) }}" class="text-primary hover:underline">
                        عرض كل نتائج المستخدمين <i class="fas fa-arrow-left mr-1"></i>
                    </a>
                </div>
            </div>
            @endif

            <!-- قائمة الطعام -->
            @if(count($results['menuItems']) > 0)
            <div>
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-utensils text-primary ml-2"></i>
                    قائمة الطعام
                </h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    @foreach($results['menuItems'] as $item)
                    <div class="bg-white dark:bg-gray-700 rounded-lg shadow-sm overflow-hidden border border-gray-200 dark:border-gray-600">
                        <div class="p-4">
                            <h4 class="font-bold text-gray-800 dark:text-white mb-2">{{ $item->name }}</h4>
                            <p class="text-gray-600 dark:text-gray-300 text-sm mb-2">{{ Str::limit($item->description, 100) }}</p>
                            <div class="flex justify-between items-center">
                                <span class="text-primary font-bold">{{ $item->price }} د.ل</span>
                                <a href="{{ route('admin.menu.show', $item->item_id) }}" class="text-blue-600 hover:text-blue-900">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
                <div class="mt-4">
                    <a href="{{ route('admin.menu', ['search' => $query]) }}" class="text-primary hover:underline">
                        عرض كل نتائج قائمة الطعام <i class="fas fa-arrow-left mr-1"></i>
                    </a>
                </div>
            </div>
            @endif

            <!-- الطلبات -->
            @if(count($results['orders']) > 0)
            <div>
                <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4 flex items-center">
                    <i class="fas fa-shopping-cart text-primary ml-2"></i>
                    الطلبات
                </h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">رقم الطلب</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">العميل</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">المبلغ</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الحالة</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach($results['orders'] as $order)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">#{{ $order->order_id }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500 dark:text-gray-400">
                                        {{ $order->user->first_name }} {{ $order->user->last_name }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ $order->total_amount }} د.ل</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                        {{ $order->status == 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                                           ($order->status == 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                                           'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200') }}">
                                        {{ $order->status == 'completed' ? 'مكتمل' : ($order->status == 'pending' ? 'قيد الانتظار' : 'ملغي') }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <a href="{{ route('admin.orders.show', $order->order_id) }}" class="text-primary hover:text-primary-dark">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                <div class="mt-4">
                    <a href="{{ route('admin.orders', ['search' => $query]) }}" class="text-primary hover:underline">
                        عرض كل نتائج الطلبات <i class="fas fa-arrow-left mr-1"></i>
                    </a>
                </div>
            </div>
            @endif

            <!-- إذا لم يتم العثور على نتائج -->
            @if(count($results['users']) == 0 && count($results['menuItems']) == 0 && count($results['orders']) == 0 && count($results['reservations']) == 0 && count($results['ingredients']) == 0 && count($results['tables']) == 0 && count($results['expenses']) == 0)
            <div class="text-center py-8">
                <i class="fas fa-search text-gray-400 dark:text-gray-600 text-5xl mb-4"></i>
                <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-2">لم يتم العثور على نتائج</h3>
                <p class="text-gray-600 dark:text-gray-400">لم نتمكن من العثور على أي نتائج تطابق "{{ $query }}"</p>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
