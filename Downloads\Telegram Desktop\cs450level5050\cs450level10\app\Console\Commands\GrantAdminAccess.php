<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Permission;

class GrantAdminAccess extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:grant-admin-access {email} {--permissions=*}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'منح موظف صلاحية الوصول للوحة الإدارة';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $permissions = $this->option('permissions');

        // البحث عن المستخدم
        $user = User::where('email', $email)->first();

        if (!$user) {
            $this->error("المستخدم بالإيميل {$email} غير موجود");
            return 1;
        }

        if ($user->user_type !== 'employee') {
            $this->error("المستخدم ليس موظفاً");
            return 1;
        }

        // إعطاء صلاحية الوصول للوحة الإدارة
        $user->givePermissionTo('dashboard.admin');
        $this->info("تم منح {$user->first_name} {$user->last_name} صلاحية الوصول للوحة الإدارة");

        // إعطاء صلاحيات إضافية إذا تم تحديدها
        if (!empty($permissions)) {
            foreach ($permissions as $permission) {
                if (Permission::where('name', $permission)->exists()) {
                    $user->givePermissionTo($permission);
                    $this->info("تم منح صلاحية: {$permission}");
                } else {
                    $this->warn("الصلاحية غير موجودة: {$permission}");
                }
            }
        } else {
            // إعطاء صلاحيات أساسية للإدارة
            $basicPermissions = [
                'orders.view', 'orders.create', 'orders.edit', 'orders.status',
                'reservations.view', 'reservations.create', 'reservations.edit', 'reservations.status',
                'inventory.view',
                'tables.view', 'tables.status',
                'reports.view', 'reports.sales'
            ];

            foreach ($basicPermissions as $permission) {
                $user->givePermissionTo($permission);
            }

            $this->info("تم منح الصلاحيات الأساسية للإدارة");
        }

        $this->info("✅ تم بنجاح! يمكن للموظف الآن الوصول للوحة الإدارة");
        $this->line("الموظف: {$user->first_name} {$user->last_name} ({$user->email})");
        $this->line("إجمالي الصلاحيات: " . $user->getAllPermissions()->count());

        return 0;
    }
}
