<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class FinancialReport extends Model
{
    use HasFactory;

    protected $primaryKey = 'report_id';
    
    protected $fillable = [
        'report_type',
        'start_date',
        'end_date',
        'total_income',
        'total_expenses',
        'generated_by'
    ];

    protected $casts = [
        'start_date' => 'date',
        'end_date' => 'date',
        'total_income' => 'decimal:2',
        'total_expenses' => 'decimal:2',
        'created_at' => 'datetime'
    ];

    public function generator()
    {
        return $this->belongsTo(User::class, 'generated_by');
    }

    public function sources()
    {
        return $this->hasMany(ReportSource::class, 'report_id');
    }
}