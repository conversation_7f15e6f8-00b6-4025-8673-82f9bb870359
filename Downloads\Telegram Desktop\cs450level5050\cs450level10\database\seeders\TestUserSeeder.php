<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class TestUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء مستخدم تجريبي للاختبار
        User::create([
            'first_name' => 'أحمد',
            'last_name' => 'محمد',
            'email' => '<EMAIL>',
            'phone' => '+218912345678',
            'address' => 'طرابلس، ليبيا',
            'password' => Hash::make('password123'),
            'user_type' => 'customer',
            'is_active' => true
        ]);

        // إنشاء مستخدم إداري للاختبار
        User::create([
            'first_name' => 'سارة',
            'last_name' => 'أحمد',
            'email' => '<EMAIL>',
            'phone' => '+218912345679',
            'address' => 'بنغازي، ليبيا',
            'password' => Hash::make('admin123'),
            'user_type' => 'admin',
            'is_active' => true
        ]);

        echo "تم إنشاء المستخدمين التجريبيين بنجاح!\n";
        echo "العميل: <EMAIL> / password123\n";
        echo "المدير: <EMAIL> / admin123\n";
    }
}
