<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class EmployeeMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        if (Auth::check() && (Auth::user()->user_type === 'employee' || Auth::user()->user_type === 'admin')) {
            return $next($request);
        }

        return redirect()->route('login')->with('error', 'غير مصرح لك بالوصول إلى هذه الصفحة');
    }
}