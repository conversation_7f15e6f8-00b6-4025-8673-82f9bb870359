@extends('layouts.admin')

@section('title', 'التقارير المالية - لوحة تحكم Eat Hub')

@section('page-title', 'التقارير المالية')

@section('content')
<div class="mb-6">
    <div class="flex justify-between items-center mb-4">
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white">التقارير المالية</h2>

        <!-- فلتر التاريخ -->
        <form method="GET" class="flex items-center space-x-4 space-x-reverse">
            <div class="flex items-center space-x-2 space-x-reverse">
                <label class="text-sm text-gray-600 dark:text-gray-400">من:</label>
                <input type="date" name="start_date" value="{{ $startDate->format('Y-m-d') }}"
                       class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm dark:bg-gray-700 dark:text-white">
            </div>
            <div class="flex items-center space-x-2 space-x-reverse">
                <label class="text-sm text-gray-600 dark:text-gray-400">إلى:</label>
                <input type="date" name="end_date" value="{{ $endDate->format('Y-m-d') }}"
                       class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm dark:bg-gray-700 dark:text-white">
            </div>
            <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md text-sm hover:bg-primary/90">
                تطبيق
            </button>
        </form>
    </div>

    <!-- البطاقات الإحصائية المحسنة -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-6">
        <!-- إجمالي المبيعات -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 card-hover">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-xs mb-1">إجمالي المبيعات</p>
                    <h3 class="text-xl font-bold text-gray-800 dark:text-white">{{ number_format($totalSales, 2) }} د.ل</h3>
                    <p class="text-green-500 text-xs mt-1 flex items-center">
                        <i class="fas fa-chart-line mr-1"></i>
                        <span>{{ $stats['completed_orders'] + $stats['preparing_orders'] }} طلب</span>
                    </p>
                </div>
                <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-2">
                    <i class="fas fa-money-bill-wave text-green-500 text-sm"></i>
                </div>
            </div>
        </div>

        <!-- تكلفة المواد الخام -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 card-hover">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-xs mb-1">تكلفة المواد الخام</p>
                    <h3 class="text-xl font-bold text-orange-600 dark:text-orange-400">{{ number_format($costOfGoodsSold, 2) }} د.ل</h3>
                    <p class="text-orange-500 text-xs mt-1 flex items-center">
                        <i class="fas fa-boxes mr-1"></i>
                        <span>COGS</span>
                    </p>
                </div>
                <div class="rounded-full bg-orange-100 dark:bg-orange-900/30 p-2">
                    <i class="fas fa-industry text-orange-500 text-sm"></i>
                </div>
            </div>
        </div>

        <!-- إجمالي الربح -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 card-hover">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-xs mb-1">إجمالي الربح</p>
                    <h3 class="text-xl font-bold {{ $grossProfit >= 0 ? 'text-blue-600' : 'text-red-600' }} dark:text-white">
                        {{ number_format($grossProfit, 2) }} د.ل
                    </h3>
                    <p class="{{ $grossProfit >= 0 ? 'text-blue-500' : 'text-red-500' }} text-xs mt-1 flex items-center">
                        <i class="fas fa-percentage mr-1"></i>
                        <span>{{ number_format($grossProfitMargin, 1) }}%</span>
                    </p>
                </div>
                <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-2">
                    <i class="fas fa-chart-bar text-blue-500 text-sm"></i>
                </div>
            </div>
        </div>

        <!-- المصروفات التشغيلية -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 card-hover">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-xs mb-1">المصروفات التشغيلية</p>
                    <h3 class="text-xl font-bold text-red-600 dark:text-red-400">{{ number_format($totalExpenses, 2) }} د.ل</h3>
                    <p class="text-red-500 text-xs mt-1 flex items-center">
                        <i class="fas fa-receipt mr-1"></i>
                        <span>{{ $expensesByCategory->count() }} فئة</span>
                    </p>
                </div>
                <div class="rounded-full bg-red-100 dark:bg-red-900/30 p-2">
                    <i class="fas fa-hand-holding-usd text-red-500 text-sm"></i>
                </div>
            </div>
        </div>

        <!-- صافي الربح -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 card-hover border-2 {{ $netProfit >= 0 ? 'border-green-200' : 'border-red-200' }}">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-xs mb-1">صافي الربح</p>
                    <h3 class="text-xl font-bold {{ $netProfit >= 0 ? 'text-green-600' : 'text-red-600' }} dark:text-white">
                        {{ number_format($netProfit, 2) }} د.ل
                    </h3>
                    <p class="{{ $netProfit >= 0 ? 'text-green-500' : 'text-red-500' }} text-xs mt-1 flex items-center">
                        <i class="fas fa-{{ $netProfit >= 0 ? 'arrow-up' : 'arrow-down' }} mr-1"></i>
                        <span>{{ number_format($netProfitMargin, 1) }}%</span>
                    </p>
                </div>
                <div class="rounded-full {{ $netProfit >= 0 ? 'bg-green-100 dark:bg-green-900/30' : 'bg-red-100 dark:bg-red-900/30' }} p-2">
                    <i class="fas fa-coins {{ $netProfit >= 0 ? 'text-green-500' : 'text-red-500' }} text-sm"></i>
                </div>
            </div>
        </div>

        <!-- متوسط قيمة الطلب -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 card-hover">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-xs mb-1">متوسط قيمة الطلب</p>
                    <h3 class="text-xl font-bold text-purple-600 dark:text-purple-400">{{ number_format($averageOrderValue, 2) }} د.ل</h3>
                    <p class="text-purple-500 text-xs mt-1 flex items-center">
                        <i class="fas fa-calculator mr-1"></i>
                        <span>{{ $stats['total_orders'] }} طلب</span>
                    </p>
                </div>
                <div class="rounded-full bg-purple-100 dark:bg-purple-900/30 p-2">
                    <i class="fas fa-shopping-cart text-purple-500 text-sm"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- ملخص الربحية -->
    <div class="bg-gradient-to-r from-blue-50 to-green-50 dark:from-gray-800 dark:to-gray-700 rounded-lg p-6 mb-6">
        <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-4">ملخص الربحية</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center">
                <p class="text-sm text-gray-600 dark:text-gray-400">هامش الربح الإجمالي</p>
                <p class="text-2xl font-bold {{ $grossProfitMargin >= 0 ? 'text-blue-600' : 'text-red-600' }}">
                    {{ number_format($grossProfitMargin, 1) }}%
                </p>
                <p class="text-xs text-gray-500">المبيعات - تكلفة المواد</p>
            </div>
            <div class="text-center">
                <p class="text-sm text-gray-600 dark:text-gray-400">هامش الربح الصافي</p>
                <p class="text-2xl font-bold {{ $netProfitMargin >= 0 ? 'text-green-600' : 'text-red-600' }}">
                    {{ number_format($netProfitMargin, 1) }}%
                </p>
                <p class="text-xs text-gray-500">بعد خصم جميع المصروفات</p>
            </div>
            <div class="text-center">
                <p class="text-sm text-gray-600 dark:text-gray-400">نسبة المصروفات</p>
                <p class="text-2xl font-bold text-orange-600">
                    {{ $totalSales > 0 ? number_format(($totalExpenses / $totalSales) * 100, 1) : 0 }}%
                </p>
                <p class="text-xs text-gray-500">من إجمالي المبيعات</p>
            </div>
        </div>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
    <!-- مخطط المبيعات مقابل المصروفات -->
    <div class="lg:col-span-2 bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white">المبيعات مقابل المصروفات</h3>
            <div class="relative">
                <select id="salesPeriodFilter" onchange="updateSalesChart()" class="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white border-0 rounded-md p-2 text-sm focus:outline-none">
                    <option value="30">آخر 30 يوم</option>
                    <option value="7">آخر 7 أيام</option>
                    <option value="90">آخر 3 أشهر</option>
                    <option value="180">آخر 6 أشهر</option>
                    <option value="365">السنة الحالية</option>
                </select>
            </div>
        </div>
        <div id="salesExpensesChart" class="w-full h-80 min-h-80"></div>
    </div>

    <!-- مخطط توزيع المصروفات -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white">توزيع المصروفات</h3>
            <div class="relative">
                <select id="expensesPeriodFilter" onchange="updateExpensesChart()" class="bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-white border-0 rounded-md p-2 text-sm focus:outline-none">
                    <option value="30">الشهر الحالي</option>
                    <option value="7">آخر 7 أيام</option>
                    <option value="90">آخر 3 أشهر</option>
                    <option value="365">السنة الحالية</option>
                </select>
            </div>
        </div>
        <div id="expensesDistributionChart" class="w-full h-80 min-h-80"></div>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- أفضل المنتجات مبيعاً -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white">أفضل المنتجات مبيعاً</h3>
            <span class="text-sm text-gray-500 dark:text-gray-400">أعلى 10 منتجات</span>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full text-sm">
                <thead>
                    <tr class="bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                        <th class="py-2 px-3 text-right">المنتج</th>
                        <th class="py-2 px-3 text-right">الكمية</th>
                        <th class="py-2 px-3 text-right">المبيعات</th>
                        <th class="py-2 px-3 text-right">التكلفة</th>
                        <th class="py-2 px-3 text-right">الربح</th>
                        <th class="py-2 px-3 text-right">هامش الربح</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($topProducts as $product)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                        <td class="py-3 px-3 font-medium text-gray-800 dark:text-white">
                            {{ $product->menuItem->name ?? 'منتج محذوف' }}
                        </td>
                        <td class="py-3 px-3 text-gray-600 dark:text-gray-300">
                            {{ number_format($product->total_quantity) }}
                        </td>
                        <td class="py-3 px-3 text-gray-600 dark:text-gray-300">
                            {{ number_format($product->total_revenue, 2) }} د.ل
                        </td>
                        <td class="py-3 px-3 text-orange-600 dark:text-orange-400">
                            {{ number_format($product->total_cost, 2) }} د.ل
                        </td>
                        <td class="py-3 px-3 font-medium {{ $product->profit >= 0 ? 'text-green-600' : 'text-red-600' }}">
                            {{ number_format($product->profit, 2) }} د.ل
                        </td>
                        <td class="py-3 px-3">
                            <span class="px-2 py-1 text-xs rounded-full {{ $product->profit_margin >= 30 ? 'bg-green-100 text-green-800' : ($product->profit_margin >= 15 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                                {{ number_format($product->profit_margin, 1) }}%
                            </span>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="6" class="py-6 text-center text-gray-500 dark:text-gray-400">
                            لا توجد بيانات مبيعات في هذه الفترة
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- أداء الموظفين -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white">أداء الموظفين</h3>
            <a href="{{ route('admin.reports.employee-performance') }}" class="text-primary text-sm hover:underline">عرض التفاصيل</a>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full text-sm">
                <thead>
                    <tr class="bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                        <th class="py-2 px-3 text-right">الموظف</th>
                        <th class="py-2 px-3 text-right">عدد الطلبات</th>
                        <th class="py-2 px-3 text-right">إجمالي المبيعات</th>
                        <th class="py-2 px-3 text-right">متوسط قيمة الطلب</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($employeePerformance as $employee)
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                        <td class="py-3 px-3 font-medium text-gray-800 dark:text-white">
                            {{ $employee->creator->first_name ?? 'غير محدد' }} {{ $employee->creator->last_name ?? '' }}
                        </td>
                        <td class="py-3 px-3 text-gray-600 dark:text-gray-300">
                            {{ number_format($employee->orders_count) }}
                        </td>
                        <td class="py-3 px-3 text-gray-600 dark:text-gray-300">
                            {{ number_format($employee->total_sales, 2) }} د.ل
                        </td>
                        <td class="py-3 px-3 text-gray-600 dark:text-gray-300">
                            {{ number_format($employee->total_sales / $employee->orders_count, 2) }} د.ل
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="4" class="py-6 text-center text-gray-500 dark:text-gray-400">
                            لا توجد بيانات أداء موظفين في هذه الفترة
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // بيانات المخططات من الخادم
    console.log('🔄 تحميل بيانات التقرير المالي...');

    const monthlyData = @json($monthlyData ?? []);
    const expensesByCategory = @json($expensesByCategory ?? []);

    console.log('📊 البيانات الشهرية:', monthlyData);
    console.log('📊 المصروفات حسب الفئة:', expensesByCategory);

    // متغيرات المخططات
    let salesExpensesChart;
    let expensesDistChart;

    // دالة إنشاء مخطط المبيعات والمصروفات
    function createSalesExpensesChart() {
        const isDark = document.documentElement.classList.contains('dark');

        // التحقق من وجود البيانات
        if (!monthlyData || monthlyData.length === 0) {
            return null;
        }

        return {
            series: [{
                name: 'المبيعات',
                data: monthlyData.map(item => parseFloat(item.sales || 0))
            }, {
                name: 'تكلفة المواد',
                data: monthlyData.map(item => parseFloat(item.cogs || 0))
            }, {
                name: 'المصروفات التشغيلية',
                data: monthlyData.map(item => parseFloat(item.expenses || 0))
            }, {
                name: 'صافي الربح',
                data: monthlyData.map(item => parseFloat(item.net_profit || 0))
            }],
            chart: {
                height: 320,
                type: 'bar',
                fontFamily: 'Cairo, sans-serif',
                background: 'transparent',
                toolbar: { show: false }
            },
            theme: {
                mode: isDark ? 'dark' : 'light'
            },
            colors: isDark ? ['#34d399', '#fbbf24', '#f87171', '#60a5fa'] : ['#10b981', '#f59e0b', '#ef4444', '#3b82f6'],
            plotOptions: {
                bar: {
                    horizontal: false,
                    columnWidth: '55%',
                    endingShape: 'rounded'
                }
            },
            dataLabels: { enabled: false },
            stroke: { show: true, width: 2, colors: ['transparent'] },
            grid: {
                borderColor: isDark ? '#374151' : '#e5e7eb'
            },
            xaxis: {
                categories: monthlyData.map(item => item.month || 'غير محدد'),
                labels: {
                    style: {
                        colors: isDark ? '#9ca3af' : '#6b7280',
                        fontFamily: 'Cairo, sans-serif'
                    }
                },
                axisBorder: { color: isDark ? '#374151' : '#e5e7eb' },
                axisTicks: { color: isDark ? '#374151' : '#e5e7eb' }
            },
            yaxis: {
                labels: {
                    formatter: function(value) {
                        return value.toFixed(0) + ' د.ل';
                    },
                    style: {
                        colors: isDark ? '#9ca3af' : '#6b7280',
                        fontFamily: 'Cairo, sans-serif'
                    }
                }
            },
            fill: { opacity: 1 },
            tooltip: {
                theme: isDark ? 'dark' : 'light',
                y: {
                    formatter: function(value) {
                        return value.toFixed(2) + ' د.ل';
                    }
                }
            }
        };
    }

    // دالة إنشاء مخطط توزيع المصروفات
    function createExpensesDistChart() {
        const isDark = document.documentElement.classList.contains('dark');

        // التحقق من وجود البيانات
        if (!expensesByCategory || expensesByCategory.length === 0) {
            return null;
        }

        return {
            series: expensesByCategory.map(item => parseFloat(item.total || 0)),
            chart: {
                height: 320,
                type: 'donut',
                fontFamily: 'Cairo, sans-serif',
                background: 'transparent',
                toolbar: { show: false }
            },
            theme: {
                mode: isDark ? 'dark' : 'light'
            },
            labels: expensesByCategory.map(item => item.category_name || item.category || 'غير محدد'),
            colors: isDark ? ['#60a5fa', '#34d399', '#fbbf24', '#f87171', '#a78bfa'] : ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'],
            legend: {
                position: 'bottom',
                fontFamily: 'Cairo, sans-serif',
                labels: {
                    colors: isDark ? '#e5e7eb' : '#4b5563'
                }
            },
            plotOptions: {
                pie: {
                    donut: { size: '65%' }
                }
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: { width: 300 },
                    legend: { position: 'bottom' }
                }
            }],
            tooltip: {
                theme: isDark ? 'dark' : 'light',
                y: {
                    formatter: function(value) {
                        return value.toFixed(2) + ' د.ل';
                    }
                }
            }
        };
    }

    // دالة إنشاء المخططات
    function initFinancialCharts() {
        initSalesExpensesChart();
        initExpensesDistChart();
    }

    // دالة إنشاء مخطط المبيعات والمصروفات
    function initSalesExpensesChart() {
        const chartElement = document.getElementById('salesExpensesChart');
        if (!chartElement) return;

        try {
            if (salesExpensesChart) {
                salesExpensesChart.destroy();
            }

            const chartOptions = createSalesExpensesChart();
            if (!chartOptions) {
                showNoDataMessage(chartElement, 'لا توجد بيانات مالية');
                return;
            }

            salesExpensesChart = new ApexCharts(chartElement, chartOptions);
            salesExpensesChart.render().then(() => {
                console.log('✅ تم إنشاء مخطط المبيعات والمصروفات');
            });

        } catch (error) {
            console.error('❌ خطأ في إنشاء مخطط المبيعات:', error);
            showErrorMessage(chartElement);
        }
    }

    // دالة إنشاء مخطط توزيع المصروفات
    function initExpensesDistChart() {
        const chartElement = document.getElementById('expensesDistributionChart');
        if (!chartElement) return;

        try {
            if (expensesDistChart) {
                expensesDistChart.destroy();
            }

            const chartOptions = createExpensesDistChart();
            if (!chartOptions) {
                showNoDataMessage(chartElement, 'لا توجد بيانات مصروفات');
                return;
            }

            expensesDistChart = new ApexCharts(chartElement, chartOptions);
            expensesDistChart.render().then(() => {
                console.log('✅ تم إنشاء مخطط توزيع المصروفات');
            });

        } catch (error) {
            console.error('❌ خطأ في إنشاء مخطط المصروفات:', error);
            showErrorMessage(chartElement);
        }
    }

    // دالة عرض رسالة عدم وجود بيانات
    function showNoDataMessage(element, message) {
        element.innerHTML = `
            <div class="flex flex-col items-center justify-center h-80 text-gray-500 dark:text-gray-400">
                <i class="fas fa-chart-bar text-4xl mb-4"></i>
                <h3 class="text-lg font-medium mb-2">${message}</h3>
                <p class="text-sm text-center">لم يتم العثور على بيانات لعرضها في المخطط</p>
            </div>
        `;
    }

    // دالة عرض رسالة خطأ
    function showErrorMessage(element) {
        element.innerHTML = `
            <div class="flex flex-col items-center justify-center h-80 text-red-500">
                <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                <h3 class="text-lg font-medium mb-2">خطأ في تحميل المخطط</h3>
                <p class="text-sm text-center">حدث خطأ أثناء تحميل المخطط</p>
                <button onclick="initFinancialCharts()" class="mt-4 px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
                    إعادة المحاولة
                </button>
            </div>
        `;
    }

    // دوال تحديث المخططات
    function updateSalesChart() {
        const period = document.getElementById('salesPeriodFilter')?.value || 30;
        console.log('🔄 تحديث مخطط المبيعات للفترة:', period);
        // يمكن إضافة منطق التحديث هنا لاحقاً
    }

    function updateExpensesChart() {
        const period = document.getElementById('expensesPeriodFilter')?.value || 30;
        console.log('🔄 تحديث مخطط المصروفات للفترة:', period);
        // يمكن إضافة منطق التحديث هنا لاحقاً
    }

    window.addEventListener('load', function() {
        initFinancialCharts();
    });

    // دوال تحديث المخططات حسب الفترة المختارة
    function updateSalesChart() {
        if (financialCharts) {
            financialCharts.updateSalesChart();
        }
    }

    function updateExpensesChart() {
        if (financialCharts) {
            financialCharts.updateExpensesChart();
        }
    }

    // إضافة زر لإجبار تحديث المخططات
    function forceUpdateAllCharts() {
        if (typeof window.forceUpdateCharts === 'function') {
            window.forceUpdateCharts();
        }
    }

    // تحديث تلقائي عند تغيير الوضع المظلم
    document.addEventListener('DOMContentLoaded', function() {
        // مراقبة تغيير الوضع المظلم
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    setTimeout(forceUpdateAllCharts, 200);
                }
            });
        });

        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['class']
        });
    });
</script>

<style>
/* إصلاح مشاكل المخططات في التقرير المالي */
#salesExpensesChart,
#expensesDistributionChart {
    position: relative;
    z-index: 1;
    overflow: hidden;
}

#salesExpensesChart .apexcharts-canvas,
#expensesDistributionChart .apexcharts-canvas {
    position: relative !important;
}

#salesExpensesChart .apexcharts-svg,
#expensesDistributionChart .apexcharts-svg {
    background: transparent !important;
}

/* تحسين المخططات في الوضع المظلم */
.dark #salesExpensesChart .apexcharts-text,
.dark #expensesDistributionChart .apexcharts-text {
    fill: #9ca3af !important;
}

.dark #salesExpensesChart .apexcharts-gridline,
.dark #expensesDistributionChart .apexcharts-gridline {
    stroke: #374151 !important;
}

.dark #salesExpensesChart .apexcharts-xaxis-line,
.dark #salesExpensesChart .apexcharts-yaxis-line,
.dark #expensesDistributionChart .apexcharts-xaxis-line,
.dark #expensesDistributionChart .apexcharts-yaxis-line {
    stroke: #374151 !important;
}

/* تحسين التصميم المتجاوب */
@media (max-width: 768px) {
    #salesExpensesChart,
    #expensesDistributionChart {
        height: 300px !important;
        min-height: 300px !important;
    }
}

/* إصلاح تداخل العناصر */
.chart-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: transparent;
}

/* تحسين الألوان في الوضع المظلم */
.dark .apexcharts-tooltip {
    background: #374151 !important;
    border: 1px solid #4b5563 !important;
    color: #e5e7eb !important;
}

.dark .apexcharts-tooltip-title {
    background: #4b5563 !important;
    border-bottom: 1px solid #6b7280 !important;
    color: #f3f4f6 !important;
}

.dark .apexcharts-legend-text {
    color: #e5e7eb !important;
}

/* تحسين مخطط الدونات */
.dark #expensesDistributionChart .apexcharts-datalabel-label {
    fill: #f3f4f6 !important;
}

.dark #expensesDistributionChart .apexcharts-datalabel-value {
    fill: #e5e7eb !important;
}

/* تحسين مخطط الأعمدة */
.dark #salesExpensesChart .apexcharts-bar-area {
    stroke: transparent !important;
}

/* إصلاح مشكلة التداخل مع الجداول */
.grid.grid-cols-1.lg\\:grid-cols-3 {
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.bg-white.dark\\:bg-gray-800 {
    position: relative;
    z-index: 1;
}

/* تحسين مساحة المخططات */
.chart-wrapper {
    width: 100%;
    height: 100%;
    min-height: 320px;
    position: relative;
    overflow: hidden;
}
</style>

@endsection
