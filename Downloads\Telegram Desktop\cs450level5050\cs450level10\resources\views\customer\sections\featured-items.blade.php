<!-- قسم الأطباق المميزة -->
<div class="bg-gray-50 dark:bg-gray-900 py-16">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-800 dark:text-white mb-4">أطباقنا المميزة</h2>
            <p class="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">تحضر بمهارة عالية وباستخدام أفضل المكونات الطازجة لإرضاء ذوقك</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @forelse($featuredItems as $item)
            <div class="menu-item-card bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
                <div class="h-48 overflow-hidden relative">
                    <img src="{{ $item->image_url ?? 'https://images.unsplash.com/photo-1513104890138-7c749659a591?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=80' }}" alt="{{ $item->name }}" class="menu-item-image w-full h-full object-cover">
                    @if(isset($item->is_bestseller) && $item->is_bestseller)
                    <div class="absolute top-4 right-4 bg-white dark:bg-gray-900 text-primary rounded-full py-1 px-3 text-sm font-semibold">
                        الأكثر مبيعاً
                    </div>
                    @endif
                </div>
                <div class="p-5">
                    <div class="flex justify-between items-start mb-2">
                        <h3 class="font-bold text-lg text-gray-800 dark:text-white">{{ $item->name }}</h3>
                        <span class="font-bold text-primary">{{ $item->price }} د.ل</span>
                    </div>
                    <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">{{ $item->description }}</p>
                    <div class="flex justify-between items-center">
                        <div class="text-yellow-400 flex text-sm">
                            @if(isset($item->rating))
                                @for($i = 1; $i <= 5; $i++)
                                    @if($i <= $item->rating)
                                        <i class="fas fa-star"></i>
                                    @elseif($i - 0.5 <= $item->rating)
                                        <i class="fas fa-star-half-alt"></i>
                                    @else
                                        <i class="far fa-star"></i>
                                    @endif
                                @endfor
                                <span class="text-gray-600 dark:text-gray-400 mr-1">{{ $item->rating }}</span>
                            @else
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star-half-alt"></i>
                                <span class="text-gray-600 dark:text-gray-400 mr-1">4.5</span>
                            @endif
                        </div>
                        @auth
                        <button class="add-to-cart btn-hover-effect bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-full text-sm transition"
                                onclick="addToCart({{ $item->item_id ?? $loop->index + 1 }}, '{{ $item->name }}', {{ $item->price }})"
                                data-item-id="{{ $item->item_id ?? $loop->index + 1 }}">
                            <i class="fas fa-plus ml-1"></i>إضافة للسلة
                        </button>
                        @else
                        <a href="{{ route('login') }}" class="bg-gray-400 hover:bg-gray-500 text-white py-2 px-4 rounded-full text-sm transition">
                            <i class="fas fa-sign-in-alt ml-1"></i>سجل دخول للطلب
                        </a>
                        @endauth
                    </div>
                </div>
            </div>
            @empty
            <!-- عناصر افتراضية في حالة عدم وجود عناصر مميزة -->
            @include('customer.components.default-menu-item')
            @endforelse
        </div>

        <div class="mt-12 text-center">
            <a href="{{ route('customer.menu') }}" class="btn-hover-effect inline-block bg-white dark:bg-gray-800 border-2 border-primary text-primary hover:bg-primary/5 dark:hover:bg-gray-700 font-bold py-3 px-8 rounded-full text-lg transition">
                عرض القائمة كاملة
            </a>
        </div>
    </div>
</div>
