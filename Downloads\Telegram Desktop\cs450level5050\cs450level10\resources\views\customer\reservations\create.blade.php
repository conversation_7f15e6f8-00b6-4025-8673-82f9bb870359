@include('customer.partials.head')

<body class="bg-gray-50 dark:bg-gray-900">

@include('customer.partials.header')

<!-- المحتوى الرئيسي -->
<main class="min-h-screen pt-20">
    <div class="container mx-auto px-4 py-8">
        <!-- عنوان الصفحة -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-800 dark:text-white mb-4">احجز طاولة</h1>
            <p class="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                احجز طاولتك الآن واستمتع بتجربة طعام لا تُنسى في أجواء مريحة
            </p>
        </div>

        <div class="max-w-2xl mx-auto">
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-md p-8">
                <form action="{{ route('customer.reservations.store') }}" method="POST">
                    @csrf

                    <!-- معلومات الحجز -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <!-- تاريخ الحجز -->
                        <div>
                            <label for="reservation_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                تاريخ الحجز
                            </label>
                            <input type="date"
                                   id="reservation_date"
                                   name="reservation_date"
                                   min="{{ date('Y-m-d') }}"
                                   value="{{ date('Y-m-d') }}"
                                   class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                   required>
                        </div>

                        <!-- وقت الحجز -->
                        <div>
                            <label for="reservation_time" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                وقت الحجز
                            </label>
                            <select id="reservation_time"
                                    name="reservation_time"
                                    class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                    required>
                                <option value="">اختر الوقت</option>
                                <option value="12:00" selected>12:00 ظهراً</option>
                                <option value="12:30">12:30 ظهراً</option>
                                <option value="13:00">1:00 ظهراً</option>
                                <option value="13:30">1:30 ظهراً</option>
                                <option value="14:00">2:00 ظهراً</option>
                                <option value="14:30">2:30 ظهراً</option>
                                <option value="15:00">3:00 عصراً</option>
                                <option value="19:00">7:00 مساءً</option>
                                <option value="19:30">7:30 مساءً</option>
                                <option value="20:00">8:00 مساءً</option>
                                <option value="20:30">8:30 مساءً</option>
                                <option value="21:00">9:00 مساءً</option>
                                <option value="21:30">9:30 مساءً</option>
                                <option value="22:00">10:00 مساءً</option>
                            </select>
                        </div>
                    </div>

                    <!-- عدد الأشخاص -->
                    <div class="mb-6">
                        <label for="party_size" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            عدد الأشخاص
                        </label>
                        <select id="party_size"
                                name="party_size"
                                class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                required>
                            <option value="">اختر عدد الأشخاص</option>
                            @for($i = 1; $i <= 10; $i++)
                            <option value="{{ $i }}" {{ $i == 2 ? 'selected' : '' }}>{{ $i }} {{ $i == 1 ? 'شخص' : 'أشخاص' }}</option>
                            @endfor
                        </select>
                    </div>

                    <!-- زر البحث عن الطاولات -->
                    <div class="mb-6">
                        <button type="button" id="checkAvailabilityBtn"
                                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg transition disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-search ml-2"></i>
                            البحث عن الطاولات المتاحة
                        </button>
                    </div>

                    <!-- عرض الطاولات المتاحة -->
                    <div id="availableTablesSection" class="mb-6 hidden">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">
                            اختر الطاولة المناسبة:
                        </label>
                        <div id="availableTablesList" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- سيتم ملء هذا القسم بـ JavaScript -->
                        </div>
                        <input type="hidden" name="selected_table" id="selected_table" required>
                    </div>

                    <!-- ملاحظات خاصة -->
                    <div class="mb-6">
                        <label for="special_requests" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            ملاحظات خاصة (اختياري)
                        </label>
                        <textarea id="special_requests"
                                  name="special_requests"
                                  rows="4"
                                  class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                                  placeholder="أي طلبات خاصة أو ملاحظات..."></textarea>
                    </div>

                    <!-- رسوم الحجز والدفع -->
                    <div class="mb-6 bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                            <i class="fas fa-credit-card ml-2 text-primary"></i>رسوم الحجز والدفع
                        </h3>

                        <!-- رسوم الحجز -->
                        <div class="mb-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-gray-600 dark:text-gray-400">رسوم حجز الطاولة:</span>
                                <span class="font-bold text-lg text-primary">25.00 د.ل</span>
                            </div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                رسوم الحجز مطلوبة لتأكيد الحجز وسيتم خصمها من قيمة الطلب النهائي
                            </p>
                        </div>

                        <!-- طرق الدفع -->
                        <div class="space-y-3">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                اختر طريقة الدفع:
                            </label>

                            <label class="flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600">
                                <input type="radio" name="payment_method" value="cash" class="ml-2" checked>
                                <i class="fas fa-money-bill-wave text-green-500 ml-3"></i>
                                <div>
                                    <div class="font-medium text-gray-800 dark:text-white">الدفع في المطعم</div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400">ادفع رسوم الحجز عند الوصول للمطعم</div>
                                </div>
                            </label>

                            <label class="flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600">
                                <input type="radio" name="payment_method" value="card" class="ml-2">
                                <i class="fas fa-credit-card text-blue-500 ml-3"></i>
                                <div>
                                    <div class="font-medium text-gray-800 dark:text-white">بطاقة ائتمان</div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400">ادفع الآن بالبطاقة الائتمانية</div>
                                </div>
                            </label>

                            <label class="flex items-center p-3 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-600">
                                <input type="radio" name="payment_method" value="bank_transfer" class="ml-2">
                                <i class="fas fa-university text-purple-500 ml-3"></i>
                                <div>
                                    <div class="font-medium text-gray-800 dark:text-white">تحويل بنكي</div>
                                    <div class="text-sm text-gray-600 dark:text-gray-400">تحويل مباشر للحساب البنكي</div>
                                </div>
                            </label>
                        </div>

                        <!-- تفاصيل الدفع بالبطاقة -->
                        <div id="cardPaymentDetails" class="mt-4 space-y-4 hidden">
                            <div class="p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600">
                                <h4 class="font-semibold text-gray-800 dark:text-white mb-3">تفاصيل البطاقة</h4>
                                <div class="grid grid-cols-1 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">رقم البطاقة</label>
                                        <input type="text" name="card_number" placeholder="1234 5678 9012 3456" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                                    </div>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">تاريخ الانتهاء</label>
                                            <input type="text" name="card_expiry" placeholder="MM/YY" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">CVV</label>
                                            <input type="text" name="card_cvv" placeholder="123" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تفاصيل التحويل البنكي -->
                        <div id="bankTransferDetails" class="mt-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 hidden">
                            <h4 class="font-semibold text-gray-800 dark:text-white mb-2">تفاصيل الحساب البنكي:</h4>
                            <div class="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                                <p><strong>اسم البنك:</strong> البنك الأهلي الليبي</p>
                                <p><strong>رقم الحساب:</strong> ************</p>
                                <p><strong>اسم المستفيد:</strong> مطعم Eat Hub</p>
                                <p><strong>المبلغ:</strong> 25.00 د.ل</p>
                                <p class="text-red-600 dark:text-red-400 mt-2">
                                    <i class="fas fa-exclamation-triangle ml-1"></i>
                                    يرجى إرسال إيصال التحويل عبر الواتساب: 0912345678
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="flex flex-col sm:flex-row gap-4">
                        <button type="submit"
                                class="flex-1 bg-primary hover:bg-primary/90 text-white font-bold py-3 px-6 rounded-lg transition">
                            <i class="fas fa-calendar-check ml-2"></i>
                            تأكيد الحجز
                        </button>
                        <a href="{{ route('customer.index') }}"
                           class="flex-1 bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-bold py-3 px-6 rounded-lg transition text-center">
                            <i class="fas fa-times ml-2"></i>
                            إلغاء
                        </a>
                    </div>
                </form>
            </div>

            <!-- معلومات إضافية -->
            <div class="mt-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-3">
                    <i class="fas fa-info-circle ml-2"></i>
                    معلومات مهمة
                </h3>
                <ul class="text-blue-700 dark:text-blue-300 space-y-2">
                    <li><i class="fas fa-check ml-2"></i>يمكن إلغاء الحجز حتى ساعتين قبل الموعد</li>
                    <li><i class="fas fa-check ml-2"></i>سيتم تأكيد الحجز خلال 30 دقيقة</li>
                    <li><i class="fas fa-check ml-2"></i>في حالة التأخير أكثر من 15 دقيقة، قد يتم إلغاء الحجز</li>
                    <li><i class="fas fa-check ml-2"></i>للمجموعات الكبيرة (أكثر من 8 أشخاص)، يرجى الاتصال بنا مباشرة</li>
                </ul>
            </div>
        </div>
    </div>
</main>

@include('customer.partials.footer')

<script>
document.addEventListener('DOMContentLoaded', function() {
    // عناصر النموذج
    const checkAvailabilityBtn = document.getElementById('checkAvailabilityBtn');
    const availableTablesSection = document.getElementById('availableTablesSection');
    const availableTablesList = document.getElementById('availableTablesList');
    const selectedTableInput = document.getElementById('selected_table');
    const reservationForm = document.querySelector('form');

    // التحقق من توفر الطاولات
    checkAvailabilityBtn.addEventListener('click', function() {
        const date = document.getElementById('reservation_date').value;
        const time = document.getElementById('reservation_time').value;
        const partySize = document.getElementById('party_size').value;

        console.log('Form values:', { date, time, partySize });

        if (!date || !time || !partySize) {
            showNotification('يرجى إدخال التاريخ والوقت وعدد الأشخاص أولاً', 'error');
            return;
        }

        // تعطيل الزر وإظهار حالة التحميل
        checkAvailabilityBtn.disabled = true;
        checkAvailabilityBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري البحث...';

        // التحقق من CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]');
        console.log('CSRF token element:', csrfToken);
        console.log('CSRF token value:', csrfToken ? csrfToken.getAttribute('content') : 'Not found');

        // إرسال طلب AJAX
        fetch('{{ route("customer.reservations.check-availability") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                date: date,
                time: time,
                party_size: parseInt(partySize)
            })
        })
        .then(response => {
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success) {
                displayAvailableTables(data.available_tables);
                showNotification(data.message, 'success');
            } else {
                showNotification(data.message || 'لا توجد طاولات متاحة', 'error');
                availableTablesSection.classList.add('hidden');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('حدث خطأ أثناء البحث عن الطاولات: ' + error.message, 'error');
        })
        .finally(() => {
            // إعادة تفعيل الزر
            checkAvailabilityBtn.disabled = false;
            checkAvailabilityBtn.innerHTML = '<i class="fas fa-search ml-2"></i>البحث عن الطاولات المتاحة';
        });
    });

    // عرض الطاولات المتاحة
    function displayAvailableTables(tables) {
        availableTablesList.innerHTML = '';

        if (tables.length === 0) {
            availableTablesSection.classList.add('hidden');
            return;
        }

        tables.forEach(table => {
            const tableCard = document.createElement('div');
            tableCard.className = 'table-option border border-gray-300 dark:border-gray-600 rounded-lg p-4 cursor-pointer hover:border-primary hover:bg-primary/5 transition';
            tableCard.dataset.tableId = table.table_id;

            tableCard.innerHTML = `
                <div class="flex items-center justify-between">
                    <div>
                        <h4 class="font-bold text-gray-800 dark:text-white">طاولة #${table.table_number}</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400">${table.location}</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">سعة: ${table.capacity} أشخاص</p>
                    </div>
                    <div class="text-primary">
                        <i class="fas fa-chair text-2xl"></i>
                    </div>
                </div>
                <div class="mt-2">
                    <span class="inline-block bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 px-2 py-1 rounded-full text-xs">
                        متاحة
                    </span>
                </div>
            `;

            tableCard.addEventListener('click', function() {
                // إزالة التحديد من جميع الطاولات
                document.querySelectorAll('.table-option').forEach(option => {
                    option.classList.remove('border-primary', 'bg-primary/10');
                    option.classList.add('border-gray-300', 'dark:border-gray-600');
                });

                // تحديد الطاولة المختارة
                this.classList.remove('border-gray-300', 'dark:border-gray-600');
                this.classList.add('border-primary', 'bg-primary/10');

                selectedTableInput.value = table.table_id;
            });

            availableTablesList.appendChild(tableCard);
        });

        availableTablesSection.classList.remove('hidden');
    }

    // التحكم في طرق الدفع
    const paymentMethodRadios = document.querySelectorAll('input[name="payment_method"]');
    const cardPaymentDetails = document.getElementById('cardPaymentDetails');
    const bankTransferDetails = document.getElementById('bankTransferDetails');

    paymentMethodRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            // إخفاء جميع التفاصيل
            cardPaymentDetails.classList.add('hidden');
            bankTransferDetails.classList.add('hidden');

            // إظهار التفاصيل المناسبة
            if (this.value === 'card') {
                cardPaymentDetails.classList.remove('hidden');
            } else if (this.value === 'bank_transfer') {
                bankTransferDetails.classList.remove('hidden');
            }
        });
    });

    // تنسيق رقم البطاقة
    const cardNumberInput = document.querySelector('input[name="card_number"]');
    if (cardNumberInput) {
        cardNumberInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
            if (formattedValue.length > 19) formattedValue = formattedValue.substr(0, 19);
            e.target.value = formattedValue;
        });
    }

    // تنسيق تاريخ انتهاء البطاقة
    const cardExpiryInput = document.querySelector('input[name="card_expiry"]');
    if (cardExpiryInput) {
        cardExpiryInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length >= 2) {
                value = value.substring(0, 2) + '/' + value.substring(2, 4);
            }
            e.target.value = value;
        });
    }

    // تنسيق CVV
    const cardCvvInput = document.querySelector('input[name="card_cvv"]');
    if (cardCvvInput) {
        cardCvvInput.addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/\D/g, '').substring(0, 3);
        });
    }

    // التحقق من النموذج قبل الإرسال
    reservationForm.addEventListener('submit', function(e) {
        // التحقق من اختيار طاولة
        if (!selectedTableInput.value) {
            e.preventDefault();
            showNotification('يرجى البحث عن الطاولات المتاحة واختيار طاولة', 'error');
            return;
        }

        const paymentMethod = document.querySelector('input[name="payment_method"]:checked');
        if (!paymentMethod) {
            e.preventDefault();
            showNotification('يرجى اختيار طريقة الدفع', 'error');
            return;
        }

        if (paymentMethod.value === 'card') {
            const cardNumber = document.querySelector('input[name="card_number"]').value;
            const cardExpiry = document.querySelector('input[name="card_expiry"]').value;
            const cardCvv = document.querySelector('input[name="card_cvv"]').value;

            if (!cardNumber || !cardExpiry || !cardCvv) {
                e.preventDefault();
                showNotification('يرجى إدخال جميع بيانات البطاقة', 'error');
                return;
            }

            if (cardNumber.replace(/\s/g, '').length < 16) {
                e.preventDefault();
                showNotification('رقم البطاقة غير صحيح', 'error');
                return;
            }
        }

        // إظهار رسالة تأكيد
        showNotification('جاري معالجة طلب الحجز...', 'info');
    });
});

// عرض الإشعارات
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-20 left-4 z-50 p-4 rounded-lg shadow-lg transition-all duration-300 transform translate-x-full ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${
                type === 'success' ? 'fa-check-circle' :
                type === 'error' ? 'fa-exclamation-circle' :
                'fa-info-circle'
            } ml-2"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.classList.remove('translate-x-full');
    }, 100);

    setTimeout(() => {
        notification.classList.add('translate-x-full');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}
</script>

@include('customer.partials.scripts')

</body>
</html>
