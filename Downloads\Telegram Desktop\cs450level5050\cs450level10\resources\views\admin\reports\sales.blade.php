@extends('layouts.admin')

@section('title', 'تقرير المبيعات - لوحة تحكم Eat Hub')

@section('page-title', 'تقرير المبيعات')

@section('content')
<div class="mb-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">تقرير المبيعات</h2>
        <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 sm:space-x-reverse">
            <a href="{{ route('admin.reports') }}" class="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-200 py-2 px-4 rounded-md transition-all">
                <i class="fas fa-arrow-right ml-1"></i>
                <span>العودة للتقارير</span>
            </a>
            <button onclick="window.print()" class="bg-blue-100 hover:bg-blue-200 dark:bg-blue-900/30 dark:hover:bg-blue-800/30 text-blue-700 dark:text-blue-400 py-2 px-4 rounded-md transition-all print:hidden">
                <i class="fas fa-print ml-1"></i>
                <span>طباعة التقرير</span>
            </button>
        </div>
    </div>

    <!-- فلاتر التقرير -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
        <form action="{{ route('admin.reports.sales') }}" method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
                <label for="period" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">الفترة الزمنية</label>
                <select id="period" name="period" onchange="toggleCustomDates(this.value)" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                    <option value="week" {{ isset($period) && $period == 'week' ? 'selected' : '' }}>آخر أسبوع</option>
                    <option value="month" {{ isset($period) && $period == 'month' ? 'selected' : '' }}>آخر شهر</option>
                    <option value="quarter" {{ isset($period) && $period == 'quarter' ? 'selected' : '' }}>آخر 3 أشهر</option>
                    <option value="year" {{ isset($period) && $period == 'year' ? 'selected' : '' }}>آخر سنة</option>
                    <option value="custom" {{ isset($period) && $period == 'custom' ? 'selected' : '' }}>فترة مخصصة</option>
                </select>
            </div>
            <div id="custom_dates" class="grid grid-cols-1 md:grid-cols-2 gap-4 md:col-span-2" style="{{ isset($period) && $period == 'custom' ? '' : 'display: none;' }}">
                <div>
                    <label for="custom_start" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">من تاريخ</label>
                    <input type="date" id="custom_start" name="custom_start" value="{{ isset($startDate) ? $startDate : '' }}" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                </div>
                <div>
                    <label for="custom_end" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">إلى تاريخ</label>
                    <input type="date" id="custom_end" name="custom_end" value="{{ isset($endDate) ? $endDate : '' }}" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                </div>
            </div>
            <div class="flex items-end">
                <button type="submit" class="bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-md transition-all">
                    <i class="fas fa-filter ml-1"></i>
                    <span>تطبيق الفلتر</span>
                </button>
            </div>
        </form>
    </div>

    <!-- ملخص المبيعات -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">إجمالي المبيعات</p>
                    <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ isset($totalSales) ? number_format($totalSales, 2) : '0.00' }} د.ل</h3>
                </div>
                <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-3">
                    <i class="fas fa-money-bill-wave text-green-500 text-xl"></i>
                </div>
            </div>
        </div>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">عدد الطلبات</p>
                    <h3 class="text-2xl font-bold text-gray-800 dark:text-white">{{ isset($ordersCount) ? $ordersCount : '0' }}</h3>
                </div>
                <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3">
                    <i class="fas fa-shopping-cart text-blue-500 text-xl"></i>
                </div>
            </div>
        </div>
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <div class="flex justify-between items-start">
                <div>
                    <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">متوسط قيمة الطلب</p>
                    <h3 class="text-2xl font-bold text-gray-800 dark:text-white">
                        {{ isset($averageOrderValue) ? number_format($averageOrderValue, 2) : '0.00' }} د.ل
                    </h3>
                </div>
                <div class="rounded-full bg-purple-100 dark:bg-purple-900/30 p-3">
                    <i class="fas fa-chart-line text-purple-500 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- مخطط المبيعات -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white">المبيعات اليومية</h3>
        </div>
        <div id="dailySalesChart" class="w-full h-80"></div>
    </div>

    <!-- المبيعات حسب الفئة -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">المبيعات حسب الفئة</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full text-sm">
                    <thead>
                        <tr class="bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                            <th class="py-2 px-3 text-right">الفئة</th>
                            <th class="py-2 px-3 text-right">المبيعات</th>
                            <th class="py-2 px-3 text-right">النسبة</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                        @if(isset($salesByCategory) && count($salesByCategory) > 0)
                            @php
                                $totalCategorySales = $salesByCategory->sum('total');
                            @endphp
                            @foreach($salesByCategory as $category)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                                <td class="py-3 px-3 font-medium text-gray-800 dark:text-white">{{ $category->category }}</td>
                                <td class="py-3 px-3 text-gray-600 dark:text-gray-300">{{ number_format($category->total, 2) }} د.ل</td>
                                <td class="py-3 px-3">
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <div class="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                                            <div class="bg-primary h-full rounded-full" style="width: {{ $totalCategorySales > 0 ? ($category->total / $totalCategorySales) * 100 : 0 }}%;"></div>
                                        </div>
                                        <span class="text-gray-600 dark:text-gray-300">{{ $totalCategorySales > 0 ? number_format(($category->total / $totalCategorySales) * 100, 1) : 0 }}%</span>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        @else
                            <tr>
                                <td colspan="3" class="py-4 px-3 text-center text-gray-500">لا توجد بيانات متاحة</td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>

        <!-- المنتجات الأكثر مبيعاً -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-bold text-gray-800 dark:text-white">المنتجات الأكثر مبيعاً</h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full text-sm">
                    <thead>
                        <tr class="bg-gray-50 dark:bg-gray-700 text-gray-600 dark:text-gray-400">
                            <th class="py-2 px-3 text-right">المنتج</th>
                            <th class="py-2 px-3 text-right">الكمية</th>
                            <th class="py-2 px-3 text-right">المبيعات</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                        @if(isset($topProducts) && count($topProducts) > 0)
                            @foreach($topProducts as $product)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                                <td class="py-3 px-3 font-medium text-gray-800 dark:text-white">{{ $product->name }}</td>
                                <td class="py-3 px-3 text-gray-600 dark:text-gray-300">{{ $product->quantity }}</td>
                                <td class="py-3 px-3 text-gray-600 dark:text-gray-300">{{ number_format($product->total, 2) }} د.ل</td>
                            </tr>
                            @endforeach
                        @else
                            <tr>
                                <td colspan="3" class="py-4 px-3 text-center text-gray-500">لا توجد بيانات متاحة</td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    function toggleCustomDates(value) {
        const customDatesDiv = document.getElementById('custom_dates');
        if (value === 'custom') {
            customDatesDiv.style.display = 'grid';
        } else {
            customDatesDiv.style.display = 'none';
        }
    }

    // تهيئة المخططات البيانية
    window.addEventListener('load', function() {
        // مخطط المبيعات اليومية
        if (document.getElementById('dailySalesChart')) {
            const salesData = @json(isset($dailySales) ? $dailySales : []);
            const dates = [];
            const values = [];

            // تحويل البيانات إلى تنسيق مناسب للمخطط
            salesData.forEach(item => {
                dates.push(item.date);
                values.push(parseFloat(item.total));
            });

            const dailySalesOptions = {
                series: [{
                    name: 'المبيعات',
                    data: values
                }],
                chart: {
                    height: 320,
                    type: 'area',
                    fontFamily: 'Cairo, sans-serif',
                    toolbar: {
                        show: false
                    }
                },
                dataLabels: {
                    enabled: false
                },
                stroke: {
                    curve: 'smooth',
                    width: 2
                },
                colors: ['#FF6B35'],
                fill: {
                    type: 'gradient',
                    gradient: {
                        shadeIntensity: 1,
                        opacityFrom: 0.7,
                        opacityTo: 0.3,
                        stops: [0, 90, 100]
                    }
                },
                grid: {
                    borderColor: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb',
                },
                xaxis: {
                    categories: dates,
                    labels: {
                        style: {
                            colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280',
                            fontFamily: 'Cairo, sans-serif'
                        }
                    }
                },
                yaxis: {
                    labels: {
                        formatter: function(value) {
                            return value + ' د.ل';
                        },
                        style: {
                            colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280',
                            fontFamily: 'Cairo, sans-serif'
                        }
                    }
                },
                tooltip: {
                    y: {
                        formatter: function(value) {
                            return value + ' د.ل';
                        }
                    }
                }
            };

            const dailySalesChart = new ApexCharts(document.getElementById('dailySalesChart'), dailySalesOptions);
            dailySalesChart.render();
        }
    });
</script>
@endsection
