<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

/**
 * @property-read \Illuminate\Database\Eloquent\Collection|\Spatie\Permission\Models\Role[] $roles
 * @method bool hasRole(string $role)
 * @method bool hasAnyRole(array|string ...$roles)
 * @method \Spatie\Permission\Models\Role[] getRoleNames()
 * @method $this assignRole(...$roles)
 */
class User extends Authenticatable
{
    use HasFactory, Notifiable, HasRoles;

    protected $primaryKey = 'user_id';

    protected $fillable = [
        'email',
        'password',
        'first_name',
        'last_name',
        'phone',
        'address',
        'user_type',
        'is_active'
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // إضافة نطاق عام للحصول على المستخدمين النشطين فقط
        static::addGlobalScope('active', function (Builder $builder) {
            // لا نطبق هذا النطاق في صفحات الإدارة
            if (!request()->is('admin*')) {
                $builder->where('is_active', true);
            }
        });
    }

    // العلاقات
    public function orders()
    {
        return $this->hasMany(Order::class, 'user_id');
    }

    public function reservations()
    {
        return $this->hasMany(Reservation::class, 'user_id');
    }

    public function reviews()
    {
        return $this->hasMany(Review::class, 'user_id');
    }

    public function inventoryTransactions()
    {
        return $this->hasMany(InventoryTransaction::class, 'user_id');
    }

    public function notifications()
    {
        return $this->hasMany(Notification::class, 'user_id');
    }

    public function expenses()
    {
        return $this->hasMany(Expense::class, 'recorded_by');
    }

    public function financialReports()
    {
        return $this->hasMany(FinancialReport::class, 'generated_by');
    }

    /**
     * التحقق من صلاحية المستخدم
     * (استبدال الدالة القديمة التي كانت تستخدم UserTypePermission)
     */
    public function hasPermission($permissionName)
    {
        return $this->can($permissionName);
    }

    /**
     * التحقق إذا كان المستخدم مديراً
     * (التوافق مع الحقل user_type أو نظام spatie)
     */


    /**
     * دالة مساعدة للتحقق من أي صلاحية من مجموعة
     */
    public function hasAnyPermission(array $permissions): bool
    {
        foreach ($permissions as $permission) {
            if ($this->can($permission)) {
                return true;
            }
        }
        return false;
    }

    /**
     * دالة مساعدة للتحقق من جميع الصلاحيات في مجموعة
     */
    public function hasAllPermissions(array $permissions): bool
    {
        foreach ($permissions as $permission) {
            if (!$this->can($permission)) {
                return false;
            }
        }
        return true;
    }
    public function isAdmin(): bool
    {
        return $this->user_type === 'admin';
    }

    /**
     * التحقق إذا كان المستخدم موظفاً
     */
    public function isEmployee(): bool
    {
        return $this->user_type === 'employee';
    }

    /**
     * التحقق إذا كان المستخدم زبوناً
     */
    public function isCustomer(): bool
    {
        return $this->user_type === 'customer';
    }
}