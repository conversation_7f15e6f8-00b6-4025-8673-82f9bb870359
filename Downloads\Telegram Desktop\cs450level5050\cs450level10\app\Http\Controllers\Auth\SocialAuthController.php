<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Str;

class SocialAuthController extends Controller
{
    // Google OAuth
    public function redirectToGoogle()
    {
        try {
            return Socialite::driver('google')->redirect();
        } catch (\Exception $e) {
            return redirect()->route('login')->with('error', 'حدث خطأ أثناء الاتصال بـ Google');
        }
    }

    public function handleGoogleCallback()
    {
        try {
            $socialUser = Socialite::driver('google')->user();
            return $this->handleSocialUser($socialUser, 'google');
        } catch (\Exception $e) {
            return redirect()->route('login')->with('error', 'حدث خطأ أثناء تسجيل الدخول بـ Google');
        }
    }

    // Facebook OAuth
    public function redirectToFacebook()
    {
        try {
            return Socialite::driver('facebook')->redirect();
        } catch (\Exception $e) {
            return redirect()->route('login')->with('error', 'حدث خطأ أثناء الاتصال بـ Facebook');
        }
    }

    public function handleFacebookCallback()
    {
        try {
            $socialUser = Socialite::driver('facebook')->user();
            return $this->handleSocialUser($socialUser, 'facebook');
        } catch (\Exception $e) {
            return redirect()->route('login')->with('error', 'حدث خطأ أثناء تسجيل الدخول بـ Facebook');
        }
    }

    // Apple OAuth
    public function redirectToApple()
    {
        try {
            return Socialite::driver('apple')->redirect();
        } catch (\Exception $e) {
            return redirect()->route('login')->with('error', 'حدث خطأ أثناء الاتصال بـ Apple');
        }
    }

    public function handleAppleCallback()
    {
        try {
            $socialUser = Socialite::driver('apple')->user();
            return $this->handleSocialUser($socialUser, 'apple');
        } catch (\Exception $e) {
            return redirect()->route('login')->with('error', 'حدث خطأ أثناء تسجيل الدخول بـ Apple');
        }
    }

    /**
     * التعامل مع المستخدم من الحساب الاجتماعي
     */
    private function handleSocialUser($socialUser, $provider)
    {
        // البحث عن المستخدم بالبريد الإلكتروني
        $user = User::where('email', $socialUser->getEmail())->first();

        if ($user) {
            // تحديث معلومات المستخدم الموجود
            $user->update([
                $provider . '_id' => $socialUser->getId(),
                'avatar' => $socialUser->getAvatar(),
            ]);
        } else {
            // إنشاء مستخدم جديد
            $user = User::create([
                'first_name' => $this->extractFirstName($socialUser->getName()),
                'last_name' => $this->extractLastName($socialUser->getName()),
                'email' => $socialUser->getEmail(),
                'password' => Hash::make(Str::random(16)), // كلمة مرور عشوائية
                'phone' => '', // سيتم ملؤها لاحقاً
                'user_type' => 'customer',
                'is_active' => true,
                'email_verified_at' => now(),
                $provider . '_id' => $socialUser->getId(),
                'avatar' => $socialUser->getAvatar(),
            ]);
        }

        // تسجيل دخول المستخدم
        Auth::login($user, true);

        // إعادة التوجيه حسب دور المستخدم
        return $this->redirectUserBasedOnRole($user);
    }

    /**
     * استخراج الاسم الأول من الاسم الكامل
     */
    private function extractFirstName($fullName)
    {
        $nameParts = explode(' ', trim($fullName));
        return $nameParts[0] ?? 'مستخدم';
    }

    /**
     * استخراج الاسم الأخير من الاسم الكامل
     */
    private function extractLastName($fullName)
    {
        $nameParts = explode(' ', trim($fullName));
        if (count($nameParts) > 1) {
            array_shift($nameParts); // إزالة الاسم الأول
            return implode(' ', $nameParts);
        }
        return '';
    }

    /**
     * إعادة توجيه المستخدم حسب دوره
     */
    private function redirectUserBasedOnRole($user)
    {
        switch ($user->user_type) {
            case 'admin':
                return redirect()->route('admin.dashboard')->with('success', 'مرحباً بك في لوحة تحكم الإدارة');
            case 'employee':
                return redirect()->route('employee.dashboard')->with('success', 'مرحباً بك في لوحة تحكم الموظف');
            case 'customer':
            default:
                return redirect()->route('customer.index')->with('success', 'مرحباً بك في Eat Hub');
        }
    }
}
