/**
 * مدير الثيم - إدارة الوضع الليلي/النهاري
 */
class ThemeManager {
    constructor() {
        this.currentTheme = localStorage.getItem("theme_preference") || "light";
        this.init();
    }

    init() {
        // تحميل الثيم المحفوظ
        this.loadSavedTheme();

        // التأكد من تطبيق الثيم
        this.ensureThemeApplied();

        // إضافة مستمعي الأحداث
        this.attachEventListeners();

        // مراقبة تغييرات النظام
        this.watchSystemTheme();

        // مراقبة تغييرات الصفحة
        this.watchPageChanges();
    }

    /**
     * التأكد من تطبيق الثيم
     */
    ensureThemeApplied() {
        const html = document.documentElement;
        const currentClass = html.classList.contains("dark") ? "dark" : "light";
        const expectedTheme = this.getEffectiveTheme();

        if (currentClass !== expectedTheme) {
            this.applyTheme();
        }
    }

    /**
     * تحميل الثيم المحفوظ - مبسط وفعال
     */
    async loadSavedTheme() {
        // تحميل من localStorage مع ضمان وجود قيمة
        this.currentTheme = localStorage.getItem("theme_preference") || "light";

        // التأكد من صحة القيمة
        if (!["light", "dark", "auto"].includes(this.currentTheme)) {
            this.currentTheme = "light";
            localStorage.setItem("theme_preference", this.currentTheme);
        }
    }

    /**
     * تطبيق الثيم مع ضمان الثبات الكامل
     */
    applyTheme() {
        const html = document.documentElement;

        // إزالة جميع فئات الثيم
        html.classList.remove("light", "dark");

        let effectiveTheme = this.currentTheme;

        // إذا كان الثيم تلقائي، استخدم إعدادات النظام
        if (this.currentTheme === "auto") {
            effectiveTheme = window.matchMedia("(prefers-color-scheme: dark)")
                .matches
                ? "dark"
                : "light";
        }

        // تطبيق الثيم بقوة
        html.className = ""; // مسح جميع الكلاسات
        html.classList.add(effectiveTheme);
        html.setAttribute("data-theme", effectiveTheme);

        // تحديث أيقونة الثيم
        this.updateThemeIcon(effectiveTheme);

        // حفظ في localStorage
        localStorage.setItem("theme_preference", this.currentTheme);
        localStorage.setItem("effective_theme", effectiveTheme);
        localStorage.setItem("theme_timestamp", Date.now().toString());
    }

    /**
     * تطبيق الثيم على العناصر المحددة
     */
    applyThemeToElements(theme) {
        // تحديث ألوان المخططات إذا كانت موجودة
        if (window.ApexCharts && window.salesChart) {
            const isDark = theme === "dark";
            window.salesChart.updateOptions({
                grid: {
                    borderColor: isDark ? "#374151" : "#e5e7eb",
                },
                xaxis: {
                    labels: {
                        style: {
                            colors: isDark ? "#9ca3af" : "#6b7280",
                        },
                    },
                },
                yaxis: {
                    labels: {
                        style: {
                            colors: isDark ? "#9ca3af" : "#6b7280",
                        },
                    },
                },
            });
        }
    }

    /**
     * تحديث أيقونة الثيم
     */
    updateThemeIcon(effectiveTheme) {
        const themeButtons = document.querySelectorAll("[data-theme-toggle]");
        const themeIcons = document.querySelectorAll(".theme-icon");

        themeButtons.forEach((button) => {
            const icon =
                button.querySelector("i") ||
                button.querySelector(".theme-icon");
            if (icon) {
                // إزالة جميع فئات الأيقونات
                icon.classList.remove("fa-sun", "fa-moon", "fa-adjust");

                // إضافة الأيقونة المناسبة
                switch (this.currentTheme) {
                    case "light":
                        icon.classList.add("fa-sun");
                        break;
                    case "dark":
                        icon.classList.add("fa-moon");
                        break;
                    case "auto":
                    default:
                        icon.classList.add("fa-adjust");
                        break;
                }
            }
        });
    }

    /**
     * تبديل الثيم
     */
    async toggleTheme() {
        const themes = ["auto", "light", "dark"];
        const currentIndex = themes.indexOf(this.currentTheme);
        const nextIndex = (currentIndex + 1) % themes.length;

        await this.setTheme(themes[nextIndex]);
    }

    /**
     * تعيين ثيم محدد
     */
    async setTheme(theme) {
        if (!["light", "dark", "auto"].includes(theme)) {
            theme = "auto";
        }

        this.currentTheme = theme;

        // حفظ فوري في localStorage لضمان الثبات
        localStorage.setItem("theme_preference", theme);

        // تطبيق الثيم فوراً
        this.applyTheme();

        // حفظ على الخادم في الخلفية
        try {
            await fetch("/theme/toggle", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN":
                        document
                            .querySelector('meta[name="csrf-token"]')
                            ?.getAttribute("content") || "",
                },
                body: JSON.stringify({ theme: theme }),
            });
        } catch (error) {
            console.warn("فشل في حفظ الثيم على الخادم:", error);
            // الثيم محفوظ محلياً، لذا لا مشكلة
        }

        // إظهار رسالة تأكيد
        this.showThemeChangeNotification(theme);
    }

    /**
     * إضافة مستمعي الأحداث
     */
    attachEventListeners() {
        // أزرار تبديل الثيم
        document.addEventListener("click", (e) => {
            const themeToggle = e.target.closest("[data-theme-toggle]");
            if (themeToggle) {
                e.preventDefault();
                this.toggleTheme();
            }
        });

        // اختصارات لوحة المفاتيح
        document.addEventListener("keydown", (e) => {
            // Ctrl/Cmd + Shift + T لتبديل الثيم
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === "T") {
                e.preventDefault();
                this.toggleTheme();
            }
        });
    }

    /**
     * مراقبة تغييرات ثيم النظام
     */
    watchSystemTheme() {
        const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");

        mediaQuery.addEventListener("change", () => {
            if (this.currentTheme === "auto") {
                this.applyTheme();
            }
        });
    }

    /**
     * مراقبة تغييرات الصفحة لضمان ثبات الثيم
     */
    watchPageChanges() {
        // مراقبة تغييرات DOM
        const observer = new MutationObserver(() => {
            this.ensureThemeApplied();
        });

        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ["class"],
        });

        // مراقبة أحداث التنقل
        window.addEventListener("popstate", () => {
            setTimeout(() => this.ensureThemeApplied(), 100);
        });

        // مراقبة تحميل المحتوى
        document.addEventListener("DOMContentLoaded", () => {
            this.ensureThemeApplied();
        });

        // مراقبة تغيير الصفحة
        let lastUrl = location.href;
        new MutationObserver(() => {
            const url = location.href;
            if (url !== lastUrl) {
                lastUrl = url;
                setTimeout(() => this.ensureThemeApplied(), 100);
            }
        }).observe(document, { subtree: true, childList: true });
    }

    /**
     * إظهار رسالة تأكيد تغيير الثيم
     */
    showThemeChangeNotification(theme) {
        const messages = {
            light: "تم التبديل للوضع النهاري ☀️",
            dark: "تم التبديل للوضع الليلي 🌙",
            auto: "تم التبديل للوضع التلقائي ⚙️",
        };

        const message = messages[theme] || "تم تغيير الثيم";

        // إنشاء إشعار مؤقت
        const notification = document.createElement("div");
        notification.className =
            "fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300 transform translate-x-full";
        notification.textContent = message;

        document.body.appendChild(notification);

        // إظهار الإشعار
        setTimeout(() => {
            notification.classList.remove("translate-x-full");
        }, 100);

        // إخفاء الإشعار بعد 3 ثوان
        setTimeout(() => {
            notification.classList.add("translate-x-full");
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    /**
     * الحصول على الثيم الحالي
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * الحصول على الثيم الفعال (بعد تطبيق القواعد التلقائية)
     */
    getEffectiveTheme() {
        if (this.currentTheme === "auto") {
            return window.matchMedia("(prefers-color-scheme: dark)").matches
                ? "dark"
                : "light";
        }
        return this.currentTheme;
    }
}

// تهيئة مدير الثيم عند تحميل الصفحة
document.addEventListener("DOMContentLoaded", () => {
    window.themeManager = new ThemeManager();
});

// ضمان ثبات الثيم عند تغيير الصفحة
window.addEventListener("beforeunload", () => {
    const currentTheme = localStorage.getItem("theme_preference");
    const effectiveTheme = localStorage.getItem("effective_theme");
    if (currentTheme && effectiveTheme) {
        // التأكد من حفظ الثيم قبل مغادرة الصفحة
        localStorage.setItem("theme_preference", currentTheme);
        localStorage.setItem("effective_theme", effectiveTheme);
        localStorage.setItem("theme_timestamp", Date.now().toString());
    }
});

// ضمان تطبيق الثيم عند العودة للصفحة
window.addEventListener("pageshow", (event) => {
    if (event.persisted && window.themeManager) {
        // إعادة تطبيق الثيم إذا كانت الصفحة محفوظة في الكاش
        window.themeManager.applyTheme();
    }
});

// تصدير للاستخدام العام
window.ThemeManager = ThemeManager;
