<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Schema;

class ThemeController extends Controller
{
    /**
     * تغيير الثيم للمستخدم
     */
    public function toggle(Request $request)
    {
        $theme = $request->input('theme', 'auto');

        // التحقق من صحة قيمة الثيم
        if (!in_array($theme, ['light', 'dark', 'auto'])) {
            $theme = 'auto';
        }

        // حفظ الثيم في الكوكيز (للمستخدمين غير المسجلين)
        $cookie = cookie('theme_preference', $theme, 60 * 24 * 365); // سنة واحدة

        // حفظ الثيم في قاعدة البيانات للمستخدمين المسجلين
        if (Auth::check()) {
            try {
                $user = Auth::user();
                // التحقق من وجود العمود قبل التحديث
                if (Schema::hasColumn('users', 'theme_preference')) {
                    $user->update(['theme_preference' => $theme]);
                }
            } catch (\Exception $e) {
                // في حالة عدم وجود العمود، نستخدم الكوكيز فقط
            }
        }

        return response()->json([
            'success' => true,
            'theme' => $theme,
            'message' => 'تم تغيير الثيم بنجاح'
        ])->withCookie($cookie);
    }

    /**
     * الحصول على الثيم الحالي للمستخدم
     */
    public function current(Request $request)
    {
        $theme = 'auto'; // القيمة الافتراضية

        // للمستخدمين المسجلين، جرب قراءة من قاعدة البيانات
        if (Auth::check()) {
            try {
                $user = Auth::user();
                if (Schema::hasColumn('users', 'theme_preference') && isset($user->theme_preference)) {
                    $theme = $user->theme_preference;
                }
            } catch (\Exception $e) {
                // في حالة عدم وجود العمود، استخدم الكوكيز
                $theme = $request->cookie('theme_preference', 'auto');
            }
        } else {
            // للمستخدمين غير المسجلين، استخدم الكوكيز
            $theme = $request->cookie('theme_preference', 'auto');
        }

        return response()->json([
            'theme' => $theme
        ]);
    }
}
