<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Eat Hub - @yield('title')</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#FF6B35',
                        secondary: '#4CAF50',
                        accent: '#FFEB3B',
                        warmBrown: '#C8A080',
                        darkText: '#333333',
                    },
                    fontFamily: {
                        sans: ['Cairo', 'sans-serif'],
                    },
                }
            }
        };
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
        
        body {
            font-family: 'Cairo', sans-serif;
        }
        
        .auth-bg {
            background-image: url('https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-1.2.1&auto=format&fit=crop&w=1567&q=80');
            background-size: cover;
            background-position: center;
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .auth-form-container {
            backdrop-filter: blur(8px);
            background-color: rgba(255, 255, 255, 0.9);
        }
        
        .dark .auth-form-container {
            background-color: rgba(30, 41, 59, 0.9);
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 min-h-screen">
    <div class="flex min-h-screen">
        <!-- Left Side - Authentication Form -->
        <div class="w-full lg:w-1/2 flex items-center justify-center p-4">
            @yield('content')
        </div>
        
        <!-- Right Side - Image and Text -->
        <div class="hidden lg:block lg:w-1/2 auth-bg relative">
            <div class="absolute inset-0 bg-gradient-to-b from-primary/80 to-primary/40 flex flex-col justify-center items-center text-white p-12">
                <div class="max-w-md text-center">
                    <h2 class="text-4xl font-bold mb-6">مرحباً بك في Eat Hub</h2>
                    <p class="text-xl mb-8">استمتع بتجربة طعام فريدة مع أشهى المأكولات والمشروبات</p>
                    <div class="space-y-6">
                        <div class="flex items-center">
                            <div class="bg-white/20 p-2 rounded-full mr-4">
                                <i class="fas fa-utensils text-2xl"></i>
                            </div>
                            <p class="text-left">قائمة متنوعة من الأطباق العالمية والمحلية</p>
                        </div>
                        <div class="flex items-center">
                            <div class="bg-white/20 p-2 rounded-full mr-4">
                                <i class="fas fa-shopping-cart text-2xl"></i>
                            </div>
                            <p class="text-left">سهولة في طلب وتتبع الطعام</p>
                        </div>
                        <div class="flex items-center">
                            <div class="bg-white/20 p-2 rounded-full mr-4">
                                <i class="fas fa-calendar-check text-2xl"></i>
                            </div>
                            <p class="text-left">إمكانية حجز طاولة في أي وقت</p>
                        </div>
                    </div>
                </div>
                
                <div class="absolute bottom-4 left-4 flex items-center">
                    <button id="darkModeToggle" class="p-2 rounded-full bg-white/10 text-white hover:bg-white/20">
                        <i id="darkModeIcon" class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تبديل الوضع المظلم/الفاتح
        const darkModeToggle = document.getElementById('darkModeToggle');
        const darkModeIcon = document.getElementById('darkModeIcon');
        
        darkModeToggle.addEventListener('click', function() {
            if (document.documentElement.classList.contains('dark')) {
                document.documentElement.classList.remove('dark');
                darkModeIcon.classList.remove('fa-sun');
                darkModeIcon.classList.add('fa-moon');
            } else {
                document.documentElement.classList.add('dark');
                darkModeIcon.classList.remove('fa-moon');
                darkModeIcon.classList.add('fa-sun');
            }
        });
        
        // تحديث أيقونة الوضع المظلم عند التحميل
        if (document.documentElement.classList.contains('dark')) {
            darkModeIcon.classList.remove('fa-moon');
            darkModeIcon.classList.add('fa-sun');
        }
    </script>
    
    @yield('scripts')
</body>
</html>