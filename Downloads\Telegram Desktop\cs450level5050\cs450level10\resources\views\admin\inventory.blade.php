@extends('layouts.admin')

@section('title', 'إدارة المخزون - لوحة تحكم Eat Hub')

@section('page-title', 'إدارة المخزون')

@section('content')
<div class="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
    <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">إدارة المخزون</h2>
    <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 sm:space-x-reverse">
        <a href="{{ route('admin.inventory.create') }}" class="bg-primary hover:bg-primary/90 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
            <i class="fas fa-plus ml-2"></i>
            <span>إضافة مخزون</span>
        </a>
        <a href="{{ route('admin.ingredients.create') }}" class="bg-secondary hover:bg-secondary/90 text-white font-bold py-2 px-4 rounded-md flex items-center justify-center transition-all">
            <i class="fas fa-plus ml-2"></i>
            <span>إضافة مكون</span>
        </a>
    </div>
</div>

<div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-6">
    <!-- إحصائية المخزون 1 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">إجمالي المكونات</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">48</h3>
            </div>
            <div class="rounded-full bg-blue-100 dark:bg-blue-900/30 p-3">
                <i class="fas fa-list text-blue-500 text-xl"></i>
            </div>
        </div>
    </div>
    
    <!-- إحصائية المخزون 2 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">قيمة المخزون</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">24,850 ر.س</h3>
            </div>
            <div class="rounded-full bg-green-100 dark:bg-green-900/30 p-3">
                <i class="fas fa-money-bill-wave text-green-500 text-xl"></i>
            </div>
        </div>
    </div>
    
    <!-- إحصائية المخزون 3 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">منتجات منخفضة</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">6</h3>
            </div>
            <div class="rounded-full bg-red-100 dark:bg-red-900/30 p-3">
                <i class="fas fa-exclamation-triangle text-red-500 text-xl"></i>
            </div>
        </div>
    </div>
    
    <!-- إحصائية المخزون 4 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <div class="flex justify-between items-start">
            <div>
                <p class="text-gray-500 dark:text-gray-400 text-sm mb-1">منتهية الصلاحية قريباً</p>
                <h3 class="text-2xl font-bold text-gray-800 dark:text-white">4</h3>
            </div>
            <div class="rounded-full bg-orange-100 dark:bg-orange-900/30 p-3">
                <i class="fas fa-clock text-orange-500 text-xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- علامات تبويب المخزون -->
<div class="bg-white dark:bg-gray-800 rounded-lg shadow-md mb-6 overflow-hidden">
    <div class="flex border-b border-gray-200 dark:border-gray-700">
        <button class="px-6 py-3 border-b-2 border-primary text-primary font-medium">المكونات</button>
        <button class="px-6 py-3 text-gray-600 dark:text-gray-300 font-medium hover:text-primary">المخزون</button>
        <a href="{{ route('admin.inventory.transactions') }}" class="px-6 py-3 text-gray-600 dark:text-gray-300 font-medium hover:text-primary">حركات المخزون</a>
        <a href="{{ route('admin.inventory.low-stock') }}" class="px-6 py-3 text-gray-600 dark:text-gray-300 font-medium hover:text-primary">المنتجات منخفضة المخزون</a>
    </div>
    
    <div class="p-6">
        <div class="mb-4 flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <h3 class="text-lg font-bold text-gray-800 dark:text-white mb-2 sm:mb-0">قائمة المكونات</h3>
            <div class="relative">
                <input type="text" placeholder="بحث..." class="px-4 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary text-base">
                <button class="absolute left-3 top-2.5 text-gray-500 dark:text-gray-400">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead>
                    <tr class="bg-gray-50 dark:bg-gray-700">
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            المكون
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            وحدة القياس
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            الكمية المتوفرة
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            التكلفة / وحدة
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            الحالة
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            تاريخ الصلاحية
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                            لحم بقري
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            كجم
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            2.5
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            75 ر.س
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 py-1 text-xs rounded-full bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                                مخزون منخفض
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            15/05/2024
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <a href="{{ route('admin.inventory.edit', 1) }}" class="text-blue-500 hover:text-blue-700 transition-all">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button onclick="deleteInventory(1)" class="text-red-500 hover:text-red-700 transition-all">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                            دقيق
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            كجم
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            5
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            3.5 ر.س
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 py-1 text-xs rounded-full bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300">
                                مخزون منخفض
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            01/06/2024
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <a href="{{ route('admin.inventory.edit', 2) }}" class="text-blue-500 hover:text-blue-700 transition-all">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button onclick="deleteInventory(2)" class="text-red-500 hover:text-red-700 transition-all">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                            طماطم
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            كجم
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            20
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            4 ر.س
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 py-1 text-xs rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                متوفر
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            20/04/2024
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <a href="{{ route('admin.inventory.edit', 3) }}" class="text-blue-500 hover:text-blue-700 transition-all">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button onclick="deleteInventory(3)" class="text-red-500 hover:text-red-700 transition-all">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                            جبنة موزاريلا
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            كجم
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            8
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            35 ر.س
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 py-1 text-xs rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
                                مخزون متوسط
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            10/05/2024
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <a href="{{ route('admin.inventory.edit', 4) }}" class="text-blue-500 hover:text-blue-700 transition-all">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button onclick="deleteInventory(4)" class="text-red-500 hover:text-red-700 transition-all">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700/30">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-800 dark:text-white">
                            صلصة بيستو
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            لتر
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            12
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 dark:text-gray-300">
                            25 ر.س
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="px-2 py-1 text-xs rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                متوفر
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class="text-orange-500">تنتهي في 3 أيام</span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <a href="{{ route('admin.inventory.edit', 5) }}" class="text-blue-500 hover:text-blue-700 transition-all">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button onclick="deleteInventory(5)" class="text-red-500 hover:text-red-700 transition-all">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- نموذج تأكيد الحذف -->
<div id="deleteModal" class="fixed inset-0 bg-black/50 z-50 hidden flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md mx-auto">
        <h3 class="text-xl font-bold text-gray-800 dark:text-white mb-4">تأكيد الحذف</h3>
        <p class="text-gray-600 dark:text-gray-300 mb-6">هل أنت متأكد من رغبتك في حذف هذا المكون؟ هذا الإجراء لا يمكن التراجع عنه.</p>
        <div class="flex justify-end space-x-2 space-x-reverse">
            <button id="cancelDelete" class="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600">إلغاء</button>
            <form id="deleteForm" method="POST" action="">
                @csrf
                @method('DELETE')
                <button type="submit" class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600">حذف</button>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    function deleteInventory(id) {
        const deleteModal = document.getElementById('deleteModal');
        const deleteForm = document.getElementById('deleteForm');
        const cancelDelete = document.getElementById('cancelDelete');
        
        deleteForm.action = "{{ route('admin.inventory.delete', '') }}/" + id;
        deleteModal.classList.remove('hidden');
        
        cancelDelete.addEventListener('click', function() {
            deleteModal.classList.add('hidden');
        });
    }
</script>
@endsection