<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\MenuItem;
use App\Models\Order;
use App\Models\User;
use App\Models\Reservation;
use App\Models\Ingredient;
use App\Models\Table;
use App\Models\Expense;
use Illuminate\Support\Facades\Auth;
use Illuminate\Routing\Controller;

class SearchController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * البحث العام في التطبيق
     */
    public function search(Request $request)
    {
        $query = $request->input('query');
        $userType = Auth::user()->user_type;
        $searchTypes = $request->input('search_types', []);
        $dateFrom = $request->input('date_from');
        $dateTo = $request->input('date_to');
        $exactMatch = $request->input('exact_match');
        $sortBy = $request->input('sort_by', 'relevance');

        if (empty($query)) {
            return redirect()->back()->with('error', 'الرجاء إدخال كلمة للبحث');
        }

        $results = [
            'menuItems' => [],
            'orders' => [],
            'users' => [],
            'reservations' => [],
            'ingredients' => [],
            'tables' => [],
            'expenses' => [],
        ];

        // تحديد نمط البحث (تطابق تام أو جزئي)
        $searchPattern = $exactMatch ? $query : "%{$query}%";

        // تحديد الفترة الزمنية للبحث
        $dateQuery = function($query) use ($dateFrom, $dateTo) {
            if ($dateFrom) {
                $query->whereDate('created_at', '>=', $dateFrom);
            }
            if ($dateTo) {
                $query->whereDate('created_at', '<=', $dateTo);
            }
            return $query;
        };

        // البحث في قائمة الطعام
        if (empty($searchTypes) || in_array('menu', $searchTypes)) {
            $menuQuery = MenuItem::query();

            if ($exactMatch) {
                $menuQuery->where(function($q) use ($searchPattern) {
                    $q->where('name', $searchPattern)
                      ->orWhere('description', $searchPattern);
                });
            } else {
                $menuQuery->where(function($q) use ($searchPattern) {
                    $q->where('name', 'like', $searchPattern)
                      ->orWhere('description', 'like', $searchPattern);
                });
            }

            // تطبيق الفلتر الزمني
            $menuQuery = $dateQuery($menuQuery);

            // تطبيق الترتيب
            if ($sortBy == 'newest') {
                $menuQuery->orderBy('created_at', 'desc');
            } elseif ($sortBy == 'oldest') {
                $menuQuery->orderBy('created_at', 'asc');
            }

            $results['menuItems'] = $menuQuery->take(5)->get();
        }

        // البحث في الطلبات (حسب صلاحيات المستخدم)
        if (empty($searchTypes) || in_array('orders', $searchTypes)) {
            $orderQuery = Order::query();

            if ($userType == 'admin' || $userType == 'employee') {
                if ($exactMatch) {
                    $orderQuery->where(function($q) use ($searchPattern) {
                        $q->where('order_id', $searchPattern)
                          ->orWhereHas('user', function($q) use ($searchPattern) {
                              $q->where('first_name', $searchPattern)
                                ->orWhere('last_name', $searchPattern);
                          });
                    });
                } else {
                    $orderQuery->where(function($q) use ($searchPattern) {
                        $q->where('order_id', 'like', $searchPattern)
                          ->orWhereHas('user', function($q) use ($searchPattern) {
                              $q->where('first_name', 'like', $searchPattern)
                                ->orWhere('last_name', 'like', $searchPattern);
                          });
                    });
                }
            } else {
                // للعملاء، عرض طلباتهم فقط
                $orderQuery->where('user_id', Auth::id());

                if ($exactMatch) {
                    $orderQuery->where('order_id', $searchPattern);
                } else {
                    $orderQuery->where('order_id', 'like', $searchPattern);
                }
            }

            // تطبيق الفلتر الزمني
            $orderQuery = $dateQuery($orderQuery);

            // تطبيق الترتيب
            if ($sortBy == 'newest') {
                $orderQuery->orderBy('created_at', 'desc');
            } elseif ($sortBy == 'oldest') {
                $orderQuery->orderBy('created_at', 'asc');
            }

            $results['orders'] = $orderQuery->take(5)->get();
        }

        // البحث في المستخدمين (للمدير فقط)
        if (($userType == 'admin') && (empty($searchTypes) || in_array('users', $searchTypes))) {
            $userQuery = User::query();

            if ($exactMatch) {
                $userQuery->where(function($q) use ($searchPattern) {
                    $q->where('first_name', $searchPattern)
                      ->orWhere('last_name', $searchPattern)
                      ->orWhere('email', $searchPattern)
                      ->orWhere('phone', $searchPattern);
                });
            } else {
                $userQuery->where(function($q) use ($searchPattern) {
                    $q->where('first_name', 'like', $searchPattern)
                      ->orWhere('last_name', 'like', $searchPattern)
                      ->orWhere('email', 'like', $searchPattern)
                      ->orWhere('phone', 'like', $searchPattern);
                });
            }

            // تطبيق الفلتر الزمني
            $userQuery = $dateQuery($userQuery);

            // تطبيق الترتيب
            if ($sortBy == 'newest') {
                $userQuery->orderBy('created_at', 'desc');
            } elseif ($sortBy == 'oldest') {
                $userQuery->orderBy('created_at', 'asc');
            }

            $results['users'] = $userQuery->take(5)->get();
        }

        // البحث في الحجوزات (حسب صلاحيات المستخدم)
        if (empty($searchTypes) || in_array('reservations', $searchTypes)) {
            $reservationQuery = Reservation::query();

            if ($userType == 'admin' || $userType == 'employee') {
                if ($exactMatch) {
                    $reservationQuery->where(function($q) use ($searchPattern) {
                        $q->whereHas('user', function($q) use ($searchPattern) {
                              $q->where('first_name', $searchPattern)
                                ->orWhere('last_name', $searchPattern);
                          })
                          ->orWhereHas('table', function($q) use ($searchPattern) {
                              $q->where('table_number', $searchPattern);
                          });
                    });
                } else {
                    $reservationQuery->where(function($q) use ($searchPattern) {
                        $q->whereHas('user', function($q) use ($searchPattern) {
                              $q->where('first_name', 'like', $searchPattern)
                                ->orWhere('last_name', 'like', $searchPattern);
                          })
                          ->orWhereHas('table', function($q) use ($searchPattern) {
                              $q->where('table_number', 'like', $searchPattern);
                          });
                    });
                }
            } else {
                // للعملاء، عرض حجوزاتهم فقط
                $reservationQuery->where('user_id', Auth::id());

                if ($exactMatch) {
                    $reservationQuery->whereHas('table', function($q) use ($searchPattern) {
                        $q->where('table_number', $searchPattern);
                    });
                } else {
                    $reservationQuery->whereHas('table', function($q) use ($searchPattern) {
                        $q->where('table_number', 'like', $searchPattern);
                    });
                }
            }

            // تطبيق الفلتر الزمني
            $reservationQuery = $dateQuery($reservationQuery);

            // تطبيق الترتيب
            if ($sortBy == 'newest') {
                $reservationQuery->orderBy('created_at', 'desc');
            } elseif ($sortBy == 'oldest') {
                $reservationQuery->orderBy('created_at', 'asc');
            }

            $results['reservations'] = $reservationQuery->take(5)->get();
        }

        // البحث في المكونات (للمدير والموظف فقط)
        if (($userType == 'admin' || $userType == 'employee') && (empty($searchTypes) || in_array('ingredients', $searchTypes))) {
            $ingredientQuery = Ingredient::query();

            if ($exactMatch) {
                $ingredientQuery->where('name', $searchPattern);
            } else {
                $ingredientQuery->where('name', 'like', $searchPattern);
            }

            // تطبيق الفلتر الزمني
            $ingredientQuery = $dateQuery($ingredientQuery);

            // تطبيق الترتيب
            if ($sortBy == 'newest') {
                $ingredientQuery->orderBy('created_at', 'desc');
            } elseif ($sortBy == 'oldest') {
                $ingredientQuery->orderBy('created_at', 'asc');
            }

            $results['ingredients'] = $ingredientQuery->take(5)->get();
        }

        // البحث في الطاولات (للمدير والموظف فقط)
        if (($userType == 'admin' || $userType == 'employee') && (empty($searchTypes) || in_array('tables', $searchTypes))) {
            $tableQuery = Table::query();

            if ($exactMatch) {
                $tableQuery->where('table_number', $searchPattern);
            } else {
                $tableQuery->where('table_number', 'like', $searchPattern);
            }

            // تطبيق الفلتر الزمني
            $tableQuery = $dateQuery($tableQuery);

            // تطبيق الترتيب
            if ($sortBy == 'newest') {
                $tableQuery->orderBy('created_at', 'desc');
            } elseif ($sortBy == 'oldest') {
                $tableQuery->orderBy('created_at', 'asc');
            }

            $results['tables'] = $tableQuery->take(5)->get();
        }

        // البحث في المصروفات (للمدير فقط)
        if (($userType == 'admin') && (empty($searchTypes) || in_array('expenses', $searchTypes))) {
            $expenseQuery = Expense::query();

            if ($exactMatch) {
                $expenseQuery->where(function($q) use ($searchPattern) {
                    $q->where('description', $searchPattern)
                      ->orWhere('category', $searchPattern);
                });
            } else {
                $expenseQuery->where(function($q) use ($searchPattern) {
                    $q->where('description', 'like', $searchPattern)
                      ->orWhere('category', 'like', $searchPattern);
                });
            }

            // تطبيق الفلتر الزمني
            $expenseQuery = $dateQuery($expenseQuery);

            // تطبيق الترتيب
            if ($sortBy == 'newest') {
                $expenseQuery->orderBy('created_at', 'desc');
            } elseif ($sortBy == 'oldest') {
                $expenseQuery->orderBy('created_at', 'asc');
            }

            $results['expenses'] = $expenseQuery->take(5)->get();
        }

        // تحديد القالب المناسب حسب نوع المستخدم
        $view = 'search.results';
        if ($userType == 'admin') {
            $view = 'admin.search.results';
        } elseif ($userType == 'employee') {
            $view = 'employee.search.results';
        } elseif ($userType == 'customer') {
            $view = 'customer.search.results';
        }

        return view($view, [
            'query' => $query,
            'results' => $results
        ]);
    }
}
