# دليل نظام إدارة الصلاحيات

## نظرة عامة
تم إنشاء نظام صلاحيات متكامل باستخدام مكتبة Spatie Permission يسمح بإدارة صلاحيات المستخدمين بمرونة عالية.

## المكونات الرئيسية

### 1. الصلاحيات (Permissions)
تم إنشاء صلاحيات شاملة تغطي جميع أجزاء النظام:

#### إدارة المستخدمين
- `users.view` - عرض المستخدمين
- `users.create` - إضافة مستخدمين
- `users.edit` - تعديل المستخدمين
- `users.delete` - حذف المستخدمين
- `users.permissions` - إدارة صلاحيات المستخدمين

#### إدارة القائمة
- `menu.view` - عرض القائمة
- `menu.create` - إضافة عناصر القائمة
- `menu.edit` - تعديل عناصر القائمة
- `menu.delete` - حذف عناصر القائمة

#### إدارة الطلبات
- `orders.view` - عرض الطلبات
- `orders.create` - إنشاء طلبات
- `orders.edit` - تعديل الطلبات
- `orders.delete` - حذف الطلبات
- `orders.status` - تغيير حالة الطلبات

#### إدارة الحجوزات
- `reservations.view` - عرض الحجوزات
- `reservations.create` - إنشاء حجوزات
- `reservations.edit` - تعديل الحجوزات
- `reservations.delete` - حذف الحجوزات
- `reservations.status` - تغيير حالة الحجوزات

#### إدارة المخزون
- `inventory.view` - عرض المخزون
- `inventory.create` - إضافة مخزون
- `inventory.edit` - تعديل المخزون
- `inventory.delete` - حذف المخزون
- `inventory.export` - تصدير المخزون

#### التقارير
- `reports.view` - عرض التقارير
- `reports.financial` - التقارير المالية
- `reports.sales` - تقارير المبيعات
- `reports.inventory` - تقارير المخزون
- `reports.export` - تصدير التقارير

### 2. الأدوار (Roles)
تم إنشاء ثلاثة أدوار أساسية:

#### Admin (مدير)
- يملك جميع الصلاحيات
- يمكنه إدارة صلاحيات المستخدمين الآخرين

#### Manager (مدير مساعد)
- صلاحيات محدودة للإدارة
- لا يمكنه حذف المستخدمين أو إدارة الصلاحيات

#### Employee (موظف)
- صلاحيات أساسية للعمل اليومي
- يمكنه إدارة الطلبات والحجوزات فقط

## كيفية الاستخدام

### 1. في الـ Controllers
```php
public function __construct()
{
    $this->middleware('permission:users.view');
}

// أو للتحقق من صلاحية معينة
public function index()
{
    if (!auth()->user()->can('users.view')) {
        abort(403, 'ليس لديك صلاحية للوصول');
    }
    // باقي الكود...
}
```

### 2. في الـ Routes
```php
Route::middleware('permission:users.view')->group(function () {
    Route::get('/users', [UserController::class, 'index']);
});
```

### 3. في الـ Blade Templates
```blade
@can('users.create')
    <a href="{{ route('admin.users.create') }}">إضافة مستخدم</a>
@endcan

@cannot('users.delete')
    <p>ليس لديك صلاحية للحذف</p>
@endcannot
```

### 4. التحقق من الأدوار
```php
// في الكود
if ($user->hasRole('admin')) {
    // كود خاص بالمدير
}

// في Blade
@role('admin')
    <p>مرحباً أيها المدير</p>
@endrole
```

## إدارة الصلاحيات

### 1. الوصول لصفحة الإدارة
- انتقل إلى `/admin/permissions`
- يتطلب صلاحية `users.permissions`

### 2. تعديل صلاحيات مستخدم
1. اختر المستخدم من القائمة
2. اضغط "تعديل الصلاحيات"
3. حدد الأدوار والصلاحيات المطلوبة
4. احفظ التغييرات

### 3. إدارة الأدوار
1. انتقل إلى صفحة "إدارة الأدوار"
2. يمكنك إنشاء أدوار جديدة
3. تعديل صلاحيات الأدوار الموجودة
4. حذف الأدوار غير المستخدمة

### 4. نسخ الصلاحيات
- يمكنك نسخ صلاحيات مستخدم إلى آخر
- مفيد عند إضافة موظفين جدد بنفس المهام

## أمثلة عملية

### إنشاء موظف جديد بصلاحيات محددة
```php
$user = User::create([...]);
$user->assignRole('employee');
$user->givePermissionTo(['orders.view', 'orders.create']);
```

### التحقق من صلاحية في Controller
```php
public function destroy($id)
{
    $this->authorize('users.delete');
    // كود الحذف...
}
```

### إخفاء أزرار حسب الصلاحيات
```blade
<div class="actions">
    @can('users.edit')
        <a href="{{ route('admin.users.edit', $user) }}">تعديل</a>
    @endcan
    
    @can('users.delete')
        <button onclick="deleteUser({{ $user->id }})">حذف</button>
    @endcan
</div>
```

## نصائح مهمة

1. **الأمان**: المديرون (user_type = 'admin') يملكون صلاحيات كاملة تلقائياً
2. **المرونة**: يمكن إعطاء صلاحيات مباشرة للمستخدمين بدون أدوار
3. **التنظيم**: استخدم الأدوار للمجموعات والصلاحيات المباشرة للحالات الخاصة
4. **الاختبار**: اختبر الصلاحيات دائماً قبل النشر

## الملفات المهمة

- `app/Http/Controllers/PermissionController.php` - كونترولر إدارة الصلاحيات
- `app/Http/Middleware/PermissionMiddleware.php` - middleware التحقق من الصلاحيات
- `database/seeders/PermissionSeeder.php` - إنشاء الصلاحيات الأساسية
- `resources/views/admin/permissions/` - صفحات إدارة الصلاحيات

## الأوامر المفيدة

```bash
# تشغيل seeder الصلاحيات
php artisan db:seed --class=PermissionSeeder

# مسح cache الصلاحيات
php artisan permission:cache-reset

# عرض جميع الصلاحيات
php artisan permission:show
```
