@extends('employee.layouts.app')

@section('content')
<div id="menu-page" class="page">
    <!-- هيدر الصفحة المبدع -->
    <div class="mb-8 p-6 bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-xl border border-red-200 dark:border-red-800">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h2 class="text-3xl font-bold text-gradient-luxury mb-2">قائمة الطعام</h2>
                <p class="text-red-600 dark:text-red-400 flex items-center">
                    <i class="fas fa-utensils mr-2"></i>
                    استعراض وإدارة قائمة الطعام والمشروبات
                </p>
                <div class="w-20 h-1 bg-gradient-to-r from-red-500 to-pink-500 rounded-full mt-2"></div>
            </div>
            <div class="mt-4 md:mt-0 flex items-center space-x-4 space-x-reverse">
                <div class="p-4 rounded-xl bg-gradient-to-r from-red-500 to-pink-500 text-white shadow-lg">
                    <i class="fas fa-utensils text-3xl"></i>
                </div>
                <div class="relative">
                    <input type="text" placeholder="بحث عن منتج..." class="w-64 px-4 py-3 pr-12 rounded-xl border-2 border-red-200 dark:border-red-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-base transition-all duration-300 shadow-sm focus:shadow-lg">
                    <button class="absolute left-3 top-1/2 transform -translate-y-1/2 p-1 rounded-lg text-red-500 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/30 transition-all duration-300">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- أزرار الفئات المبدعة -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-6 mb-8 border border-gray-200 dark:border-gray-700">
        <div class="flex items-center mb-4">
            <div class="p-2 rounded-lg bg-gradient-to-r from-red-500 to-pink-500 text-white mr-3">
                <i class="fas fa-filter"></i>
            </div>
            <div>
                <h3 class="text-lg font-bold text-gradient-luxury">فئات القائمة</h3>
                <p class="text-sm text-red-600 dark:text-red-400">اختر الفئة لعرض المنتجات</p>
            </div>
        </div>
        <div class="flex flex-wrap gap-3">
            <button class="category-btn active btn-magical px-6 py-3 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 group" data-category="all">
                <i class="fas fa-th-large mr-2 group-hover:scale-110 transition-transform duration-300"></i>
                الكل
            </button>
            <button class="category-btn btn-magical px-6 py-3 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 text-gray-700 dark:text-gray-300 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 group" data-category="main">
                <i class="fas fa-utensils mr-2 group-hover:scale-110 transition-transform duration-300"></i>
                الأطباق الرئيسية
            </button>
            <button class="category-btn btn-magical px-6 py-3 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 text-gray-700 dark:text-gray-300 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 group" data-category="appetizers">
                <i class="fas fa-cheese mr-2 group-hover:scale-110 transition-transform duration-300"></i>
                المقبلات
            </button>
            <button class="category-btn btn-magical px-6 py-3 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 text-gray-700 dark:text-gray-300 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 group" data-category="desserts">
                <i class="fas fa-ice-cream mr-2 group-hover:scale-110 transition-transform duration-300"></i>
                الحلويات
            </button>
            <button class="category-btn btn-magical px-6 py-3 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-600 text-gray-700 dark:text-gray-300 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 group" data-category="drinks">
                <i class="fas fa-coffee mr-2 group-hover:scale-110 transition-transform duration-300"></i>
                المشروبات
            </button>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- بطاقات المنتجات هنا -->
        @forelse($menuItems as $item)
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300">
            @if($item->image)
            <div class="h-48 overflow-hidden">
                <img src="{{ asset('storage/' . $item->image) }}" alt="{{ $item->name }}" class="w-full h-full object-cover">
            </div>
            @else
            <div class="h-48 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                <i class="fas fa-utensils text-4xl text-gray-400 dark:text-gray-500"></i>
            </div>
            @endif
            <div class="p-4">
                <div class="flex justify-between items-start mb-2">
                    <h3 class="text-lg font-semibold text-gray-800 dark:text-white">{{ $item->name }}</h3>
                    <span class="bg-primary/10 text-primary text-sm font-bold px-2 py-1 rounded">{{ $item->category }}</span>
                </div>
                <p class="text-sm text-gray-500 dark:text-gray-400 mb-3">{{ Str::limit($item->description, 100) }}</p>
                <div class="flex justify-between items-center">
                    <span class="text-lg font-bold text-primary">{{ number_format($item->price, 2) }} د.ل</span>
                    <button type="button" class="add-to-order-btn bg-primary hover:bg-primary/90 text-white py-2 px-4 rounded-md flex items-center justify-center transition-all"
                        data-id="{{ $item->item_id }}"
                        data-name="{{ $item->name }}"
                        data-price="{{ $item->price }}">
                        <i class="fas fa-plus ml-1"></i> إضافة للطلب
                    </button>
                </div>
            </div>
        </div>
        @empty
        <div class="col-span-3 py-8 text-center">
            <i class="fas fa-search text-gray-400 text-5xl mb-4"></i>
            <h3 class="text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">لم يتم العثور على أي منتجات</h3>
            <p class="text-gray-500 dark:text-gray-400">جرب البحث بكلمات مختلفة أو تصفح جميع الفئات</p>
        </div>
        @endforelse
    </div>

    <!-- ترقيم الصفحات -->
    <div class="mt-6 flex justify-center">
        <nav class="flex items-center space-x-1 space-x-reverse">
            <button class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 disabled:opacity-50">
                السابق
            </button>
            <button class="px-3 py-1 rounded-md bg-primary text-white hover:bg-primary/90">
                1
            </button>
            <button class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600">
                2
            </button>
            <button class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600">
                3
            </button>
            <button class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600">
                التالي
            </button>
        </nav>
    </div>


</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // إضافة مستمعي الأحداث لأزرار إضافة العناصر
        var addButtons = document.querySelectorAll('.add-to-order-btn');
        addButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                var itemId = this.getAttribute('data-id');
                var itemName = this.getAttribute('data-name');
                var itemPrice = this.getAttribute('data-price');

                // إضافة العنصر مباشرة إلى الطلب
                addItemToOrder(itemId, itemName, itemPrice);
            });
        });

        // إضافة مستمعي الأحداث لأزرار التصنيف
        var categoryButtons = document.querySelectorAll('.category-btn');
        categoryButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                // إزالة الفئة النشطة من جميع الأزرار
                categoryButtons.forEach(function(btn) {
                    btn.classList.remove('active', 'bg-primary', 'text-white');
                    btn.classList.add('bg-gray-200', 'dark:bg-gray-700', 'text-darkText', 'dark:text-white');
                });

                // إضافة الفئة النشطة للزر المحدد
                this.classList.remove('bg-gray-200', 'dark:bg-gray-700', 'text-darkText', 'dark:text-white');
                this.classList.add('active', 'bg-primary', 'text-white');

                // الحصول على فئة المنتجات المحددة
                var category = this.getAttribute('data-category');

                // تصفية المنتجات حسب الفئة
                filterMenuItems(category);
            });
        });

        // إضافة مستمع الحدث لحقل البحث
        var searchInput = document.querySelector('input[placeholder="بحث عن منتج..."]');
        searchInput.addEventListener('input', function() {
            var searchTerm = this.value.trim().toLowerCase();
            searchMenuItems(searchTerm);
        });
    });

    // دالة إضافة عنصر للطلب
    function addItemToOrder(itemId, itemName, itemPrice) {
        // إضافة العنصر مباشرة إلى الطلب بكمية 1 بدون ملاحظات
        addToCart(itemId, itemName, itemPrice, 1, '');

        // إظهار رسالة تأكيد
        showSuccessMessage('تمت إضافة ' + itemName + ' إلى الطلب');
    }

    // دالة إضافة العنصر إلى سلة التسوق
    function addToCart(itemId, itemName, itemPrice, quantity, notes) {
        console.log('إضافة العنصر إلى السلة:', itemId, itemName, itemPrice, quantity, notes);

        // استرجاع عناصر السلة الحالية من التخزين المحلي
        let cartItems = JSON.parse(localStorage.getItem('cartItems')) || [];

        // التحقق مما إذا كان العنصر موجودًا بالفعل في السلة
        const existingItemIndex = cartItems.findIndex(item => item.id === itemId);

        if (existingItemIndex !== -1) {
            // تحديث الكمية إذا كان العنصر موجودًا بالفعل
            cartItems[existingItemIndex].quantity += quantity;
            // تحديث الملاحظات إذا تم إدخالها
            if (notes) {
                cartItems[existingItemIndex].notes = notes;
            }
        } else {
            // إضافة عنصر جديد إلى السلة
            cartItems.push({
                id: itemId,
                name: itemName,
                price: parseFloat(itemPrice),
                quantity: quantity,
                notes: notes
            });
        }

        // حفظ السلة المحدثة في التخزين المحلي
        localStorage.setItem('cartItems', JSON.stringify(cartItems));

        // تحديث عدد العناصر في السلة
        const cartCountElements = document.querySelectorAll('.cart-count');
        cartCountElements.forEach(element => {
            element.textContent = cartItems.length;
        });

        // إظهار رسالة نجاح
        showSuccessMessage('تمت إضافة ' + itemName + ' إلى الطلب');
    }

    // دالة إظهار رسالة نجاح
    function showSuccessMessage(message) {
        // إنشاء عنصر الرسالة
        var messageElement = document.createElement('div');
        messageElement.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-primary/10 border border-primary text-primary px-6 py-4 rounded-md shadow-lg z-50 flex items-center';
        messageElement.style.minWidth = '300px';
        messageElement.style.textAlign = 'center';
        messageElement.style.justifyContent = 'center';
        messageElement.innerHTML = '<i class="fas fa-check-circle ml-2 text-primary text-xl"></i><span class="font-bold">' + message + '</span>';

        // إضافة العنصر إلى الصفحة
        document.body.appendChild(messageElement);

        // إضافة تأثير ظهور
        setTimeout(function() {
            messageElement.classList.add('animate-bounce');
        }, 100);

        // إخفاء الرسالة بعد 2 ثوان
        setTimeout(function() {
            messageElement.classList.add('opacity-0', 'transition-opacity', 'duration-500');
            setTimeout(function() {
                document.body.removeChild(messageElement);
            }, 500);
        }, 2000);
    }

    // دالة إظهار رسالة خطأ
    function showErrorMessage(message) {
        // إنشاء عنصر الرسالة
        var messageElement = document.createElement('div');
        messageElement.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-red-100 border border-red-400 text-red-700 px-6 py-4 rounded-md shadow-lg z-50 flex items-center';
        messageElement.style.minWidth = '300px';
        messageElement.style.textAlign = 'center';
        messageElement.style.justifyContent = 'center';
        messageElement.innerHTML = '<i class="fas fa-exclamation-circle ml-2 text-red-500 text-xl"></i><span class="font-bold">' + message + '</span>';

        // إضافة العنصر إلى الصفحة
        document.body.appendChild(messageElement);

        // إضافة تأثير ظهور
        setTimeout(function() {
            messageElement.classList.add('animate-bounce');
        }, 100);

        // إخفاء الرسالة بعد 2 ثوان
        setTimeout(function() {
            messageElement.classList.add('opacity-0', 'transition-opacity', 'duration-500');
            setTimeout(function() {
                document.body.removeChild(messageElement);
            }, 500);
        }, 2000);
    }

    // دالة تصفية المنتجات حسب الفئة
    function filterMenuItems(category) {
        var menuItems = document.querySelectorAll('.grid > div:not(.col-span-3)');

        menuItems.forEach(function(item) {
            var itemCategory = item.querySelector('.bg-primary\\/10')?.textContent.trim().toLowerCase();

            if (category === 'all' || (itemCategory && itemCategory === category)) {
                item.classList.remove('hidden');
            } else {
                item.classList.add('hidden');
            }
        });

        // التحقق مما إذا كانت جميع العناصر مخفية
        var visibleItems = document.querySelectorAll('.grid > div:not(.hidden):not(.col-span-3)');
        var noResultsMessage = document.querySelector('.grid > .col-span-3');

        if (visibleItems.length === 0) {
            if (!noResultsMessage) {
                var grid = document.querySelector('.grid');
                noResultsMessage = document.createElement('div');
                noResultsMessage.className = 'col-span-3 py-8 text-center';
                noResultsMessage.innerHTML = `
                    <i class="fas fa-search text-gray-400 text-5xl mb-4"></i>
                    <h3 class="text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">لم يتم العثور على أي منتجات</h3>
                    <p class="text-gray-500 dark:text-gray-400">جرب البحث بكلمات مختلفة أو تصفح جميع الفئات</p>
                `;
                grid.appendChild(noResultsMessage);
            } else {
                noResultsMessage.classList.remove('hidden');
            }
        } else if (noResultsMessage) {
            noResultsMessage.classList.add('hidden');
        }
    }

    // دالة البحث عن المنتجات
    function searchMenuItems(searchTerm) {
        var menuItems = document.querySelectorAll('.grid > div:not(.col-span-3)');

        menuItems.forEach(function(item) {
            var itemName = item.querySelector('h3').textContent.trim().toLowerCase();
            var itemDescription = item.querySelector('p').textContent.trim().toLowerCase();

            if (itemName.includes(searchTerm) || itemDescription.includes(searchTerm)) {
                item.classList.remove('hidden');
            } else {
                item.classList.add('hidden');
            }
        });

        // التحقق مما إذا كانت جميع العناصر مخفية
        var visibleItems = document.querySelectorAll('.grid > div:not(.hidden):not(.col-span-3)');
        var noResultsMessage = document.querySelector('.grid > .col-span-3');

        if (visibleItems.length === 0) {
            if (!noResultsMessage) {
                var grid = document.querySelector('.grid');
                noResultsMessage = document.createElement('div');
                noResultsMessage.className = 'col-span-3 py-8 text-center';
                noResultsMessage.innerHTML = `
                    <i class="fas fa-search text-gray-400 text-5xl mb-4"></i>
                    <h3 class="text-xl font-bold text-gray-700 dark:text-gray-300 mb-2">لم يتم العثور على أي منتجات</h3>
                    <p class="text-gray-500 dark:text-gray-400">جرب البحث بكلمات مختلفة أو تصفح جميع الفئات</p>
                `;
                grid.appendChild(noResultsMessage);
            } else {
                noResultsMessage.classList.remove('hidden');
            }
        } else if (noResultsMessage) {
            noResultsMessage.classList.add('hidden');
        }
    }
</script>
@endsection