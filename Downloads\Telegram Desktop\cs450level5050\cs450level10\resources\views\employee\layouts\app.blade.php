<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Eat Hub - نظام إدارة المطعم للموظفين</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        // نظام ألوان متطور ومبدع للمطعم
                        primary: {
                            50: '#FFF7ED',
                            100: '#FFEDD5',
                            200: '#FED7AA',
                            300: '#FDBA74',
                            400: '#FB923C',
                            500: '#F97316',  // البرتقالي الأساسي
                            600: '#EA580C',
                            700: '#C2410C',
                            800: '#9A3412',
                            900: '#7C2D12',
                        },
                        secondary: {
                            50: '#F0FDF4',
                            100: '#DCFCE7',
                            200: '#BBF7D0',
                            300: '#86EFAC',
                            400: '#4ADE80',
                            500: '#22C55E',  // الأخضر الأساسي
                            600: '#16A34A',
                            700: '#15803D',
                            800: '#166534',
                            900: '#14532D',
                        },
                        accent: {
                            50: '#FEFCE8',
                            100: '#FEF9C3',
                            200: '#FEF08A',
                            300: '#FDE047',
                            400: '#FACC15',
                            500: '#EAB308',  // الأصفر الأساسي
                            600: '#CA8A04',
                            700: '#A16207',
                            800: '#854D0E',
                            900: '#713F12',
                        },
                        royal: {
                            50: '#F8FAFC',
                            100: '#F1F5F9',
                            200: '#E2E8F0',
                            300: '#CBD5E1',
                            400: '#94A3B8',
                            500: '#64748B',  // الرمادي الملكي
                            600: '#475569',
                            700: '#334155',
                            800: '#1E293B',
                            900: '#0F172A',
                        },
                        luxury: {
                            50: '#FDF4FF',
                            100: '#FAE8FF',
                            200: '#F5D0FE',
                            300: '#F0ABFC',
                            400: '#E879F9',
                            500: '#D946EF',  // البنفسجي الفاخر
                            600: '#C026D3',
                            700: '#A21CAF',
                            800: '#86198F',
                            900: '#701A75',
                        },
                        ocean: {
                            50: '#F0F9FF',
                            100: '#E0F2FE',
                            200: '#BAE6FD',
                            300: '#7DD3FC',
                            400: '#38BDF8',
                            500: '#0EA5E9',  // الأزرق المحيطي
                            600: '#0284C7',
                            700: '#0369A1',
                            800: '#075985',
                            900: '#0C4A6E',
                        },
                        sunset: {
                            50: '#FEF2F2',
                            100: '#FEE2E2',
                            200: '#FECACA',
                            300: '#FCA5A5',
                            400: '#F87171',
                            500: '#EF4444',  // الأحمر الغروبي
                            600: '#DC2626',
                            700: '#B91C1C',
                            800: '#991B1B',
                            900: '#7F1D1D',
                        },
                        forest: {
                            50: '#F7FDF7',
                            100: '#ECFDF5',
                            200: '#D1FAE5',
                            300: '#A7F3D0',
                            400: '#6EE7B7',
                            500: '#34D399',  // الأخضر الغابي
                            600: '#059669',
                            700: '#047857',
                            800: '#065F46',
                            900: '#064E3B',
                        },
                        gold: {
                            50: '#FFFBEB',
                            100: '#FEF3C7',
                            200: '#FDE68A',
                            300: '#FCD34D',
                            400: '#FBBF24',
                            500: '#F59E0B',  // الذهبي
                            600: '#D97706',
                            700: '#B45309',
                            800: '#92400E',
                            900: '#78350F',
                        },
                        // ألوان خاصة للمطعم
                        spice: '#D2691E',        // لون التوابل
                        cream: '#F5F5DC',        // لون الكريمة
                        coffee: '#6F4E37',       // لون القهوة
                        mint: '#98FB98',         // لون النعناع
                        tomato: '#FF6347',       // لون الطماطم
                        olive: '#808000',        // لون الزيتون
                        wine: '#722F37',         // لون النبيذ
                        honey: '#FFB347',        // لون العسل
                    },
                    fontFamily: {
                        sans: ['Cairo', 'sans-serif'],
                    },
                }
            }
        };

        // ضمان ثبات الثيم - حل قوي ومضمون
        (function() {
            // تحديد الثيم الافتراضي
            let savedTheme = localStorage.getItem('theme_preference');

            // إذا لم يكن هناك ثيم محفوظ، استخدم light كافتراضي
            if (!savedTheme) {
                savedTheme = 'light';
                localStorage.setItem('theme_preference', savedTheme);
            }

            // تحديد الثيم الفعال
            let themeToApply = savedTheme;
            if (savedTheme === 'auto') {
                themeToApply = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
            }

            // تطبيق الثيم فوراً على HTML
            const html = document.documentElement;
            html.className = ''; // مسح جميع الكلاسات
            html.classList.add(themeToApply);
            html.setAttribute('data-theme', themeToApply);
            html.style.setProperty('--theme-applied', themeToApply);

            // حفظ الثيم في localStorage
            localStorage.setItem('theme_preference', savedTheme);
            localStorage.setItem('effective_theme', themeToApply);
            localStorage.setItem('theme_timestamp', Date.now().toString());

            // إضافة CSS مباشر لضمان التطبيق
            const style = document.createElement('style');
            style.id = 'theme-enforcer';
            style.textContent = `
                html.light { color-scheme: light; }
                html.dark { color-scheme: dark; }
                html:not(.light):not(.dark) { color-scheme: light; }
                body { transition: background-color 0.3s ease, color 0.3s ease; }
            `;
            document.head.appendChild(style);

            // مراقبة مستمرة لضمان عدم تغيير الثيم
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        const currentTheme = localStorage.getItem('effective_theme');
                        if (currentTheme && !html.classList.contains(currentTheme)) {
                            html.className = '';
                            html.classList.add(currentTheme);
                        }
                    }
                });
            });

            observer.observe(html, {
                attributes: true,
                attributeFilter: ['class']
            });
        })();
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/apexcharts@3.27.3/dist/apexcharts.min.css" rel="stylesheet">
    <link href="{{ asset('css/theme.css') }}" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap');

        body {
            font-family: 'Cairo', sans-serif;
        }

        /* تنسيق السكرولبار */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .dark ::-webkit-scrollbar-track {
            background: #2d3748;
        }

        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #FF6B35;
        }

        /* تحريك القائمة الجانبية بتأثير سلس */
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }

        /* تأثيرات الحركة */
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* تنسيق البطاقات */
        .card-hover:hover {
            transform: translateY(-5px);
        }

        /* إصلاح الألوان الأساسية */
        .bg-primary {
            background-color: #3b82f6 !important;
        }

        .text-primary {
            color: #3b82f6 !important;
        }

        .border-primary {
            border-color: #3b82f6 !important;
        }

        .hover\:bg-primary:hover {
            background-color: #2563eb !important;
        }

        .hover\:bg-primary-dark:hover {
            background-color: #1d4ed8 !important;
        }

        /* الألوان المتدرجة */
        .gradient-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        }

        .gradient-ocean {
            background: linear-gradient(135deg, #2196F3 0%, #21CBF3 100%) !important;
        }

        .gradient-forest {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%) !important;
        }

        .gradient-sunset {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
        }

        .gradient-luxury {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        }

        /* نصوص متدرجة */
        .text-gradient-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .text-gradient-ocean {
            background: linear-gradient(135deg, #2196F3 0%, #21CBF3 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .text-gradient-luxury {
            background: linear-gradient(135deg, #e91e63 0%, #f06292 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            transition: transform 0.3s ease;
        }

        /* تنسيق زر تحديث الحالة */
        .status-btn {
            transition: all 0.3s ease;
        }

        .status-btn:hover {
            transform: translateY(-2px);
        }

        /* خاص بلوحة الموظف - الطاولات */
        .table-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 1rem;
        }

        .restaurant-table {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            position: relative;
        }

        .restaurant-table:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .table-available {
            background-color: rgba(74, 222, 128, 0.2);
            border: 2px solid #4ade80;
        }

        .table-occupied {
            background-color: rgba(248, 113, 113, 0.2);
            border: 2px solid #f87171;
        }

        .table-reserved {
            background-color: rgba(251, 191, 36, 0.2);
            border: 2px solid #fbbf24;
        }

        .table-number {
            font-size: 1.5rem;
            font-weight: 700;
        }

        .table-status {
            font-size: 0.875rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            margin-top: 0.5rem;
        }

        /* نظام ألوان متطور ومبدع */

        /* تدرجات لونية جميلة للبطاقات */
        .gradient-primary {
            background: linear-gradient(135deg, #F97316 0%, #EA580C 50%, #DC2626 100%);
        }

        .gradient-secondary {
            background: linear-gradient(135deg, #22C55E 0%, #16A34A 50%, #059669 100%);
        }

        .gradient-luxury {
            background: linear-gradient(135deg, #D946EF 0%, #C026D3 50%, #A21CAF 100%);
        }

        .gradient-ocean {
            background: linear-gradient(135deg, #0EA5E9 0%, #0284C7 50%, #0369A1 100%);
        }

        .gradient-sunset {
            background: linear-gradient(135deg, #F59E0B 0%, #D97706 50%, #B45309 100%);
        }

        .gradient-forest {
            background: linear-gradient(135deg, #34D399 0%, #059669 50%, #047857 100%);
        }

        /* تأثيرات الظلال الملونة */
        .shadow-primary {
            box-shadow: 0 10px 25px -5px rgba(249, 115, 22, 0.3), 0 4px 6px -2px rgba(249, 115, 22, 0.1);
        }

        .shadow-secondary {
            box-shadow: 0 10px 25px -5px rgba(34, 197, 94, 0.3), 0 4px 6px -2px rgba(34, 197, 94, 0.1);
        }

        .shadow-luxury {
            box-shadow: 0 10px 25px -5px rgba(217, 70, 239, 0.3), 0 4px 6px -2px rgba(217, 70, 239, 0.1);
        }

        .shadow-ocean {
            box-shadow: 0 10px 25px -5px rgba(14, 165, 233, 0.3), 0 4px 6px -2px rgba(14, 165, 233, 0.1);
        }

        /* تأثيرات الحركة المتطورة */
        .card-float {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .card-float:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.2);
        }

        /* تأثيرات الخلفيات المتحركة */
        .animated-bg {
            background: linear-gradient(-45deg, #F97316, #22C55E, #D946EF, #0EA5E9);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* تأثيرات النيون */
        .neon-glow {
            text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
        }

        .neon-border {
            border: 2px solid;
            box-shadow: inset 0 0 10px currentColor, 0 0 10px currentColor;
        }

        /* تأثيرات الزجاج المصقول */
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .dark .glass-effect {
            background: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* تأثيرات الأيقونات المتحركة */
        .icon-bounce {
            animation: iconBounce 2s infinite;
        }

        @keyframes iconBounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .icon-pulse {
            animation: iconPulse 2s infinite;
        }

        @keyframes iconPulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        .icon-rotate {
            animation: iconRotate 3s linear infinite;
        }

        @keyframes iconRotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* تأثيرات النصوص المتدرجة */
        .text-gradient-primary {
            background: linear-gradient(135deg, #F97316, #DC2626);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .text-gradient-luxury {
            background: linear-gradient(135deg, #D946EF, #A21CAF);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .text-gradient-ocean {
            background: linear-gradient(135deg, #0EA5E9, #0369A1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* تأثيرات الحدود المتوهجة */
        .border-glow-primary {
            border: 2px solid #F97316;
            box-shadow: 0 0 20px rgba(249, 115, 22, 0.5);
        }

        .border-glow-secondary {
            border: 2px solid #22C55E;
            box-shadow: 0 0 20px rgba(34, 197, 94, 0.5);
        }

        .border-glow-luxury {
            border: 2px solid #D946EF;
            box-shadow: 0 0 20px rgba(217, 70, 239, 0.5);
        }

        /* تأثيرات الخلفيات المتدرجة للوضع المظلم */
        .dark .gradient-dark-primary {
            background: linear-gradient(135deg, #1E293B 0%, #334155 50%, #475569 100%);
        }

        .dark .gradient-dark-luxury {
            background: linear-gradient(135deg, #581C87 0%, #7C3AED 50%, #A855F7 100%);
        }

        .dark .gradient-dark-ocean {
            background: linear-gradient(135deg, #0C4A6E 0%, #075985 50%, #0369A1 100%);
        }

        /* تأثيرات الأزرار المتطورة */
        .btn-magical {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .btn-magical::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .btn-magical:hover::before {
            left: 100%;
        }

        .btn-magical:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        /* تأثيرات الإحصائيات المتحركة */
        .stat-card {
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: statRotate 4s linear infinite;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .stat-card:hover::before {
            opacity: 1;
        }

        @keyframes statRotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* تأثيرات الجداول الملونة */
        .table-row-hover {
            transition: all 0.3s ease;
        }

        .table-row-hover:hover {
            background: linear-gradient(90deg, rgba(249, 115, 22, 0.1), rgba(34, 197, 94, 0.1));
            transform: scale(1.01);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        /* تأثيرات التحميل المبدعة */
        .loading-shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
        }

        .dark .loading-shimmer {
            background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
            background-size: 200% 100%;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }
    </style>
</head>
<body class="bg-white dark:bg-gray-800 min-h-screen">
    <div class="flex h-screen overflow-hidden">
        <!-- استدعاء القائمة الجانبية -->
        @include('employee.layouts.sidebar')

        <!-- المحتوى الرئيسي -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- استدعاء الهيدر -->
            @include('employee.layouts.header')

            <!-- المحتوى الرئيسي -->
            <main class="flex-1 overflow-y-auto p-4 bg-white dark:bg-gray-800">
                @yield('content')
            </main>
        </div>
    </div>

    <!-- استدعاء ملف السكريبتات -->
    @include('employee.layouts.scripts')
</body>
</html>