<!-- القائمة الجانبية للشاشات الكبيرة - تصميم مبدع -->
<aside id="sidebar" class="hidden md:flex md:flex-col w-64 bg-gradient-to-b from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 shadow-xl sidebar-transition z-20 border-r border-gray-200 dark:border-gray-700">
    <!-- هيدر مبدع -->
    <div class="p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="relative">
                    <span class="text-2xl font-bold text-gradient-primary">Eat Hub</span>
                    <!-- تأثير التوهج -->
                    <div class="absolute -inset-1 bg-gradient-primary opacity-20 blur-sm rounded-lg"></div>
                </div>
                <span class="mr-3 px-3 py-1 bg-gradient-primary text-white text-xs rounded-full shadow-primary font-medium">الموظفين</span>
            </div>
            @if(Auth::user()->user_type == 'admin' || Auth::user()->can('dashboard.admin'))
            <a href="{{ route('admin.dashboard') }}"
               class="p-3 rounded-xl bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-600 hover:text-green-700 hover:shadow-lg transition-all duration-300 border border-green-200 dark:border-green-800"
               title="العودة لواجهة المدير">
                <i class="fas fa-user-shield text-lg"></i>
            </a>
            @endif
        </div>
    </div>

    <!-- قائمة التنقل المبدعة -->
    <div class="py-6 flex flex-col flex-1 overflow-y-auto">
        <nav class="px-4 space-y-2">
            <!-- لوحة التحكم -->
            <a href="{{ route('employee.dashboard') }}" data-page="dashboard" class="nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-orange-50 hover:to-red-50 dark:hover:from-orange-900/20 dark:hover:to-red-900/20 transition-all duration-300 hover:shadow-lg hover:scale-105">
                <div class="absolute left-0 top-0 bottom-0 w-1 bg-gradient-primary rounded-r-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="p-2 rounded-lg bg-orange-100 dark:bg-orange-900/30 group-hover:bg-gradient-primary group-hover:text-white transition-all duration-300">
                    <i class="fas fa-tachometer-alt text-orange-600 dark:text-orange-400 group-hover:text-white"></i>
                </div>
                <span class="mr-3 font-medium group-hover:text-orange-600 dark:group-hover:text-orange-400 transition-colors duration-300">لوحة التحكم</span>
            </a>

            <!-- إدارة الطلبات -->
            <a href="{{ route('employee.orders') }}" data-page="orders" class="nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/20 dark:hover:to-indigo-900/20 transition-all duration-300 hover:shadow-lg hover:scale-105">
                <div class="absolute left-0 top-0 bottom-0 w-1 bg-gradient-ocean rounded-r-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30 group-hover:bg-gradient-ocean group-hover:text-white transition-all duration-300">
                    <i class="fas fa-shopping-cart text-blue-600 dark:text-blue-400 group-hover:text-white"></i>
                </div>
                <span class="mr-3 font-medium group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">إدارة الطلبات</span>
            </a>

            <!-- إدارة الحجوزات -->
            <a href="{{ route('employee.reservations') }}" data-page="reservations" class="nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 dark:hover:from-purple-900/20 dark:hover:to-pink-900/20 transition-all duration-300 hover:shadow-lg hover:scale-105">
                <div class="absolute left-0 top-0 bottom-0 w-1 bg-gradient-luxury rounded-r-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="p-2 rounded-lg bg-purple-100 dark:bg-purple-900/30 group-hover:bg-gradient-luxury group-hover:text-white transition-all duration-300">
                    <i class="fas fa-calendar-check text-purple-600 dark:text-purple-400 group-hover:text-white"></i>
                </div>
                <span class="mr-3 font-medium group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-300">إدارة الحجوزات</span>
            </a>

            <!-- حالة الطاولات -->
            <a href="{{ route('employee.tables') }}" data-page="tables" class="nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 dark:hover:from-green-900/20 dark:hover:to-emerald-900/20 transition-all duration-300 hover:shadow-lg hover:scale-105">
                <div class="absolute left-0 top-0 bottom-0 w-1 bg-gradient-forest rounded-r-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="p-2 rounded-lg bg-green-100 dark:bg-green-900/30 group-hover:bg-gradient-forest group-hover:text-white transition-all duration-300">
                    <i class="fas fa-chair text-green-600 dark:text-green-400 group-hover:text-white"></i>
                </div>
                <span class="mr-3 font-medium group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-300">حالة الطاولات</span>
            </a>

            <!-- المدفوعات -->
            <a href="{{ route('employee.payments') }}" data-page="payments" class="nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-yellow-50 hover:to-orange-50 dark:hover:from-yellow-900/20 dark:hover:to-orange-900/20 transition-all duration-300 hover:shadow-lg hover:scale-105">
                <div class="absolute left-0 top-0 bottom-0 w-1 bg-gradient-sunset rounded-r-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="p-2 rounded-lg bg-yellow-100 dark:bg-yellow-900/30 group-hover:bg-gradient-sunset group-hover:text-white transition-all duration-300">
                    <i class="fas fa-money-bill-wave text-yellow-600 dark:text-yellow-400 group-hover:text-white"></i>
                </div>
                <span class="mr-3 font-medium group-hover:text-yellow-600 dark:group-hover:text-yellow-400 transition-colors duration-300">المدفوعات</span>
            </a>

            <!-- قائمة الطعام -->
            <a href="{{ route('employee.menu') }}" data-page="menu" class="nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 dark:hover:from-red-900/20 dark:hover:to-pink-900/20 transition-all duration-300 hover:shadow-lg hover:scale-105">
                <div class="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-red-500 to-pink-500 rounded-r-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="p-2 rounded-lg bg-red-100 dark:bg-red-900/30 group-hover:bg-gradient-to-r group-hover:from-red-500 group-hover:to-pink-500 group-hover:text-white transition-all duration-300">
                    <i class="fas fa-utensils text-red-600 dark:text-red-400 group-hover:text-white"></i>
                </div>
                <span class="mr-3 font-medium group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors duration-300">قائمة الطعام</span>
            </a>

            <!-- الإشعارات -->
            <a href="{{ route('employee.notifications') }}" data-page="notifications" class="nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-indigo-50 hover:to-blue-50 dark:hover:from-indigo-900/20 dark:hover:to-blue-900/20 transition-all duration-300 hover:shadow-lg hover:scale-105">
                <div class="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-indigo-500 to-blue-500 rounded-r-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="p-2 rounded-lg bg-indigo-100 dark:bg-indigo-900/30 group-hover:bg-gradient-to-r group-hover:from-indigo-500 group-hover:to-blue-500 group-hover:text-white transition-all duration-300">
                    <i class="fas fa-bell text-indigo-600 dark:text-indigo-400 group-hover:text-white"></i>
                </div>
                <span class="mr-3 font-medium group-hover:text-indigo-600 dark:group-hover:text-indigo-400 transition-colors duration-300">الإشعارات</span>
                <span id="notification-badge" class="mr-auto bg-gradient-primary text-white text-xs rounded-full h-6 w-6 flex items-center justify-center shadow-lg animate-pulse">3</span>
            </a>

            <!-- التقارير -->
            <a href="{{ route('employee.reports') }}" data-page="reports" class="nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-teal-50 hover:to-cyan-50 dark:hover:from-teal-900/20 dark:hover:to-cyan-900/20 transition-all duration-300 hover:shadow-lg hover:scale-105">
                <div class="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-teal-500 to-cyan-500 rounded-r-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="p-2 rounded-lg bg-teal-100 dark:bg-teal-900/30 group-hover:bg-gradient-to-r group-hover:from-teal-500 group-hover:to-cyan-500 group-hover:text-white transition-all duration-300">
                    <i class="fas fa-chart-bar text-teal-600 dark:text-teal-400 group-hover:text-white"></i>
                </div>
                <span class="mr-3 font-medium group-hover:text-teal-600 dark:group-hover:text-teal-400 transition-colors duration-300">التقارير</span>
            </a>

            <!-- المخزون -->
            <a href="{{ route('employee.inventory') }}" data-page="inventory" class="nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-amber-50 hover:to-yellow-50 dark:hover:from-amber-900/20 dark:hover:to-yellow-900/20 transition-all duration-300 hover:shadow-lg hover:scale-105">
                <div class="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-amber-500 to-yellow-500 rounded-r-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="p-2 rounded-lg bg-amber-100 dark:bg-amber-900/30 group-hover:bg-gradient-to-r group-hover:from-amber-500 group-hover:to-yellow-500 group-hover:text-white transition-all duration-300">
                    <i class="fas fa-boxes text-amber-600 dark:text-amber-400 group-hover:text-white"></i>
                </div>
                <span class="mr-3 font-medium group-hover:text-amber-600 dark:group-hover:text-amber-400 transition-colors duration-300">المخزون</span>
            </a>

            <!-- العملاء -->
            <a href="{{ route('employee.customers') }}" data-page="customers" class="nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-rose-50 hover:to-pink-50 dark:hover:from-rose-900/20 dark:hover:to-pink-900/20 transition-all duration-300 hover:shadow-lg hover:scale-105">
                <div class="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-rose-500 to-pink-500 rounded-r-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="p-2 rounded-lg bg-rose-100 dark:bg-rose-900/30 group-hover:bg-gradient-to-r group-hover:from-rose-500 group-hover:to-pink-500 group-hover:text-white transition-all duration-300">
                    <i class="fas fa-users text-rose-600 dark:text-rose-400 group-hover:text-white"></i>
                </div>
                <span class="mr-3 font-medium group-hover:text-rose-600 dark:group-hover:text-rose-400 transition-colors duration-300">العملاء</span>
            </a>

            <!-- الملف الشخصي -->
            <a href="{{ route('employee.profile') }}" data-page="profile" class="nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-violet-50 hover:to-purple-50 dark:hover:from-violet-900/20 dark:hover:to-purple-900/20 transition-all duration-300 hover:shadow-lg hover:scale-105">
                <div class="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-violet-500 to-purple-500 rounded-r-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="p-2 rounded-lg bg-violet-100 dark:bg-violet-900/30 group-hover:bg-gradient-to-r group-hover:from-violet-500 group-hover:to-purple-500 group-hover:text-white transition-all duration-300">
                    <i class="fas fa-user-circle text-violet-600 dark:text-violet-400 group-hover:text-white"></i>
                </div>
                <span class="mr-3 font-medium group-hover:text-violet-600 dark:group-hover:text-violet-400 transition-colors duration-300">الملف الشخصي</span>
            </a>

            <!-- الإعدادات -->
            <a href="{{ route('employee.settings') }}" data-page="settings" class="nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-slate-50 hover:to-gray-50 dark:hover:from-slate-900/20 dark:hover:to-gray-900/20 transition-all duration-300 hover:shadow-lg hover:scale-105">
                <div class="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-slate-500 to-gray-500 rounded-r-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div class="p-2 rounded-lg bg-slate-100 dark:bg-slate-900/30 group-hover:bg-gradient-to-r group-hover:from-slate-500 group-hover:to-gray-500 group-hover:text-white transition-all duration-300">
                    <i class="fas fa-cog text-slate-600 dark:text-slate-400 group-hover:text-white"></i>
                </div>
                <span class="mr-3 font-medium group-hover:text-slate-600 dark:group-hover:text-slate-400 transition-colors duration-300">الإعدادات</span>
            </a>

            {{-- رابط لوحة الإدارة للموظفين المخولين --}}
            @if(auth()->user()->can('dashboard.admin'))
            <div class="border-t border-gray-200 dark:border-gray-700 my-4"></div>
            <a href="{{ route('admin.dashboard') }}" class="nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl bg-gradient-to-r from-orange-50 to-yellow-50 dark:from-orange-900/20 dark:to-yellow-900/20 border-2 border-orange-200 dark:border-orange-800 hover:shadow-lg hover:scale-105 transition-all duration-300">
                <div class="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-orange-500 to-yellow-500 rounded-r-full opacity-100"></div>
                <div class="p-2 rounded-lg bg-gradient-to-r from-orange-400 to-yellow-400 text-white">
                    <i class="fas fa-crown"></i>
                </div>
                <span class="mr-3 font-bold text-orange-600 dark:text-orange-400">لوحة الإدارة</span>
                <i class="fas fa-external-link-alt text-xs text-orange-400 mr-auto group-hover:translate-x-1 transition-transform duration-300"></i>
            </a>
            @endif
        </nav>

        <!-- قسم الإحصائيات السريعة المبدع -->
        <div class="mt-auto px-4 py-4">
            <div class="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-blue-900/20 dark:via-indigo-900/20 dark:to-purple-900/20 rounded-2xl p-4 mb-4 border border-blue-200/50 dark:border-blue-700/50 shadow-lg">
                <div class="flex items-center mb-3">
                    <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg mr-2">
                        <i class="fas fa-chart-line text-white text-sm"></i>
                    </div>
                    <h3 class="text-sm font-bold text-gray-800 dark:text-white">📊 إحصائيات اليوم</h3>
                </div>

                <div class="grid grid-cols-2 gap-3">
                    <div class="bg-white/60 dark:bg-gray-800/60 rounded-xl p-3 text-center backdrop-blur-sm border border-white/20 dark:border-gray-700/20">
                        <div class="text-lg font-bold text-blue-600 dark:text-blue-400">12</div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">طلبات جديدة</div>
                    </div>
                    <div class="bg-white/60 dark:bg-gray-800/60 rounded-xl p-3 text-center backdrop-blur-sm border border-white/20 dark:border-gray-700/20">
                        <div class="text-lg font-bold text-green-600 dark:text-green-400">8</div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">طلبات مكتملة</div>
                    </div>
                    <div class="bg-white/60 dark:bg-gray-800/60 rounded-xl p-3 text-center backdrop-blur-sm border border-white/20 dark:border-gray-700/20">
                        <div class="text-lg font-bold text-purple-600 dark:text-purple-400">5</div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">حجوزات</div>
                    </div>
                    <div class="bg-white/60 dark:bg-gray-800/60 rounded-xl p-3 text-center backdrop-blur-sm border border-white/20 dark:border-gray-700/20">
                        <div class="text-lg font-bold text-orange-600 dark:text-orange-400">3</div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">إشعارات</div>
                    </div>
                </div>
            </div>

            <!-- معلومات المستخدم المبدعة -->
            <div class="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-2xl p-4 mb-4 border border-indigo-200/50 dark:border-indigo-700/50 shadow-lg">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center shadow-lg mr-3 overflow-hidden">
                        @if(Auth::user()->profile_image)
                        <img src="{{ asset('storage/' . Auth::user()->profile_image) }}" alt="{{ Auth::user()->first_name }}" class="w-full h-full object-cover">
                        @else
                        <i class="fas fa-user text-white"></i>
                        @endif
                    </div>
                    <div class="flex-1">
                        <div class="text-sm font-bold text-gray-800 dark:text-white truncate">{{ Auth::user()->first_name }} {{ Auth::user()->last_name }}</div>
                        <div class="text-xs text-gray-600 dark:text-gray-400">{{ Auth::user()->user_type == 'admin' ? '👑 مدير' : '👨‍💼 موظف' }}</div>
                        <div class="flex items-center mt-1">
                            <div class="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></div>
                            <span class="text-xs text-green-600 dark:text-green-400">متصل الآن</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قسم تسجيل الخروج المبدع -->
            <div class="border-t border-gradient-primary pt-4">
                <form action="{{ route('logout') }}" method="POST" class="w-full">
                    @csrf
                    <button type="submit" class="w-full group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 dark:hover:from-red-900/20 dark:hover:to-pink-900/20 transition-all duration-300 hover:shadow-lg hover:scale-105 text-right">
                        <div class="absolute left-0 top-0 bottom-0 w-1 bg-gradient-to-b from-red-500 to-pink-500 rounded-r-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        <div class="p-2 rounded-lg bg-red-100 dark:bg-red-900/30 group-hover:bg-gradient-to-r group-hover:from-red-500 group-hover:to-pink-500 group-hover:text-white transition-all duration-300">
                            <i class="fas fa-sign-out-alt text-red-600 dark:text-red-400 group-hover:text-white"></i>
                        </div>
                        <span class="mr-3 font-medium group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors duration-300">تسجيل الخروج</span>
                    </button>
                </form>
            </div>
        </div>
    </div>
</aside>

<!-- شريط القائمة للجوال - تصميم مبدع -->
<div id="mobileMenu" class="md:hidden bg-gradient-to-b from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 shadow-2xl sidebar-transition fixed inset-0 w-64 transform -translate-x-full z-30">
    <!-- هيدر الجوال المبدع -->
    <div class="p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 flex justify-between items-center">
        <div class="flex items-center">
            <div class="relative">
                <span class="text-2xl font-bold text-gradient-primary">Eat Hub</span>
                <!-- تأثير التوهج -->
                <div class="absolute -inset-1 bg-gradient-primary opacity-20 blur-sm rounded-lg"></div>
            </div>
            @if(Auth::user()->user_type == 'admin' || Auth::user()->can('dashboard.admin'))
            <a href="{{ route('admin.dashboard') }}"
               class="mr-3 p-2 rounded-xl bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-600 hover:text-green-700 hover:shadow-lg transition-all duration-300"
               title="العودة لواجهة المدير">
                <i class="fas fa-user-shield text-sm"></i>
            </a>
            @endif
        </div>
        <button id="closeMobileMenu" class="p-2 rounded-xl text-gray-700 dark:text-gray-300 hover:text-red-600 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 transition-all duration-300">
            <i class="fas fa-times text-xl"></i>
        </button>
    </div>

    <!-- قائمة التنقل المحمولة المبدعة -->
    <div class="py-6">
        <nav class="px-4 space-y-2">
            <!-- لوحة التحكم -->
            <a href="{{ route('employee.dashboard') }}" data-page="dashboard" class="mobile-nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-orange-50 hover:to-red-50 dark:hover:from-orange-900/20 dark:hover:to-red-900/20 transition-all duration-300">
                <div class="p-2 rounded-lg bg-orange-100 dark:bg-orange-900/30 group-hover:bg-gradient-primary group-hover:text-white transition-all duration-300">
                    <i class="fas fa-tachometer-alt text-orange-600 dark:text-orange-400 group-hover:text-white"></i>
                </div>
                <span class="mr-3 font-medium">لوحة التحكم</span>
            </a>

            <!-- إدارة الطلبات -->
            <a href="{{ route('employee.orders') }}" data-page="orders" class="mobile-nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/20 dark:hover:to-indigo-900/20 transition-all duration-300">
                <div class="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/30 group-hover:bg-gradient-ocean group-hover:text-white transition-all duration-300">
                    <i class="fas fa-shopping-cart text-blue-600 dark:text-blue-400 group-hover:text-white"></i>
                </div>
                <span class="mr-3 font-medium">إدارة الطلبات</span>
            </a>

            <!-- إدارة الحجوزات -->
            <a href="{{ route('employee.reservations') }}" data-page="reservations" class="mobile-nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 dark:hover:from-purple-900/20 dark:hover:to-pink-900/20 transition-all duration-300">
                <div class="p-2 rounded-lg bg-purple-100 dark:bg-purple-900/30 group-hover:bg-gradient-luxury group-hover:text-white transition-all duration-300">
                    <i class="fas fa-calendar-check text-purple-600 dark:text-purple-400 group-hover:text-white"></i>
                </div>
                <span class="mr-3 font-medium">إدارة الحجوزات</span>
            </a>

            <!-- باقي العناصر -->
            <a href="{{ route('employee.tables') }}" data-page="tables" class="mobile-nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 dark:hover:from-green-900/20 dark:hover:to-emerald-900/20 transition-all duration-300">
                <div class="p-2 rounded-lg bg-green-100 dark:bg-green-900/30 group-hover:bg-gradient-forest group-hover:text-white transition-all duration-300">
                    <i class="fas fa-chair text-green-600 dark:text-green-400 group-hover:text-white"></i>
                </div>
                <span class="mr-3 font-medium">حالة الطاولات</span>
            </a>

            <a href="{{ route('employee.menu') }}" data-page="menu" class="mobile-nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 dark:hover:from-red-900/20 dark:hover:to-pink-900/20 transition-all duration-300">
                <div class="p-2 rounded-lg bg-red-100 dark:bg-red-900/30 group-hover:bg-gradient-to-r group-hover:from-red-500 group-hover:to-pink-500 group-hover:text-white transition-all duration-300">
                    <i class="fas fa-utensils text-red-600 dark:text-red-400 group-hover:text-white"></i>
                </div>
                <span class="mr-3 font-medium">قائمة الطعام</span>
            </a>

            <a href="{{ route('employee.profile') }}" data-page="profile" class="mobile-nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-violet-50 hover:to-purple-50 dark:hover:from-violet-900/20 dark:hover:to-purple-900/20 transition-all duration-300">
                <div class="p-2 rounded-lg bg-violet-100 dark:bg-violet-900/30 group-hover:bg-gradient-to-r group-hover:from-violet-500 group-hover:to-purple-500 group-hover:text-white transition-all duration-300">
                    <i class="fas fa-user-circle text-violet-600 dark:text-violet-400 group-hover:text-white"></i>
                </div>
                <span class="mr-3 font-medium">الملف الشخصي</span>
            </a>
        </nav>

        <!-- تسجيل الخروج للجوال -->
        <div class="px-4 mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <form action="{{ route('logout') }}" method="POST" class="w-full">
                @csrf
                <button type="submit" class="w-full group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 dark:hover:from-red-900/20 dark:hover:to-pink-900/20 transition-all duration-300 text-right">
                    <div class="p-2 rounded-lg bg-red-100 dark:bg-red-900/30 group-hover:bg-gradient-to-r group-hover:from-red-500 group-hover:to-pink-500 group-hover:text-white transition-all duration-300">
                        <i class="fas fa-sign-out-alt text-red-600 dark:text-red-400 group-hover:text-white"></i>
                    </div>
                    <span class="mr-3 font-medium group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors duration-300">تسجيل الخروج</span>
                </button>
            </form>
        </div>
    </div>
</div>

<!-- JavaScript للتفاعل مع القائمة الجانبية -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحديث الإحصائيات كل 30 ثانية
    function updateStats() {
        // يمكن إضافة AJAX call هنا لتحديث الإحصائيات الحقيقية
        const stats = document.querySelectorAll('.sidebar-stat');
        stats.forEach(stat => {
            stat.classList.add('animate-pulse');
            setTimeout(() => {
                stat.classList.remove('animate-pulse');
            }, 1000);
        });
    }

    // تحديث الإحصائيات كل 30 ثانية
    setInterval(updateStats, 30000);

    // تأثير التمرير على عناصر القائمة
    const navLinks = document.querySelectorAll('.nav-link, .mobile-nav-link');
    navLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(5px)';
        });

        link.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });

    // تحديد الصفحة النشطة
    const currentPath = window.location.pathname;
    navLinks.forEach(link => {
        if (link.href === window.location.href) {
            link.classList.add('bg-gradient-to-r', 'from-blue-100', 'to-indigo-100', 'dark:from-blue-900/30', 'dark:to-indigo-900/30', 'border-r-4', 'border-blue-500');
            const icon = link.querySelector('i');
            if (icon) {
                icon.classList.add('text-blue-600', 'dark:text-blue-400');
            }
        }
    });
});
</script>

<style>
/* تحسينات CSS للقائمة الجانبية */
.sidebar-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-link:hover {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.mobile-nav-link:hover {
    box-shadow: 0 4px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* تأثير التوهج للعناصر النشطة */
.nav-link.active {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* تحسين الانتقالات */
* {
    transition: all 0.3s ease;
}
</style>
                <span class="mr-3 font-medium">إدارة الحجوزات</span>
            </a>

            <!-- حالة الطاولات -->
            <a href="{{ route('employee.tables') }}" data-page="tables" class="mobile-nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 dark:hover:from-green-900/20 dark:hover:to-emerald-900/20 transition-all duration-300">
                <div class="p-2 rounded-lg bg-green-100 dark:bg-green-900/30 group-hover:bg-gradient-forest group-hover:text-white transition-all duration-300">
                    <i class="fas fa-chair text-green-600 dark:text-green-400 group-hover:text-white"></i>
                </div>
                <span class="mr-3 font-medium">حالة الطاولات</span>
            </a>

            <!-- المدفوعات -->
            <a href="{{ route('employee.payments') }}" data-page="payments" class="mobile-nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-yellow-50 hover:to-orange-50 dark:hover:from-yellow-900/20 dark:hover:to-orange-900/20 transition-all duration-300">
                <div class="p-2 rounded-lg bg-yellow-100 dark:bg-yellow-900/30 group-hover:bg-gradient-sunset group-hover:text-white transition-all duration-300">
                    <i class="fas fa-money-bill-wave text-yellow-600 dark:text-yellow-400 group-hover:text-white"></i>
                </div>
                <span class="mr-3 font-medium">المدفوعات</span>
            </a>

            <!-- قائمة الطعام -->
            <a href="{{ route('employee.menu') }}" data-page="menu" class="mobile-nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 dark:hover:from-red-900/20 dark:hover:to-pink-900/20 transition-all duration-300">
                <div class="p-2 rounded-lg bg-red-100 dark:bg-red-900/30 group-hover:bg-gradient-to-r group-hover:from-red-500 group-hover:to-pink-500 group-hover:text-white transition-all duration-300">
                    <i class="fas fa-utensils text-red-600 dark:text-red-400 group-hover:text-white"></i>
                </div>
                <span class="mr-3 font-medium">قائمة الطعام</span>
            </a>

            <!-- الإشعارات -->
            <a href="{{ route('employee.notifications') }}" data-page="notifications" class="mobile-nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl hover:bg-gradient-to-r hover:from-indigo-50 hover:to-blue-50 dark:hover:from-indigo-900/20 dark:hover:to-blue-900/20 transition-all duration-300">
                <div class="p-2 rounded-lg bg-indigo-100 dark:bg-indigo-900/30 group-hover:bg-gradient-to-r group-hover:from-indigo-500 group-hover:to-blue-500 group-hover:text-white transition-all duration-300">
                    <i class="fas fa-bell text-indigo-600 dark:text-indigo-400 group-hover:text-white"></i>
                </div>
                <span class="mr-3 font-medium">الإشعارات</span>
                <span class="mr-auto bg-gradient-primary text-white text-xs rounded-full h-6 w-6 flex items-center justify-center shadow-lg animate-pulse">3</span>
            </a>

            {{-- رابط لوحة الإدارة للموظفين المخولين في القائمة المحمولة --}}
            @if(auth()->user()->can('dashboard.admin'))
            <div class="border-t border-gray-200 dark:border-gray-700 my-4"></div>
            <a href="{{ route('admin.dashboard') }}" class="mobile-nav-link group relative flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-xl bg-gradient-to-r from-orange-50 to-yellow-50 dark:from-orange-900/20 dark:to-yellow-900/20 border-2 border-orange-200 dark:border-orange-800 transition-all duration-300">
                <div class="p-2 rounded-lg bg-gradient-to-r from-orange-400 to-yellow-400 text-white">
                    <i class="fas fa-crown"></i>
                </div>
                <span class="mr-3 font-bold text-orange-600 dark:text-orange-400">لوحة الإدارة</span>
                <i class="fas fa-external-link-alt text-xs text-orange-400 mr-auto"></i>
            </a>
            @endif
        </nav>
    </div>
</div>