@extends('employee.layouts.app')

@section('title', 'لوحة تحكم الموظف')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 py-8">
    <!-- عناصر زخرفية متحركة -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
        <div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-indigo-600/20 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
        <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-purple-400/20 to-pink-600/20 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <!-- ترحيب مبدع -->
        <div class="text-center mb-12">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full shadow-2xl mb-6 animate-pulse">
                <i class="fas fa-user-tie text-3xl text-white"></i>
            </div>
            <h1 class="text-4xl md:text-5xl font-bold text-gray-800 dark:text-white mb-4">
                مرحباً {{ auth()->user()->first_name }} 👋
            </h1>
            <p class="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                أهلاً بك في لوحة تحكم الموظف - مركز إدارة عملياتك اليومية
            </p>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full mx-auto mt-4"></div>
        </div>

        <!-- بطاقات الإحصائيات -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <!-- إجمالي الطلبات -->
            <div class="bg-white/20 dark:bg-gray-800/20 backdrop-blur-sm rounded-3xl p-6 shadow-xl border border-white/20 dark:border-gray-700/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-shopping-cart text-white text-xl"></i>
                    </div>
                    <span class="text-3xl font-bold text-blue-600 dark:text-blue-400">{{ $todayStats['totalOrders'] ?? 0 }}</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">طلبات اليوم</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">طلب جديد</p>
            </div>

            <!-- إجمالي الحجوزات -->
            <div class="bg-white/20 dark:bg-gray-800/20 backdrop-blur-sm rounded-3xl p-6 shadow-xl border border-white/20 dark:border-gray-700/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-calendar-check text-white text-xl"></i>
                    </div>
                    <span class="text-3xl font-bold text-purple-600 dark:text-purple-400">{{ $todayStats['totalReservations'] ?? 0 }}</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">حجوزات اليوم</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">حجز مؤكد</p>
            </div>

            <!-- إجمالي المبيعات -->
            <div class="bg-white/20 dark:bg-gray-800/20 backdrop-blur-sm rounded-3xl p-6 shadow-xl border border-white/20 dark:border-gray-700/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-dollar-sign text-white text-xl"></i>
                    </div>
                    <span class="text-3xl font-bold text-green-600 dark:text-green-400">{{ number_format($todayStats['totalSales'] ?? 0, 0) }}</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">مبيعات اليوم</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">ريال سعودي</p>
            </div>

            <!-- الطاولات المتاحة -->
            <div class="bg-white/20 dark:bg-gray-800/20 backdrop-blur-sm rounded-3xl p-6 shadow-xl border border-white/20 dark:border-gray-700/20 hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                <div class="flex items-center justify-between mb-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-chair text-white text-xl"></i>
                    </div>
                    <span class="text-3xl font-bold text-orange-600 dark:text-orange-400">{{ $todayStats['availableTables'] ?? 0 }}</span>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">طاولات متاحة</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">من إجمالي {{ $todayStats['totalTables'] ?? 0 }}</p>
            </div>
        </div>

        <!-- الوصول السريع -->
        <div class="bg-white/10 dark:bg-gray-800/10 backdrop-blur-sm rounded-3xl p-8 shadow-2xl border border-white/20 dark:border-gray-700/20 mb-8">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800 dark:text-white mb-4">🚀 الوصول السريع</h2>
                <p class="text-gray-600 dark:text-gray-400">اختصارات مفيدة لتسهيل عملك اليومي</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- إدارة الطلبات -->
                <a href="{{ route('employee.orders') }}" class="group bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-6 rounded-2xl border border-blue-200/50 dark:border-blue-700/50 hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-shopping-cart text-white"></i>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-bold text-gray-800 dark:text-white">إدارة الطلبات</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">عرض ومتابعة الطلبات</p>
                        </div>
                    </div>
                    <div class="flex items-center text-blue-600 dark:text-blue-400 group-hover:text-blue-700 dark:group-hover:text-blue-300">
                        <span class="text-sm font-medium">انتقال سريع</span>
                        <i class="fas fa-arrow-left mr-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </div>
                </a>

                <!-- إدارة الحجوزات -->
                <a href="{{ route('employee.reservations') }}" class="group bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-6 rounded-2xl border border-purple-200/50 dark:border-purple-700/50 hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-calendar-check text-white"></i>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-bold text-gray-800 dark:text-white">إدارة الحجوزات</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">متابعة الحجوزات</p>
                        </div>
                    </div>
                    <div class="flex items-center text-purple-600 dark:text-purple-400 group-hover:text-purple-700 dark:group-hover:text-purple-300">
                        <span class="text-sm font-medium">انتقال سريع</span>
                        <i class="fas fa-arrow-left mr-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </div>
                </a>

                <!-- قائمة الطعام -->
                <a href="{{ route('employee.menu') }}" class="group bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-6 rounded-2xl border border-green-200/50 dark:border-green-700/50 hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-utensils text-white"></i>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-bold text-gray-800 dark:text-white">قائمة الطعام</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">عرض الأطباق المتاحة</p>
                        </div>
                    </div>
                    <div class="flex items-center text-green-600 dark:text-green-400 group-hover:text-green-700 dark:group-hover:text-green-300">
                        <span class="text-sm font-medium">انتقال سريع</span>
                        <i class="fas fa-arrow-left mr-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </div>
                </a>

                <!-- إدارة الطاولات -->
                <a href="{{ route('employee.tables') }}" class="group bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 p-6 rounded-2xl border border-orange-200/50 dark:border-orange-700/50 hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-chair text-white"></i>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-bold text-gray-800 dark:text-white">إدارة الطاولات</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">متابعة حالة الطاولات</p>
                        </div>
                    </div>
                    <div class="flex items-center text-orange-600 dark:text-orange-400 group-hover:text-orange-700 dark:group-hover:text-orange-300">
                        <span class="text-sm font-medium">انتقال سريع</span>
                        <i class="fas fa-arrow-left mr-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </div>
                </a>

                <!-- التقارير -->
                <a href="{{ route('employee.reports') }}" class="group bg-gradient-to-r from-indigo-50 to-blue-50 dark:from-indigo-900/20 dark:to-blue-900/20 p-6 rounded-2xl border border-indigo-200/50 dark:border-indigo-700/50 hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-indigo-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-chart-bar text-white"></i>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-bold text-gray-800 dark:text-white">التقارير</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">عرض الإحصائيات</p>
                        </div>
                    </div>
                    <div class="flex items-center text-indigo-600 dark:text-indigo-400 group-hover:text-indigo-700 dark:group-hover:text-indigo-300">
                        <span class="text-sm font-medium">انتقال سريع</span>
                        <i class="fas fa-arrow-left mr-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </div>
                </a>

                <!-- الملف الشخصي -->
                <a href="{{ route('employee.profile') }}" class="group bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-900/20 dark:to-slate-900/20 p-6 rounded-2xl border border-gray-200/50 dark:border-gray-700/50 hover:shadow-lg transform hover:scale-105 transition-all duration-300">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-gray-500 to-slate-600 rounded-full flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-bold text-gray-800 dark:text-white">الملف الشخصي</h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">إدارة الحساب</p>
                        </div>
                    </div>
                    <div class="flex items-center text-gray-600 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300">
                        <span class="text-sm font-medium">انتقال سريع</span>
                        <i class="fas fa-arrow-left mr-2 group-hover:translate-x-1 transition-transform duration-300"></i>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>

<style>
@keyframes blob {
    0% { transform: translate(0px, 0px) scale(1); }
    33% { transform: translate(30px, -50px) scale(1.1); }
    66% { transform: translate(-20px, 20px) scale(0.9); }
    100% { transform: translate(0px, 0px) scale(1); }
}

.animate-blob {
    animation: blob 7s infinite;
}

.animation-delay-2000 {
    animation-delay: 2s;
}
</style>
@endsection
