// ملف منفصل للوضع المظلم
console.log('Dark mode script loaded');

// دالة تبديل الوضع المظلم
function toggleDarkMode() {
    console.log('toggleDarkMode called');
    
    const html = document.documentElement;
    const icon = document.getElementById('darkModeIcon');
    
    console.log('Current dark mode state:', html.classList.contains('dark'));
    console.log('Icon element:', icon);
    
    if (html.classList.contains('dark')) {
        // تبديل إلى الوضع الفاتح
        html.classList.remove('dark');
        if (icon) {
            icon.className = 'fas fa-moon';
        }
        localStorage.setItem('darkMode', 'false');
        console.log('Switched to light mode');
    } else {
        // تبديل إلى الوضع المظلم
        html.classList.add('dark');
        if (icon) {
            icon.className = 'fas fa-sun';
        }
        localStorage.setItem('darkMode', 'true');
        console.log('Switched to dark mode');
    }
}

// تحميل الإعدادات عند بدء الصفحة
function loadDarkModeSettings() {
    console.log('Loading dark mode settings...');
    
    const savedDarkMode = localStorage.getItem('darkMode');
    const html = document.documentElement;
    const icon = document.getElementById('darkModeIcon');
    
    console.log('Saved setting:', savedDarkMode);
    
    if (savedDarkMode === 'true') {
        html.classList.add('dark');
        if (icon) {
            icon.className = 'fas fa-sun';
        }
        console.log('Applied dark mode from settings');
    } else if (savedDarkMode === 'false') {
        html.classList.remove('dark');
        if (icon) {
            icon.className = 'fas fa-moon';
        }
        console.log('Applied light mode from settings');
    } else {
        // لا يوجد إعداد محفوظ، استخدم تفضيلات النظام
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            html.classList.add('dark');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
            localStorage.setItem('darkMode', 'true');
            console.log('Applied dark mode from system preference');
        } else {
            html.classList.remove('dark');
            if (icon) {
                icon.className = 'fas fa-moon';
            }
            localStorage.setItem('darkMode', 'false');
            console.log('Applied light mode as default');
        }
    }
}

// إعداد الزر عند تحميل الصفحة
function setupDarkModeButton() {
    console.log('Setting up dark mode button...');
    
    const button = document.getElementById('darkModeToggle');
    
    if (button) {
        console.log('Dark mode button found');
        
        // إزالة أي event listeners موجودة
        button.removeEventListener('click', toggleDarkMode);
        
        // إضافة event listener جديد
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Dark mode button clicked');
            toggleDarkMode();
        });
        
        console.log('Dark mode button setup complete');
    } else {
        console.error('Dark mode button not found!');
    }
}

// تشغيل الإعدادات عند تحميل DOM
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing dark mode...');
    loadDarkModeSettings();
    setupDarkModeButton();
});

// تشغيل الإعدادات عند تحميل كامل للصفحة
window.addEventListener('load', function() {
    console.log('Window loaded, re-checking dark mode...');
    loadDarkModeSettings();
    setupDarkModeButton();
});

// جعل الدالة متاحة عالمياً
window.toggleDarkMode = toggleDarkMode;
window.loadDarkModeSettings = loadDarkModeSettings;
window.setupDarkModeButton = setupDarkModeButton;

console.log('Dark mode script initialization complete');
