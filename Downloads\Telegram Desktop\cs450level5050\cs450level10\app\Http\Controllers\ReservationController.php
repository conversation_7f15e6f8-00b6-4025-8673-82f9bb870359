<?php

namespace App\Http\Controllers;

use App\Models\Reservation;
use App\Models\Table;
use App\Models\User;
use App\Models\Notification;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Routing\Controller;

class ReservationController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    // Employee methods
    public function index()
    {
        $this->middleware('employee');

        $reservations = Reservation::with(['user', 'table'])
            ->orderBy('reservation_time')
            ->paginate(15);

        return view('employee.reservations.index', compact('reservations'));
    }

    public function employeeIndex()
    {
        return $this->index();
    }

    public function create()
    {
        $this->middleware('employee');

        $tables = Table::all();
        $customers = User::where('user_type', 'customer')->get();

        return view('employee.reservations.create', compact('tables', 'customers'));
    }

    public function store(Request $request)
    {
        $this->middleware('employee');

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,user_id',
            'table_id' => 'required|exists:tables,table_id',
            'reservation_time' => 'required|date|after:now',
            'duration' => 'required|integer|min:30|max:240',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // Check if table is available at the requested time
        $startTime = new \DateTime($request->reservation_time);
        $endTime = clone $startTime;
        $endTime->add(new \DateInterval('PT' . $request->duration . 'M'));

        $conflictingReservations = Reservation::where('table_id', $request->table_id)
            ->where('status', 'confirmed')
            ->where(function($query) use ($startTime, $endTime) {
                // Reservation overlaps with the requested time
                $query->where(function($q) use ($startTime, $endTime) {
                    $q->where('reservation_time', '<', $endTime->format('Y-m-d H:i:s'))
                      ->whereRaw('DATE_ADD(reservation_time, INTERVAL duration MINUTE) > ?', [$startTime->format('Y-m-d H:i:s')]);
                });
            })
            ->count();

        if ($conflictingReservations > 0) {
            return redirect()->back()->with('error', 'الطاولة غير متاحة في هذا الوقت')->withInput();
        }

        DB::beginTransaction();

        try {
            // Create reservation
            $reservation = Reservation::create([
                'user_id' => $request->user_id,
                'table_id' => $request->table_id,
                'reservation_time' => $request->reservation_time,
                'duration' => $request->duration,
                'status' => 'confirmed',
            ]);

            // Create notification for the customer
            Notification::create([
                'user_id' => $request->user_id,
                'message' => 'تم تأكيد حجزك للطاولة رقم ' . Table::find($request->table_id)->table_number . ' في ' . $startTime->format('Y-m-d H:i'),
                'is_read' => false,
            ]);

            DB::commit();

            return redirect()->route('employee.reservations')->with('success', 'تم إنشاء الحجز بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء إنشاء الحجز: ' . $e->getMessage())->withInput();
        }
    }

    public function show($id)
    {
        $this->middleware('employee');

        $reservation = Reservation::with(['user', 'table'])->findOrFail($id);

        return view('employee.reservations.show', compact('reservation'));
    }

    public function edit($id)
    {
        $this->middleware('employee');

        $reservation = Reservation::with(['user', 'table'])->findOrFail($id);
        $tables = Table::all();

        return view('employee.reservations.edit', compact('reservation', 'tables'));
    }

    public function update(Request $request, $id)
    {
        $this->middleware('employee');

        $reservation = Reservation::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'table_id' => 'required|exists:tables,table_id',
            'reservation_time' => 'required|date|after:now',
            'duration' => 'required|integer|min:30|max:240',
            'status' => 'required|in:confirmed,canceled,completed',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // If changing table or time, check availability
        if ($request->table_id != $reservation->table_id ||
            $request->reservation_time != $reservation->reservation_time->format('Y-m-d H:i:s') ||
            $request->duration != $reservation->duration) {

            $startTime = new \DateTime($request->reservation_time);
            $endTime = clone $startTime;
            $endTime->add(new \DateInterval('PT' . $request->duration . 'M'));

            $conflictingReservations = Reservation::where('table_id', $request->table_id)
                ->where('status', 'confirmed')
                ->where('reservation_id', '!=', $id)
                ->where(function($query) use ($startTime, $endTime) {
                    // Reservation overlaps with the requested time
                    $query->where(function($q) use ($startTime, $endTime) {
                        $q->where('reservation_time', '<', $endTime->format('Y-m-d H:i:s'))
                          ->whereRaw('DATE_ADD(reservation_time, INTERVAL duration MINUTE) > ?', [$startTime->format('Y-m-d H:i:s')]);
                    });
                })
                ->count();

            if ($conflictingReservations > 0) {
                return redirect()->back()->with('error', 'الطاولة غير متاحة في هذا الوقت')->withInput();
            }
        }

        DB::beginTransaction();

        try {
            // Update reservation
            $reservation->update([
                'table_id' => $request->table_id,
                'reservation_time' => $request->reservation_time,
                'duration' => $request->duration,
                'status' => $request->status,
            ]);

            // Get table information safely
            $table = Table::find($request->table_id);
            $tableNumber = $table ? $table->table_number : 'غير محدد';

            // Format reservation time safely
            $reservationTimeFormatted = '';
            try {
                if ($request->reservation_time) {
                    $reservationTimeFormatted = (new \DateTime($request->reservation_time))->format('Y-m-d H:i');
                }
            } catch (\Exception $e) {
                $reservationTimeFormatted = 'غير محدد';
            }

            // Create notification for the customer
            $message = '';
            switch ($request->status) {
                case 'confirmed':
                    $message = 'تم تأكيد حجزك للطاولة رقم ' . $tableNumber . ($reservationTimeFormatted ? ' في ' . $reservationTimeFormatted : '');
                    break;
                case 'canceled':
                    $message = 'تم إلغاء حجزك للطاولة رقم ' . $tableNumber;
                    break;
                case 'completed':
                    $message = 'تم إكمال حجزك للطاولة رقم ' . $tableNumber . '. نشكرك على زيارتك!';
                    break;
            }

            Notification::create([
                'user_id' => $reservation->user_id,
                'message' => $message,
                'is_read' => false,
            ]);

            DB::commit();

            return redirect()->route('employee.reservations')->with('success', 'تم تحديث الحجز بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء تحديث الحجز: ' . $e->getMessage())->withInput();
        }
    }

    public function updateStatus(Request $request, $id)
    {
        $reservation = Reservation::findOrFail($id);

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:confirmed,canceled,completed',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        DB::beginTransaction();

        try {
            // Update reservation status
            $reservation->update([
                'status' => $request->status,
            ]);

            // Get table information safely
            $table = Table::find($reservation->table_id);
            $tableNumber = $table ? $table->table_number : 'غير محدد';

            // Format reservation time safely
            $reservationTimeFormatted = '';
            try {
                if ($reservation->reservation_time) {
                    $reservationTimeFormatted = is_string($reservation->reservation_time)
                        ? \Carbon\Carbon::parse($reservation->reservation_time)->format('Y-m-d H:i')
                        : $reservation->reservation_time->format('Y-m-d H:i');
                }
            } catch (\Exception $e) {
                $reservationTimeFormatted = 'غير محدد';
            }

            // Create notification for the customer
            $message = '';
            switch ($request->status) {
                case 'confirmed':
                    $message = 'تم تأكيد حجزك للطاولة رقم ' . $tableNumber . ($reservationTimeFormatted ? ' في ' . $reservationTimeFormatted : '');
                    break;
                case 'canceled':
                    $message = 'تم إلغاء حجزك للطاولة رقم ' . $tableNumber;
                    break;
                case 'completed':
                    $message = 'تم إكمال حجزك للطاولة رقم ' . $tableNumber . '. نشكرك على زيارتك!';
                    break;
            }

            Notification::create([
                'user_id' => $reservation->user_id,
                'message' => $message,
                'is_read' => false,
            ]);

            DB::commit();

            return redirect()->route('employee.reservations')->with('success', 'تم تحديث حالة الحجز بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء تحديث حالة الحجز: ' . $e->getMessage());
        }
    }

    public function delete($id)
    {
        $reservation = Reservation::findOrFail($id);

        DB::beginTransaction();

        try {
            // Create notification for the customer
            Notification::create([
                'user_id' => $reservation->user_id,
                'message' => 'تم حذف حجزك للطاولة رقم ' . Table::find($reservation->table_id)->table_number,
                'is_read' => false,
            ]);

            // Delete reservation
            $reservation->delete();

            DB::commit();

            return redirect()->route('employee.reservations')->with('success', 'تم حذف الحجز بنجاح');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء حذف الحجز: ' . $e->getMessage());
        }
    }

    // Customer methods
    public function customerIndex()
    {
        $reservations = Reservation::with('table')
            ->where('user_id', Auth::id())
            ->orderBy('reservation_time', 'desc')
            ->paginate(10);

        return view('customer.reservations.index', compact('reservations'));
    }

    public function customerCreate()
    {
        // جلب جميع الطاولات المتاحة مع معلوماتها
        $tables = Table::where('status', 'available')
            ->orderBy('capacity')
            ->orderBy('table_number')
            ->get();

        return view('customer.reservations.create', compact('tables'));
    }

    // API endpoint للتحقق من توفر الطاولات
    public function checkAvailability(Request $request)
    {
        // تسجيل البيانات المستلمة للتشخيص
        Log::info('Check availability request:', $request->all());

        $validator = Validator::make($request->all(), [
            'date' => 'required|date|after_or_equal:today',
            'time' => 'required',
            'party_size' => 'required|integer|min:1|max:10',
        ]);

        if ($validator->fails()) {
            Log::error('Validation failed:', $validator->errors()->toArray());
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة: ' . $validator->errors()->first(),
                'errors' => $validator->errors()
            ]);
        }

        $requestedDateTime = $request->date . ' ' . $request->time;

        // التحقق من أن الوقت في المستقبل
        if (strtotime($requestedDateTime) <= time()) {
            return response()->json([
                'success' => false,
                'message' => 'يجب أن يكون وقت الحجز في المستقبل'
            ]);
        }

        // جلب الطاولات المناسبة لعدد الأشخاص
        $suitableTables = Table::where('capacity', '>=', $request->party_size)
            ->where('status', 'available')
            ->get();

        $availableTables = [];

        foreach ($suitableTables as $table) {
            // التحقق من عدم وجود حجوزات متضاربة
            $conflictingReservations = Reservation::where('table_id', $table->table_id)
                ->where('status', '!=', 'cancelled')
                ->where(function($query) use ($requestedDateTime) {
                    $startTime = date('Y-m-d H:i:s', strtotime($requestedDateTime));
                    $endTime = date('Y-m-d H:i:s', strtotime($requestedDateTime . ' +2 hours'));

                    $query->whereBetween('reservation_time', [$startTime, $endTime])
                          ->orWhere(function($q) use ($startTime, $endTime) {
                              $q->where('reservation_time', '<=', $startTime)
                                ->whereRaw('DATE_ADD(reservation_time, INTERVAL duration MINUTE) >= ?', [$startTime]);
                          });
                })
                ->exists();

            if (!$conflictingReservations) {
                $availableTables[] = [
                    'table_id' => $table->table_id,
                    'table_number' => $table->table_number,
                    'capacity' => $table->capacity,
                    'location' => $table->location ?? 'منطقة عامة',
                    'description' => $table->description ?? ''
                ];
            }
        }

        return response()->json([
            'success' => true,
            'available_tables' => $availableTables,
            'message' => count($availableTables) > 0 ?
                'تم العثور على ' . count($availableTables) . ' طاولة متاحة' :
                'لا توجد طاولات متاحة في هذا الوقت'
        ]);
    }

    public function customerStore(Request $request)
    {
        // التحقق من نوع الحجز (عادي أو من عرض)
        $isOfferReservation = $request->has('offer_slug');

        $validationRules = [
            'reservation_date' => 'required|date|after_or_equal:today',
            'reservation_time' => 'required',
            'party_size' => 'required|integer|min:1|max:10',
            'special_requests' => 'nullable|string|max:500',
        ];

        // إضافة قواعد التحقق للحجز العادي
        if (!$isOfferReservation) {
            $validationRules['selected_table'] = 'required|exists:tables,table_id';
            $validationRules['payment_method'] = 'required|in:cash,card,bank_transfer';
            $validationRules['card_number'] = 'required_if:payment_method,card|nullable|string';
            $validationRules['card_expiry'] = 'required_if:payment_method,card|nullable|string';
            $validationRules['card_cvv'] = 'required_if:payment_method,card|nullable|string';
        }

        // تسجيل البيانات المستلمة للتشخيص
        Log::info('Customer store request:', $request->all());
        Log::info('Validation rules:', $validationRules);

        $validator = Validator::make($request->all(), $validationRules);

        if ($validator->fails()) {
            Log::error('Customer store validation failed:', $validator->errors()->toArray());
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // دمج التاريخ والوقت
        $reservationDateTime = $request->reservation_date . ' ' . $request->reservation_time;

        // التحقق من أن الوقت في المستقبل
        if (strtotime($reservationDateTime) <= time()) {
            return redirect()->back()->with('error', 'يجب أن يكون وقت الحجز في المستقبل')->withInput();
        }

        DB::beginTransaction();

        try {
            // اختيار الطاولة المناسبة
            if ($isOfferReservation) {
                // للحجوزات من العروض، اختيار أفضل طاولة متاحة تلقائياً
                $selectedTable = Table::where('status', 'available')
                    ->where('capacity', '>=', $request->party_size)
                    ->orderBy('capacity') // اختيار أصغر طاولة مناسبة
                    ->first();

                if (!$selectedTable) {
                    return redirect()->back()->with('error', 'لا توجد طاولات متاحة لهذا العدد من الأشخاص')->withInput();
                }
            } else {
                // للحجوزات العادية، استخدام الطاولة المحددة
                $selectedTable = Table::where('table_id', $request->selected_table)
                    ->where('status', 'available')
                    ->where('capacity', '>=', $request->party_size)
                    ->first();

                if (!$selectedTable) {
                    return redirect()->back()->with('error', 'الطاولة المحددة غير متاحة')->withInput();
                }
            }

            // التحقق من عدم وجود حجوزات متضاربة
            $conflictingReservations = Reservation::where('table_id', $selectedTable->table_id)
                ->where('status', '!=', 'cancelled')
                ->where(function($query) use ($reservationDateTime) {
                    $startTime = date('Y-m-d H:i:s', strtotime($reservationDateTime));
                    $endTime = date('Y-m-d H:i:s', strtotime($reservationDateTime . ' +2 hours'));

                    $query->whereBetween('reservation_time', [$startTime, $endTime])
                          ->orWhere(function($q) use ($startTime) {
                              $q->where('reservation_time', '<=', $startTime)
                                ->whereRaw('DATE_ADD(reservation_time, INTERVAL duration MINUTE) >= ?', [$startTime]);
                          });
                })
                ->exists();

            if ($conflictingReservations) {
                return redirect()->back()->with('error', 'الطاولة محجوزة في هذا الوقت')->withInput();
            }

            // تحضير بيانات الحجز
            $reservationData = [
                'user_id' => Auth::id(),
                'table_id' => $selectedTable->table_id,
                'reservation_time' => $reservationDateTime,
                'party_size' => $request->party_size,
                'special_requests' => $request->special_requests,
                'status' => 'pending',
                'duration' => 120, // مدة افتراضية ساعتين
            ];

            // إضافة معلومات العرض إذا كان الحجز من عرض
            if ($isOfferReservation) {
                $reservationData['offer_slug'] = $request->offer_slug;
                $reservationData['offer_title'] = $request->offer_title;
                $reservationData['contact_phone'] = $request->contact_phone;
            }

            // إنشاء الحجز
            $reservation = Reservation::create($reservationData);

            // معالجة الدفع (فقط للحجوزات العادية)
            if (!$isOfferReservation) {
                $paymentStatus = $this->processReservationPayment($request, $reservation);

                if (!$paymentStatus['success']) {
                    DB::rollBack();
                    return redirect()->back()->with('error', $paymentStatus['message'])->withInput();
                }
            }

            // إشعار الموظفين
            $this->notifyStaffAboutNewReservation($reservation);

            DB::commit();

            // رسالة النجاح حسب نوع الحجز
            if ($isOfferReservation) {
                $successMessage = 'تم إرسال طلب حجز العرض بنجاح! ';
                $successMessage .= 'تم تخصيص الطاولة رقم ' . $selectedTable->table_number . ' لك. ';
                $successMessage .= 'سيتم التواصل معك قريباً لتأكيد الحجز.';
            } else {
                $successMessage = 'تم إرسال طلب الحجز بنجاح. ';
                if ($request->payment_method === 'cash') {
                    $successMessage .= 'يرجى دفع رسوم الحجز (25 د.ل) عند الوصول للمطعم.';
                } elseif ($request->payment_method === 'card') {
                    $successMessage .= 'تم خصم رسوم الحجز من بطاقتك الائتمانية.';
                } else {
                    $successMessage .= 'يرجى إرسال إيصال التحويل البنكي عبر الواتساب.';
                }
            }

            return redirect()->route('customer.reservations')->with('success', $successMessage);
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء إنشاء الحجز: ' . $e->getMessage())->withInput();
        }
    }

    public function customerShow($id)
    {
        $reservation = Reservation::with('table')
            ->where('user_id', Auth::id())
            ->findOrFail($id);

        return view('customer.reservations.show', compact('reservation'));
    }

    public function customerEdit($id)
    {
        $reservation = Reservation::with('table')
            ->where('user_id', Auth::id())
            ->findOrFail($id);

        // Check if the reservation can be edited (at least 2 hours before)
        if ($reservation->reservation_time < now()->addHours(2)) {
            return redirect()->route('customer.reservations')
                ->with('error', 'لا يمكن تعديل الحجز قبل أقل من ساعتين من الموعد');
        }

        // Get available tables
        $tables = Table::where('status', 'available')
            ->orderBy('capacity')
            ->orderBy('table_number')
            ->get();

        return view('customer.reservations.edit', compact('reservation', 'tables'));
    }

    public function customerUpdate(Request $request, $id)
    {
        $reservation = Reservation::where('user_id', Auth::id())->findOrFail($id);

        // Check if the reservation can be edited (at least 2 hours before)
        if ($reservation->reservation_time < now()->addHours(2)) {
            return redirect()->route('customer.reservations')
                ->with('error', 'لا يمكن تعديل الحجز قبل أقل من ساعتين من الموعد');
        }

        $validator = Validator::make($request->all(), [
            'reservation_date' => 'required|date|after_or_equal:today',
            'reservation_time' => 'required',
            'guest_count' => 'required|integer|min:1|max:10',
            'table_preference' => 'nullable|string',
            'duration' => 'required|integer|min:60|max:180',
            'occasion' => 'nullable|string',
            'special_requests' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        // Combine date and time
        $reservationDateTime = $request->reservation_date . ' ' . $request->reservation_time;

        // Check if the time is in the future
        if (strtotime($reservationDateTime) <= time()) {
            return redirect()->back()->with('error', 'يجب أن يكون وقت الحجز في المستقبل')->withInput();
        }

        DB::beginTransaction();

        try {
            // Find suitable table based on preference and guest count
            $tableQuery = Table::where('status', 'available')
                ->where('capacity', '>=', $request->guest_count);

            // Apply table preference filter
            if ($request->table_preference && $request->table_preference !== '') {
                switch ($request->table_preference) {
                    case 'window':
                        $tableQuery->where('location', 'LIKE', '%نافذة%');
                        break;
                    case 'garden':
                        $tableQuery->where('location', 'LIKE', '%حديقة%');
                        break;
                    case 'quiet':
                        $tableQuery->where('location', 'LIKE', '%هادئة%');
                        break;
                    case 'family':
                        $tableQuery->where('location', 'LIKE', '%عائلات%');
                        break;
                    case 'vip':
                        $tableQuery->where('location', 'LIKE', '%VIP%');
                        break;
                }
            }

            $selectedTable = $tableQuery->orderBy('capacity')->first();

            // If no table matches preference, get any suitable table
            if (!$selectedTable) {
                $selectedTable = Table::where('status', 'available')
                    ->where('capacity', '>=', $request->guest_count)
                    ->orderBy('capacity')
                    ->first();
            }

            if (!$selectedTable) {
                return redirect()->back()->with('error', 'لا توجد طاولات متاحة لهذا العدد من الأشخاص')->withInput();
            }

            // Check for conflicting reservations (excluding current reservation)
            $conflictingReservations = Reservation::where('table_id', $selectedTable->table_id)
                ->where('status', '!=', 'cancelled')
                ->where('reservation_id', '!=', $id)
                ->where(function($query) use ($reservationDateTime, $request) {
                    $startTime = date('Y-m-d H:i:s', strtotime($reservationDateTime));
                    $endTime = date('Y-m-d H:i:s', strtotime($reservationDateTime . ' +' . $request->duration . ' minutes'));

                    $query->whereBetween('reservation_time', [$startTime, $endTime])
                          ->orWhere(function($q) use ($startTime, $endTime) {
                              $q->where('reservation_time', '<=', $startTime)
                                ->whereRaw('DATE_ADD(reservation_time, INTERVAL duration MINUTE) >= ?', [$startTime]);
                          });
                })
                ->exists();

            if ($conflictingReservations) {
                return redirect()->back()->with('error', 'الطاولة محجوزة في هذا الوقت')->withInput();
            }

            // Update reservation
            $reservation->update([
                'table_id' => $selectedTable->table_id,
                'reservation_time' => $reservationDateTime,
                'party_size' => $request->guest_count,
                'duration' => $request->duration,
                'special_requests' => $request->special_requests,
                'status' => 'pending', // Reset to pending for review
            ]);

            // Notify staff about the update
            $this->notifyStaffAboutUpdatedReservation($reservation);

            DB::commit();

            return redirect()->route('customer.reservations')
                ->with('success', 'تم تحديث الحجز بنجاح. سيتم مراجعة التعديلات وتأكيدها قريباً.');

        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', 'حدث خطأ أثناء تحديث الحجز: ' . $e->getMessage())->withInput();
        }
    }

    public function customerDelete($id)
    {
        $reservation = Reservation::with('table')
            ->where('user_id', Auth::id())
            ->findOrFail($id);

        // Check if the reservation can be deleted
        if ($reservation->reservation_time < now()) {
            return redirect()->route('customer.reservations')
                ->with('error', 'لا يمكن حذف حجز في الماضي');
        }

        return view('customer.reservations.delete', compact('reservation'));
    }

    public function customerCancel($id)
    {
        $reservation = Reservation::where('user_id', Auth::id())->findOrFail($id);

        // Check if the reservation is in the future
        if ($reservation->reservation_time < now()) {
            return redirect()->back()->with('error', 'لا يمكن إلغاء حجز في الماضي');
        }

        DB::beginTransaction();

        try {
            // Update reservation status to canceled
            $reservation->update(['status' => 'canceled']);

            // Notify staff
            $this->notifyStaffAboutCanceledReservation($reservation);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'تم إلغاء الحجز بنجاح'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إلغاء الحجز: ' . $e->getMessage()
            ]);
        }
    }

    // Helper methods
    private function processReservationPayment($request, $reservation)
    {
        $reservationFee = 25.00; // رسوم الحجز

        try {
            switch ($request->payment_method) {
                case 'cash':
                    // الدفع نقداً - لا حاجة لمعالجة فورية
                    return [
                        'success' => true,
                        'message' => 'سيتم الدفع نقداً في المطعم'
                    ];

                case 'card':
                    // معالجة الدفع بالبطاقة (محاكاة)
                    if (empty($request->card_number) || empty($request->card_expiry) || empty($request->card_cvv)) {
                        return [
                            'success' => false,
                            'message' => 'بيانات البطاقة غير مكتملة'
                        ];
                    }

                    // هنا يمكن إضافة تكامل مع بوابة دفع حقيقية
                    // حالياً سنقوم بمحاكاة نجاح العملية

                    // إنشاء سجل دفع (مؤقتاً نستخدم order_id = 0 للحجوزات)
                    // يمكن إضافة حقل reservation_id لاحقاً
                    /*
                    Payment::create([
                        'order_id' => 0, // للحجوزات نستخدم 0
                        'amount' => $reservationFee,
                        'payment_method' => 'card',
                        'transaction_date' => now(),
                    ]);
                    */

                    return [
                        'success' => true,
                        'message' => 'تم الدفع بالبطاقة بنجاح'
                    ];

                case 'bank_transfer':
                    // التحويل البنكي - يحتاج تأكيد يدوي
                    return [
                        'success' => true,
                        'message' => 'في انتظار تأكيد التحويل البنكي'
                    ];

                default:
                    return [
                        'success' => false,
                        'message' => 'طريقة دفع غير صحيحة'
                    ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء معالجة الدفع: ' . $e->getMessage()
            ];
        }
    }

    private function notifyStaffAboutNewReservation($reservation)
    {
        // Find employee users
        $employees = DB::table('users')
            ->where('user_type', 'employee')
            ->where('is_active', true)
            ->get();

        $table = Table::find($reservation->table_id);

        foreach ($employees as $employee) {
            Notification::create([
                'user_id' => $employee->user_id,
                'message' => 'حجز جديد للطاولة رقم ' . $table->table_number . ' في ' . $reservation->reservation_time,
                'is_read' => false,
            ]);
        }
    }

    private function notifyStaffAboutCanceledReservation($reservation)
    {
        // Find employee users
        $employees = DB::table('users')
            ->where('user_type', 'employee')
            ->where('is_active', true)
            ->get();

        $table = Table::find($reservation->table_id);

        foreach ($employees as $employee) {
            Notification::create([
                'user_id' => $employee->user_id,
                'message' => 'تم إلغاء حجز الطاولة رقم ' . $table->table_number . ' في ' . $reservation->reservation_time,
                'is_read' => false,
            ]);
        }
    }

    private function notifyStaffAboutUpdatedReservation($reservation)
    {
        // Find employee users
        $employees = DB::table('users')
            ->where('user_type', 'employee')
            ->where('is_active', true)
            ->get();

        $table = Table::find($reservation->table_id);

        foreach ($employees as $employee) {
            Notification::create([
                'user_id' => $employee->user_id,
                'message' => 'تم تعديل حجز الطاولة رقم ' . $table->table_number . ' في ' . $reservation->reservation_time,
                'is_read' => false,
            ]);
        }
    }
}