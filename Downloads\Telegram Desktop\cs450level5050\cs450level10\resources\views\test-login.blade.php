<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - Eat Hub</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#FF6B35',
                        secondary: '#2C3E50',
                        darkText: '#1A202C'
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { font-family: 'Cairo', sans-serif; }
    </style>
</head>
<body class="bg-gray-50 dark:bg-gray-900 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full mx-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-8">
            <!-- Logo -->
            <div class="text-center mb-8">
                <div class="text-3xl font-bold text-primary flex items-center justify-center mb-4">
                    <i class="fas fa-utensils ml-2"></i>
                    <span>Eat Hub</span>
                </div>
                <h2 class="text-2xl font-bold text-gray-800 dark:text-white">تسجيل الدخول</h2>
                <p class="text-gray-600 dark:text-gray-400 mt-2">أدخل بياناتك للوصول إلى حسابك</p>
            </div>

            <!-- رسائل الخطأ -->
            @if($errors->any())
                <div class="mb-6 bg-red-100 dark:bg-red-900/30 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded-lg">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-exclamation-circle ml-2"></i>
                        <span class="font-medium">خطأ في تسجيل الدخول:</span>
                    </div>
                    <ul class="list-disc list-inside space-y-1">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            @if(session('error'))
                <div class="mb-6 bg-red-100 dark:bg-red-900/30 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-300 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle ml-2"></i>
                        {{ session('error') }}
                    </div>
                </div>
            @endif

            <!-- نموذج تسجيل الدخول -->
            <form action="{{ route('login') }}" method="POST" class="space-y-6">
                @csrf
                
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">البريد الإلكتروني</label>
                    <div class="relative">
                        <input type="email" id="email" name="email" value="{{ old('email') }}" required class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white pr-10">
                        <div class="absolute top-3 right-3 text-gray-500 dark:text-gray-400">
                            <i class="fas fa-envelope"></i>
                        </div>
                    </div>
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">كلمة المرور</label>
                    <div class="relative">
                        <input type="password" id="password" name="password" required class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white pr-10">
                        <div class="absolute top-3 right-3 text-gray-500 dark:text-gray-400">
                            <i class="fas fa-lock"></i>
                        </div>
                    </div>
                </div>

                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input type="checkbox" id="remember" name="remember" class="w-4 h-4 text-primary border-gray-300 dark:border-gray-600 focus:ring-primary">
                        <label for="remember" class="mr-2 block text-sm text-gray-700 dark:text-gray-300">تذكرني</label>
                    </div>
                    <a href="#" class="text-sm text-primary hover:underline">نسيت كلمة المرور؟</a>
                </div>

                <button type="submit" class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-3 px-4 rounded-lg transition">
                    <i class="fas fa-sign-in-alt ml-2"></i>
                    تسجيل الدخول
                </button>
            </form>

            <!-- بيانات تجريبية -->
            <div class="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                <h3 class="text-sm font-bold text-blue-800 dark:text-blue-300 mb-2">بيانات تجريبية للاختبار:</h3>
                <div class="text-xs text-blue-700 dark:text-blue-400 space-y-1">
                    <p><strong>العميل:</strong> <EMAIL> / password123</p>
                    <p><strong>المدير:</strong> <EMAIL> / admin123</p>
                </div>
            </div>

            <!-- روابط سريعة -->
            <div class="mt-6 text-center space-y-2">
                <a href="{{ route('test.profile') }}" class="block text-primary hover:underline text-sm">
                    <i class="fas fa-eye ml-1"></i>
                    عرض صفحة الملف الشخصي (تجريبي)
                </a>
                <a href="{{ url('/') }}" class="block text-gray-600 dark:text-gray-400 hover:text-primary text-sm">
                    <i class="fas fa-home ml-1"></i>
                    العودة للرئيسية
                </a>
            </div>
        </div>
    </div>

    <script>
        // ملء البيانات التجريبية عند النقر
        document.addEventListener('DOMContentLoaded', function() {
            const testData = document.querySelector('.bg-blue-50, .bg-blue-900\\/20');
            if (testData) {
                testData.addEventListener('click', function() {
                    document.getElementById('email').value = '<EMAIL>';
                    document.getElementById('password').value = 'password123';
                });
            }
        });
    </script>
</body>
</html>
