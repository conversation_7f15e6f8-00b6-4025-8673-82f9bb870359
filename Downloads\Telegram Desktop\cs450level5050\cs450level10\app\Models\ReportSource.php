<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ReportSource extends Model
{
    use HasFactory;

    protected $primaryKey = 'source_id';
    
    protected $fillable = [
        'report_id',
        'source_type',
        'source_ref_id',
        'amount'
    ];

    protected $casts = [
        'amount' => 'decimal:2'
    ];

    public function report()
    {
        return $this->belongsTo(FinancialReport::class, 'report_id');
    }
}