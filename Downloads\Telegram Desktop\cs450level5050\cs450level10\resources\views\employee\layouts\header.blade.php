<!-- الهيدر المبدع -->
<header class="bg-gradient-to-r from-white/20 via-blue-50/20 to-indigo-50/20 dark:from-gray-800/20 dark:via-gray-850/20 dark:to-gray-900/20 backdrop-blur-sm shadow-2xl border-b-2 border-gradient-primary z-40 relative">
    <!-- خلفية متحركة -->
    <div class="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-pink-500/5 animate-pulse"></div>
    <div class="relative px-6 py-4 flex justify-between items-center">
        <!-- زر فتح/إغلاق القائمة الجانبية المبدع -->
        <button id="sidebarToggle" class="md:hidden btn-magical p-3 rounded-xl bg-gradient-to-r from-orange-100 to-red-100 dark:from-orange-900/30 dark:to-red-900/30 text-orange-600 dark:text-orange-400 hover:from-orange-200 hover:to-red-200 dark:hover:from-orange-800/40 dark:hover:to-red-800/40 transition-all duration-300 shadow-lg hover:shadow-xl border border-orange-200 dark:border-orange-700">
            <i class="fas fa-bars text-xl"></i>
        </button>

        <!-- عنوان الصفحة المبدع -->
        <div class="hidden md:block">
            <div class="flex items-center">
                <div class="p-2 rounded-lg bg-gradient-primary text-white mr-3 shadow-lg">
                    <i class="fas fa-tachometer-alt"></i>
                </div>
                <div>
                    <h1 id="pageTitle" class="text-2xl font-bold text-gradient-primary">لوحة التحكم - الموظفين</h1>
                    <p class="text-sm text-indigo-600 dark:text-indigo-400 font-medium">🍽️ مطعم الذوق الرفيع - نظام إدارة المطعم</p>
                </div>
            </div>
            <div class="w-20 h-1 bg-gradient-primary rounded-full mt-2 shadow-sm"></div>
        </div>

        <div class="flex items-center space-x-4 space-x-reverse">
            <!-- زر البحث المبدع -->
            <div class="relative">
                <form action="{{ route('search') }}" method="GET" class="hidden md:flex items-center">
                    <div class="relative">
                        <input type="text" name="query" placeholder="بحث في النظام..." class="w-64 px-4 py-3 pr-12 rounded-xl border-2 border-blue-200 dark:border-blue-600 bg-gradient-to-r from-white to-blue-50 dark:from-gray-700 dark:to-gray-600 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm transition-all duration-300 shadow-lg focus:shadow-xl placeholder-gray-500 dark:placeholder-gray-400">
                        <button type="submit" class="absolute left-3 top-1/2 transform -translate-y-1/2 p-2 rounded-lg bg-gradient-to-r from-blue-500 to-indigo-500 text-white hover:from-blue-600 hover:to-indigo-600 transition-all duration-300 shadow-md hover:shadow-lg">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
                <button id="mobileSearchBtn" class="md:hidden p-3 rounded-xl text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-blue-100 hover:to-indigo-100 dark:hover:from-blue-900/30 dark:hover:to-indigo-900/30 hover:text-blue-600 dark:hover:text-blue-400 transition-all duration-300 hover:shadow-lg">
                    <i class="fas fa-search"></i>
                </button>
            </div>

            <!-- زر سلة الطلبات المبدع -->
            <div class="relative">
                <a href="{{ route('employee.orders.create') }}" class="btn-magical relative p-3 rounded-xl bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 text-green-600 dark:text-green-400 hover:from-green-200 hover:to-emerald-200 dark:hover:from-green-800/40 dark:hover:to-emerald-800/40 transition-all duration-300 shadow-lg hover:shadow-xl inline-block group border border-green-200 dark:border-green-700" title="إنشاء طلب جديد">
                    <i class="fas fa-shopping-cart text-lg group-hover:scale-110 transition-transform duration-300"></i>
                    <span class="cart-count absolute -top-1 -right-1 bg-gradient-to-r from-red-500 to-pink-500 text-white rounded-full text-xs w-6 h-6 flex items-center justify-center shadow-lg animate-bounce font-bold hidden">0</span>
                </a>
                <!-- قائمة سلة الطلبات - ستظهر عند النقر -->
                <div id="cartMenu" class="absolute left-0 top-full mt-2 w-80 bg-white dark:bg-gray-800 rounded-md shadow-xl py-1 z-50 hidden border border-gray-200 dark:border-gray-700">
                    <div class="px-4 py-2 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                        <h3 class="font-bold text-gray-800 dark:text-white">سلة الطلبات</h3>
                        <a href="{{ route('employee.orders.create') }}" class="text-primary text-sm hover:underline">إنشاء طلب</a>
                    </div>
                    <div id="cartItemsList" class="max-h-64 overflow-y-auto">
                        <!-- ستتم تعبئة هذا القسم بعناصر السلة من خلال JavaScript -->
                        <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                            <p>لا توجد عناصر في السلة</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- زر الإشعارات المبدع -->
            <div class="relative">
                <button id="notificationBtn" class="btn-magical relative p-3 rounded-xl bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900/30 dark:to-pink-900/30 text-purple-600 dark:text-purple-400 hover:from-purple-200 hover:to-pink-200 dark:hover:from-purple-800/40 dark:hover:to-pink-800/40 transition-all duration-300 shadow-lg hover:shadow-xl group border border-purple-200 dark:border-purple-700">
                    <i class="fas fa-bell text-lg group-hover:animate-pulse"></i>
                    @php
                        $unreadCount = \App\Models\Notification::where('user_id', Auth::id())->where('is_read', false)->count();
                    @endphp
                    <span id="notificationCount" class="absolute -top-1 -right-1 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-full text-xs min-w-[24px] h-6 flex items-center justify-center shadow-lg animate-bounce font-bold {{ $unreadCount > 0 ? '' : 'hidden' }}">{{ $unreadCount }}</span>
                </button>
                <!-- قائمة الإشعارات - ستظهر عند النقر -->
                <div id="notificationsMenu" class="absolute left-0 top-full mt-2 w-80 bg-white dark:bg-gray-800 rounded-md shadow-xl py-1 z-50 hidden border border-gray-200 dark:border-gray-700">
                    <div class="px-4 py-2 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                        <h3 class="font-bold text-gray-800 dark:text-white">الإشعارات</h3>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <button onclick="markAllNotificationsAsRead()" class="text-xs text-gray-500 hover:text-primary transition-colors" title="تعليم الكل كمقروء">
                                <i class="fas fa-check-double"></i>
                            </button>
                            <a href="{{ route('employee.notifications') }}" class="text-primary text-sm hover:underline">عرض الكل</a>
                        </div>
                    </div>
                    <div id="notificationsList" class="max-h-64 overflow-y-auto">
                        <!-- ستتم تعبئة هذا القسم بالإشعارات من خلال JavaScript -->
                        <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                            <p>جاري تحميل الإشعارات...</p>
                        </div>
                    </div>
                    <div class="border-t border-gray-200 dark:border-gray-700 p-2">
                        <a href="{{ route('employee.notifications') }}" class="block w-full text-center py-2 text-primary hover:bg-primary/10 rounded-md transition-colors">
                            عرض جميع الإشعارات
                        </a>
                    </div>
                </div>
            </div>

            <!-- زر تبديل الثيم المبدع -->
            <button data-theme-toggle class="btn-magical theme-toggle p-3 rounded-xl bg-gradient-to-r from-yellow-100 to-orange-100 dark:from-yellow-900/30 dark:to-orange-900/30 text-yellow-600 dark:text-yellow-400 hover:from-yellow-200 hover:to-orange-200 dark:hover:from-yellow-800/40 dark:hover:to-orange-800/40 transition-all duration-300 shadow-lg hover:shadow-xl group border border-yellow-200 dark:border-yellow-700" title="تبديل الثيم">
                <i class="theme-icon fas fa-adjust text-lg group-hover:rotate-180 transition-transform duration-500"></i>
            </button>

            <!-- صورة المستخدم المبدعة -->
            <div class="relative">
                <button id="userMenuBtn" class="btn-magical flex items-center p-3 rounded-xl bg-gradient-to-r from-indigo-100 to-blue-100 dark:from-indigo-900/30 dark:to-blue-900/30 text-indigo-600 dark:text-indigo-400 hover:from-indigo-200 hover:to-blue-200 dark:hover:from-indigo-800/40 dark:hover:to-blue-800/40 transition-all duration-300 shadow-lg hover:shadow-xl group border border-indigo-200 dark:border-indigo-700">
                    <div class="relative">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-r from-indigo-500 to-blue-500 flex items-center justify-center text-white font-bold overflow-hidden shadow-lg group-hover:scale-110 transition-transform duration-300 border-2 border-white dark:border-gray-700">
                            @if(Auth::user()->profile_image)
                                <img src="{{ asset('storage/' . Auth::user()->profile_image) }}" alt="{{ Auth::user()->first_name }}" class="w-full h-full object-cover">
                            @else
                                <span class="text-lg">{{ substr(Auth::user()->first_name, 0, 1) }}</span>
                            @endif
                        </div>
                        <!-- حلقة التوهج -->
                        <div class="absolute inset-0 rounded-full border-2 border-indigo-400 dark:border-indigo-500 opacity-0 group-hover:opacity-70 transition-opacity duration-300 animate-pulse"></div>
                        <!-- نقطة الحالة -->
                        <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white dark:border-gray-800 shadow-sm animate-pulse"></div>
                    </div>
                    <div class="mr-3 hidden md:block">
                        <div class="text-right">
                            <span class="font-bold text-gradient-primary text-lg">{{ Auth::user()->first_name }} {{ Auth::user()->last_name }}</span>
                            <div class="flex items-center justify-end mt-1">
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-bold bg-gradient-to-r from-blue-100 to-indigo-100 dark:from-blue-900/30 dark:to-indigo-900/30 text-blue-800 dark:text-blue-300 border border-blue-200 dark:border-blue-700 shadow-sm">
                                    <i class="fas fa-user-tie mr-1"></i>
                                    موظف
                                </span>
                            </div>
                        </div>
                    </div>
                    <i class="fas fa-chevron-down text-sm mr-2 hidden md:block group-hover:rotate-180 transition-transform duration-300 text-indigo-500 dark:text-indigo-400"></i>
                </button>

                <!-- قائمة المستخدم - مخفية افتراضياً -->
                <div id="userMenu" class="absolute left-0 top-full mt-2 w-64 bg-white dark:bg-gray-800 rounded-md shadow-xl py-1 z-50 hidden border border-gray-200 dark:border-gray-700">
                    <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">{{ Auth::user()->first_name }} {{ Auth::user()->last_name }}</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 truncate">{{ Auth::user()->email }}</p>
                    </div>
                    <a href="{{ route('employee.profile') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-user-circle ml-2"></i>الملف الشخصي
                    </a>

                    @if(Auth::user()->user_type == 'admin' || Auth::user()->can('dashboard.admin'))
                    <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                    <div class="px-3 py-1">
                        <span class="text-xs text-gray-500 dark:text-gray-400 font-medium">التنقل السريع</span>
                    </div>
                    <a href="{{ route('admin.dashboard') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-green-50 dark:hover:bg-green-900/20 hover:text-green-600 dark:hover:text-green-400 transition-colors">
                        <i class="fas fa-user-shield ml-2 text-green-500"></i>واجهة المدير
                        <span class="text-xs text-gray-500 dark:text-gray-400 block">العودة لوحة الإدارة</span>
                    </a>
                    @endif
                    <a href="{{ route('employee.settings') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-cog ml-2"></i>الإعدادات
                    </a>
                   
                    <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                    <form action="{{ route('logout') }}" method="POST" class="w-full">
                        @csrf
                        <button type="submit" class="w-full text-right block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i class="fas fa-sign-out-alt ml-2"></i>تسجيل الخروج
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</header>

<style>
    /* تحسين القوائم المنسدلة */
    .dropdown-menu {
        max-height: 400px;
        overflow-y: auto;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    /* التأكد من أن القوائم تظهر فوق كل شيء */
    #cartMenu, #notificationsMenu, #userMenu {
        z-index: 9999 !important;
        position: absolute !important;
        top: 100% !important;
        transform: translateY(0) !important;
        min-width: 200px;
        max-width: 400px;
    }

    /* تحسين الحاوي للقوائم */
    .relative .absolute {
        position: absolute !important;
    }

    /* إصلاح مشكلة الانقطاع */
    header {
        overflow: visible !important;
    }

    header .relative {
        overflow: visible !important;
    }

    /* تحسين الحاوي النسبي للقوائم */
    .relative {
        position: relative !important;
    }
</style>